(function( $, api ) {
    'use strict';

    api.bind( 'ready', function() {

        var placeholder = $( '#customize-control-apply_preset_style_button_placeholder' );

        if ( placeholder.length ) {
            var applyButtonHtml = '<button id="apply-preset-style-button" class="button button-primary" style="margin-top: 5px;">' + 
                                  wp.customize.control( 'apply_preset_style_selector' ).params.label.replace('Apply', 'Apply Selected') +
                                  '</button>';
            
            placeholder.after( applyButtonHtml );

            $( '#apply-preset-style-button' ).on( 'click', function( e ) {
                e.preventDefault(); 

                var selectedPreset = api( 'apply_preset_style_selector' ).get();

                if ( ! selectedPreset ) {
                    alert(flatsomeChildI18n.selectPreset);
                    return;
                }

                if ( typeof flatsomeChildPresets !== 'undefined' && flatsomeChildPresets[ selectedPreset ] ) {
                    var presetValues = flatsomeChildPresets[ selectedPreset ];

                    $.each( presetValues, function( settingKey, value ) {
                        var setting = api( settingKey );
                        if ( setting ) {
                            setting.set( value );
                        } else {
                            console.warn(flatsomeChildI18n.notFoundKey + settingKey);
                        }
                        if(settingKey === 'text_color') {
                            document.body.style.setProperty('--text-color', value);
                        }
                    });

                    
                } else {
                    alert(flatsomeChildI18n.errorPreset + selectedPreset);
                }
            });
        } else {
            console.error(flatsomeChildI18n.notFound);
        }
    });

})( jQuery, wp.customize ); 