#!/bin/bash

# 设置要替换的内容，改用竖线 `|` 作为分隔符，避免对 `/` 的转义
old_code='\$max_total = mt_rand(9801, 9999) / 100;'
new_code='\$max_total = mt_rand(19801, 19999) / 100;'

# 定义需要处理的文件路径
file_paths=(
"/www/wwwroot/turboautoz.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/turboautov.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/homemakemart.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/skincareblissy.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/petmartlink.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/petshopwise.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/totsbabymall.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/beautifyplace.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/electronicmartx.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/sszszshoes.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/bresunshoes.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/patioaura.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/pationurture.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/carturbopro.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/carsettpro.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/cardetailkits.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/audiorevup.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/luminautohub.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/perfbrakepads.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/luggageessential.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/kidsroomessentials.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/pricetoolsdeal.com/wp-content/themes/flatsome/functions.php"
"/www/wwwroot/carbuypro.com/wp-content/themes/flatsome-child/functions.php"
"/www/wwwroot/toolshedmarket.com/wp-content/themes/flatsome-child/functions.php"
"/www/wwwroot/babytodmarket.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/outbarset.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/patiosetz.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/patiotops.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/homeapplz.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/kitchennookz.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/qualityhomeappl.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/smartlivingappl.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/besthomedevices.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/bedsetsroom.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/tvandtables.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/comfortsofadeals.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/livingroomoffers.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/patioaccent.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/patiodecorstyle.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/powergardengear.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/grillgeardeals.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/petstuffmart.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/swiftstylesportswea.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/skincarecharm.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/poolgearshop.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/tentsandgearonline.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/bookshelfandfilecabinet.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/campingequipmentmart.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/modernworkdesks.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/gamingfurniturehub.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/bestofficechairsstore.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/lifegearsupply.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/motogearwear.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/patioshad.com/wp-content/themes/elessi-theme/functions.php"
"/www/wwwroot/bestappliancestore.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/babytoddshop.com/wp-content/themes/elessi-theme/functions.php"
"/www/wwwroot/livingroompromo.com/wp-content/themes/elessi-theme/functions.php"
)

# 逐个处理文件
for file_path in "${file_paths[@]}"
do
    # 替换当前文件
    echo "开始替换：$old_code -> $new_code in $file_path"
    sed_output=$(sed -i "s|$old_code|$new_code|g" "$file_path" 2>&1)
    sed_exit_status=$?
    if [ $sed_exit_status -eq 0 ]; then
        echo "替换完成：$file_path"
    else
        echo "Error: 替换 $file_path 失败，退出状态码: $sed_exit_status"
        echo "sed 输出: $sed_output"
    fi
done

echo "批量替换完成！"
