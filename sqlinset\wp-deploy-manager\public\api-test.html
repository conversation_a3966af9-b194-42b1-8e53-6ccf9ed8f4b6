<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 API路径测试</h1>
    
    <div id="results"></div>
    
    <button onclick="testAPI()">测试API</button>
    <button onclick="clearResults()">清除结果</button>
    
    <script>
        // 直接定义API类，避免依赖外部文件
        class TestAPI {
            constructor() {
                this.baseUrl = '/api';
            }
            
            async request(endpoint, options = {}) {
                const url = `${this.baseUrl}${endpoint}`;
                console.log('请求URL:', url);
                
                try {
                    const response = await fetch(url, {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        ...options
                    });
                    
                    return {
                        ok: response.ok,
                        status: response.status,
                        url: response.url,
                        data: response.ok ? await response.json() : null
                    };
                } catch (error) {
                    return {
                        ok: false,
                        status: 0,
                        url: url,
                        error: error.message
                    };
                }
            }
        }
        
        const testApi = new TestAPI();
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function testAPI() {
            clearResults();
            addResult('🔍 开始API测试...', 'info');
            
            const endpoints = [
                '/',
                '/deploy.php',
                '/templates.php',
                '/status.php',
                '/logs.php',
                '/settings.php'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const result = await testApi.request(endpoint);
                    
                    const message = `
                        <strong>${endpoint}</strong><br>
                        状态: ${result.status}<br>
                        URL: ${result.url}<br>
                        ${result.error ? '错误: ' + result.error : ''}
                    `;
                    
                    const type = result.ok ? 'success' : 'error';
                    addResult(message, type);
                    
                } catch (error) {
                    addResult(`<strong>${endpoint}</strong><br>异常: ${error.message}`, 'error');
                }
            }
            
            addResult('✅ API测试完成', 'info');
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
