/**
 * 部署管理模块
 */

class DeployManager {
    constructor() {
        this.activeJobs = new Map();
        this.eventSources = new Map();
        this.init();
    }

    /**
     * 初始化部署管理
     */
    init() {
        this.loadDeployQueue();
        this.loadDeployHistory();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 筛选按钮
        const filterBtn = document.querySelector('[onclick="filterDeployHistory()"]');
        if (filterBtn) {
            filterBtn.onclick = () => this.filterDeployHistory();
        }
    }

    /**
     * 加载部署队列
     */
    async loadDeployQueue() {
        try {
            const response = await api.getQueueStatus();
            const queueData = response.data;

            this.renderDeployQueue(queueData);

        } catch (error) {
            console.error('Failed to load deploy queue:', error);
            app.showNotification('加载部署队列失败', 'error');
        }
    }

    /**
     * 渲染部署队列
     */
    renderDeployQueue(queueData) {
        const container = document.getElementById('deploy-queue-list');
        if (!container) return;

        const runningJobs = queueData.running_jobs || [];
        const pendingJobs = queueData.pending_jobs || [];

        if (runningJobs.length === 0 && pendingJobs.length === 0) {
            container.innerHTML = '<div class="empty-queue">队列为空</div>';
            return;
        }

        const html = `
            ${runningJobs.length > 0 ? `
                <div class="queue-section">
                    <h4>运行中 (${runningJobs.length})</h4>
                    ${runningJobs.map(job => this.renderQueueItem(job, 'running')).join('')}
                </div>
            ` : ''}
            
            ${pendingJobs.length > 0 ? `
                <div class="queue-section">
                    <h4>待处理 (${pendingJobs.length})</h4>
                    ${pendingJobs.map(job => this.renderQueueItem(job, 'pending')).join('')}
                </div>
            ` : ''}
        `;

        container.innerHTML = html;
    }

    /**
     * 渲染队列项目
     */
    renderQueueItem(job, status) {
        return `
            <div class="queue-item" data-job-id="${job.id}">
                <div class="queue-item-header">
                    <div class="queue-item-title">
                        <span class="job-type">${job.type === 'batch' ? '批量' : '单域名'}</span>
                        <span class="job-domain">${job.domain || '多个域名'}</span>
                    </div>
                    <div class="queue-item-actions">
                        ${status === 'running' ? `
                            <button class="btn btn-sm btn-danger" onclick="deployManager.cancelJob(${job.id})">
                                取消
                            </button>
                        ` : `
                            <button class="btn btn-sm btn-secondary" onclick="deployManager.cancelJob(${job.id})">
                                移除
                            </button>
                        `}
                    </div>
                </div>
                
                <div class="queue-item-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${job.progress || 0}%"></div>
                    </div>
                    <span class="progress-text">${job.progress || 0}%</span>
                </div>
                
                <div class="queue-item-meta">
                    <span class="job-template">${job.template_name || '未知模板'}</span>
                    <span class="job-time">${app.formatDateTime(job.created_at)}</span>
                    ${job.current_step ? `<span class="job-step">${job.current_step}</span>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 加载部署历史
     */
    async loadDeployHistory(filters = {}) {
        try {
            const response = await api.getDeployHistory({
                page: 1,
                limit: 20,
                ...filters
            });
            
            const historyData = response.data;
            this.renderDeployHistory(historyData.jobs || []);
            this.renderPagination(historyData.pagination || {});

        } catch (error) {
            console.error('Failed to load deploy history:', error);
            app.showNotification('加载部署历史失败', 'error');
        }
    }

    /**
     * 渲染部署历史
     */
    renderDeployHistory(jobs) {
        const tbody = document.querySelector('#deploy-history-table tbody');
        if (!tbody) return;

        if (jobs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无部署记录</td></tr>';
            return;
        }

        const html = jobs.map(job => `
            <tr class="job-row" data-job-id="${job.id}">
                <td>
                    <div class="job-domain">
                        ${job.domain || '多个域名'}
                        ${job.type === 'batch' ? '<span class="badge badge-info">批量</span>' : ''}
                    </div>
                </td>
                <td>${job.template_name || '未知'}</td>
                <td>
                    <span class="status-badge status-${job.status}">
                        ${this.getStatusText(job.status)}
                    </span>
                </td>
                <td>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${job.progress || 0}%"></div>
                        </div>
                        <span class="progress-text">${job.progress || 0}%</span>
                    </div>
                </td>
                <td>${app.formatDateTime(job.created_at)}</td>
                <td>${this.formatDuration(job.duration)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="deployManager.viewJobDetails(${job.id})" title="查看详情">
                            <i class="icon-eye"></i>
                        </button>
                        ${job.status === 'failed' ? `
                            <button class="btn btn-sm btn-warning" onclick="deployManager.retryJob(${job.id})" title="重试">
                                <i class="icon-refresh"></i>
                            </button>
                        ` : ''}
                        ${job.log_file ? `
                            <button class="btn btn-sm btn-info" onclick="deployManager.viewLogs(${job.id})" title="查看日志">
                                <i class="icon-file-text"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    /**
     * 渲染分页
     */
    renderPagination(pagination) {
        const container = document.getElementById('deploy-pagination');
        if (!container || !pagination.total_pages) return;

        const currentPage = pagination.current_page || 1;
        const totalPages = pagination.total_pages;

        let html = '<div class="pagination-controls">';
        
        // 上一页
        if (currentPage > 1) {
            html += `<button class="btn btn-sm" onclick="deployManager.loadPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const activeClass = i === currentPage ? 'btn-primary' : 'btn-outline';
            html += `<button class="btn btn-sm ${activeClass}" onclick="deployManager.loadPage(${i})">${i}</button>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            html += `<button class="btn btn-sm" onclick="deployManager.loadPage(${currentPage + 1})">下一页</button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    /**
     * 加载指定页面
     */
    loadPage(page) {
        const filters = this.getCurrentFilters();
        filters.page = page;
        this.loadDeployHistory(filters);
    }

    /**
     * 获取当前筛选条件
     */
    getCurrentFilters() {
        return {
            status: document.getElementById('status-filter')?.value || '',
            domain: document.getElementById('domain-filter')?.value || ''
        };
    }

    /**
     * 筛选部署历史
     */
    filterDeployHistory() {
        const filters = this.getCurrentFilters();
        this.loadDeployHistory(filters);
    }

    /**
     * 取消任务
     */
    async cancelJob(jobId) {
        if (!confirm('确定要取消这个任务吗？')) return;

        try {
            await api.cancelJob(jobId);
            app.showNotification('任务已取消', 'success');
            this.loadDeployQueue();
            this.loadDeployHistory();
        } catch (error) {
            console.error('Failed to cancel job:', error);
            app.showNotification('取消任务失败：' + error.message, 'error');
        }
    }

    /**
     * 重试任务
     */
    async retryJob(jobId) {
        if (!confirm('确定要重试这个任务吗？')) return;

        try {
            await api.retryJob(jobId);
            app.showNotification('任务已重新加入队列', 'success');
            this.loadDeployQueue();
            this.loadDeployHistory();
        } catch (error) {
            console.error('Failed to retry job:', error);
            app.showNotification('重试任务失败：' + error.message, 'error');
        }
    }

    /**
     * 查看任务详情
     */
    async viewJobDetails(jobId) {
        try {
            const response = await api.getJobStatus(jobId);
            const job = response.data;
            
            this.showJobDetailsModal(job);
        } catch (error) {
            console.error('Failed to load job details:', error);
            app.showNotification('加载任务详情失败', 'error');
        }
    }

    /**
     * 显示任务详情模态框
     */
    showJobDetailsModal(job) {
        // 实现任务详情模态框
        console.log('Job details:', job);
        app.showNotification('任务详情功能开发中', 'info');
    }

    /**
     * 查看日志
     */
    async viewLogs(jobId) {
        try {
            const response = await api.getJobLogs(jobId);
            const logs = response.data;
            
            this.showLogsModal(logs);
        } catch (error) {
            console.error('Failed to load job logs:', error);
            app.showNotification('加载日志失败', 'error');
        }
    }

    /**
     * 显示日志模态框
     */
    showLogsModal(logs) {
        // 实现日志模态框
        console.log('Job logs:', logs);
        app.showNotification('日志查看功能开发中', 'info');
    }

    /**
     * 启动实时更新
     */
    startRealTimeUpdates() {
        // 每10秒刷新队列状态
        setInterval(() => {
            if (!document.hidden) {
                this.loadDeployQueue();
            }
        }, 10000);
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const texts = {
            'completed': '已完成',
            'failed': '失败',
            'running': '运行中',
            'pending': '待处理',
            'cancelled': '已取消'
        };
        return texts[status] || status;
    }

    /**
     * 格式化持续时间
     */
    formatDuration(seconds) {
        if (!seconds) return '-';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
}

// 全局函数
function showSingleDeployModal() {
    app.showNotification('单域名部署功能开发中', 'info');
}

function showBatchDeployModal() {
    app.showNotification('批量部署功能开发中', 'info');
}

function toggleQueue() {
    app.showNotification('队列控制功能开发中', 'info');
}

function clearQueue() {
    if (confirm('确定要清空队列吗？这将取消所有待处理的任务。')) {
        app.showNotification('清空队列功能开发中', 'info');
    }
}

function filterDeployHistory() {
    if (window.deployManager) {
        window.deployManager.filterDeployHistory();
    }
}

// 创建全局实例
window.deployManager = new DeployManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DeployManager;
}
