#!/bin/bash

# 修复API路由问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  修复API路由问题"
echo "==================================================${NC}"

# 方案1: 复制API文件到public/api目录
echo -e "${BLUE}[1/3]${NC} 复制API文件到public/api目录..."

# 复制所有API文件
cp api/*.php public/api/

echo "已复制的API文件:"
ls -la public/api/*.php

# 方案2: 创建符号链接（备用方案）
echo -e "${BLUE}[2/3]${NC} 创建备用符号链接..."

# 如果复制方案不工作，可以尝试符号链接
# ln -sf ../../api/*.php public/api/ 2>/dev/null || echo "符号链接创建失败，使用复制方案"

# 设置权限
echo -e "${BLUE}[3/3]${NC} 设置文件权限..."
chown -R www:www public/api/
chmod 644 public/api/*.php

echo -e "${GREEN}=================================================="
echo "  ✅ API路由修复完成！"
echo "=================================================="
echo "修复方案: 直接复制API文件到public/api目录"
echo ""
echo "现在测试API端点:"
echo "curl https://wpd.cloudcheckout.shop/api/deploy.php"
echo "==================================================${NC}"

# 测试API端点
echo -e "${YELLOW}测试API端点...${NC}"

DOMAIN="wpd.cloudcheckout.shop"
API_ENDPOINTS=("deploy.php" "templates.php" "status.php" "logs.php" "settings.php")

for endpoint in "${API_ENDPOINTS[@]}"; do
    echo "测试 https://$DOMAIN/api/$endpoint"
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/$endpoint" --max-time 10 2>/dev/null || echo "000")
    
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "400" ] || [ "$HTTP_CODE" = "500" ]; then
        echo "✓ $endpoint 可访问 (HTTP $HTTP_CODE)"
    else
        echo "✗ $endpoint 不可访问 (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo -e "${GREEN}修复完成！请刷新浏览器测试网站。${NC}"

exit 0
