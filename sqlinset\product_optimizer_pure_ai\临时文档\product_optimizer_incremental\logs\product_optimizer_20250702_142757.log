2025-07-02 14:27:57 | INFO     | MainThread | 日志系统初始化完成，日志文件: /www/imagerdown/sqlinset/product_optimizer_incremental/logs/product_optimizer_20250702_142757.log
2025-07-02 14:27:57 | INFO     | MainThread | 程序启动
2025-07-02 14:27:57 | INFO     | MainThread | 选择性能模式: balanced
2025-07-02 14:27:57 | INFO     | MainThread | 目录检查完成
2025-07-02 14:27:57 | INFO     | MainThread | 发现初始文件: 1 个
2025-07-02 14:27:57 | INFO     | MainThread | 开始处理 1 个初始文件
2025-07-02 14:27:57 | INFO     | Thread-1 (file_monitor_thread) | 启动文件监控线程
2025-07-02 14:27:57 | INFO     | MainThread | 文件监控线程已启动
2025-07-02 14:27:57 | INFO     | Thread-2 (monitor_performance) | 启动性能监控线程
2025-07-02 14:27:57 | INFO     | MainThread | 性能监控线程已启动
2025-07-02 14:27:57 | INFO     | MainThread | 开始处理初始文件 1/1: home1
2025-07-02 14:27:57 | INFO     | MainThread | 初始化顺序保持器，确保输出顺序正确
2025-07-02 14:27:57 | INFO     | MainThread | 开始处理文件: home1
2025-07-02 14:27:57 | INFO     | MainThread | 文件读取成功: 99 个产品
2025-07-02 14:27:57 | INFO     | MainThread | 准备处理: 4 个批次，8 个线程
2025-07-02 14:27:57 | INFO     | MainThread | 开始并发处理批次
2025-07-02 14:27:57 | INFO     | ThreadPoolExecutor-0_0 | 开始处理批次 1: 25 个产品
2025-07-02 14:27:57 | INFO     | ThreadPoolExecutor-0_1 | 开始处理批次 2: 25 个产品
2025-07-02 14:27:57 | INFO     | ThreadPoolExecutor-0_2 | 开始处理批次 3: 25 个产品
2025-07-02 14:27:57 | INFO     | ThreadPoolExecutor-0_3 | 开始处理批次 4: 24 个产品
2025-07-02 14:28:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:28:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:29:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:29:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:30:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:30:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:31:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:31:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:32:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:32:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:33:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:33:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:34:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:34:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:35:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:35:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:36:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:36:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:37:57 | ERROR    | ThreadPoolExecutor-0_0 | 批次 1 未知错误 (尝试 1): Expecting value: line 10 column 1 (char 9)
Traceback (most recent call last):
  File "/www/imagerdown/sqlinset/product_optimizer_incremental/product_optimizer_incremental.py", line 343, in ai_optimize_products
    result = response.json()
  File "/usr/lib/python3/dist-packages/requests/models.py", line 900, in json
    return complexjson.loads(self.text, **kwargs)
  File "/usr/lib/python3/dist-packages/simplejson/__init__.py", line 525, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 370, in decode
    obj, end = self.raw_decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 400, in raw_decode
    return self.scan_once(s, idx=_w(s, idx).end())
simplejson.errors.JSONDecodeError: Expecting value: line 10 column 1 (char 9)
2025-07-02 14:37:57 | ERROR    | ThreadPoolExecutor-0_2 | 批次 3 未知错误 (尝试 1): Expecting value: line 10 column 1 (char 9)
Traceback (most recent call last):
  File "/www/imagerdown/sqlinset/product_optimizer_incremental/product_optimizer_incremental.py", line 343, in ai_optimize_products
    result = response.json()
  File "/usr/lib/python3/dist-packages/requests/models.py", line 900, in json
    return complexjson.loads(self.text, **kwargs)
  File "/usr/lib/python3/dist-packages/simplejson/__init__.py", line 525, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 370, in decode
    obj, end = self.raw_decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 400, in raw_decode
    return self.scan_once(s, idx=_w(s, idx).end())
simplejson.errors.JSONDecodeError: Expecting value: line 10 column 1 (char 9)
2025-07-02 14:37:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:37:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:37:57 | ERROR    | ThreadPoolExecutor-0_1 | 批次 2 未知错误 (尝试 1): Expecting value: line 10 column 1 (char 9)
Traceback (most recent call last):
  File "/www/imagerdown/sqlinset/product_optimizer_incremental/product_optimizer_incremental.py", line 343, in ai_optimize_products
    result = response.json()
  File "/usr/lib/python3/dist-packages/requests/models.py", line 900, in json
    return complexjson.loads(self.text, **kwargs)
  File "/usr/lib/python3/dist-packages/simplejson/__init__.py", line 525, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 370, in decode
    obj, end = self.raw_decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 400, in raw_decode
    return self.scan_once(s, idx=_w(s, idx).end())
simplejson.errors.JSONDecodeError: Expecting value: line 10 column 1 (char 9)
2025-07-02 14:37:57 | ERROR    | ThreadPoolExecutor-0_3 | 批次 4 未知错误 (尝试 1): Expecting value: line 10 column 1 (char 9)
Traceback (most recent call last):
  File "/www/imagerdown/sqlinset/product_optimizer_incremental/product_optimizer_incremental.py", line 343, in ai_optimize_products
    result = response.json()
  File "/usr/lib/python3/dist-packages/requests/models.py", line 900, in json
    return complexjson.loads(self.text, **kwargs)
  File "/usr/lib/python3/dist-packages/simplejson/__init__.py", line 525, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 370, in decode
    obj, end = self.raw_decode(s)
  File "/usr/lib/python3/dist-packages/simplejson/decoder.py", line 400, in raw_decode
    return self.scan_once(s, idx=_w(s, idx).end())
simplejson.errors.JSONDecodeError: Expecting value: line 10 column 1 (char 9)
2025-07-02 14:38:33 | WARNING  | MainThread | 接收到终止信号 (15)，开始优雅关闭流程
2025-07-02 14:38:33 | INFO     | MainThread | 优雅关闭完成，程序退出
2025-07-02 14:38:37 | INFO     | Thread-1 (file_monitor_thread) | 文件监控线程结束
2025-07-02 14:38:57 | INFO     | Thread-2 (monitor_performance) | 性能监控: 总速度 0.0 产品/分钟, 最近 0 产品/分钟
2025-07-02 14:38:57 | WARNING  | Thread-2 (monitor_performance) | 性能警告: 处理速度较慢，可能遇到API限制
2025-07-02 14:38:57 | INFO     | Thread-2 (monitor_performance) | 性能监控线程结束
2025-07-02 14:41:59 | INFO     | ThreadPoolExecutor-0_3 | 批次 4 处理成功: 24 个产品
2025-07-02 14:42:01 | INFO     | ThreadPoolExecutor-0_0 | 批次 1 处理成功: 25 个产品
2025-07-02 14:42:05 | INFO     | ThreadPoolExecutor-0_2 | 批次 3 处理成功: 25 个产品
2025-07-02 14:42:09 | INFO     | ThreadPoolExecutor-0_1 | 批次 2 处理成功: 25 个产品
