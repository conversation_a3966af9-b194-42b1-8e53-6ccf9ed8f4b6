# 🤖 Gemini版自动分片处理功能

## 📋 功能概述

新增的自动分片功能可以智能处理大文件，无需手动分割，脚本会自动将大文件分成小块逐个处理。

## 🔧 自动分片机制

### **触发条件**:
```
文件大小 >= 5,000行: 显示大文件提示
文件大小 >= 10,000行: 提供分片选项
自动模式 >= 10,000行: 自动启用分片
```

### **分片配置**:
```python
分片大小: 3,000行/片
处理方式: 逐片顺序处理
临时存储: 自动创建temp_chunks目录
最终合并: 自动合并所有结果
```

## 🚀 使用方法

### **1. 交互模式 - 手动选择**
```bash
python3 product_optimizer_gemini.py

# 当文件 >= 10,000行时会提示:
🤖 大文件处理选项:
1. 自动分片处理 (推荐) - 分成小块逐个处理
2. 整体处理 - 一次性处理全部数据
请选择处理方式 (1/2): 1
```

### **2. 自动模式 - 智能分片**
```bash
python3 product_optimizer_gemini.py auto

# 文件 >= 10,000行时自动启用分片:
🤖 自动启用分片处理 (20,000 行)
🔄 启动自动分片处理:
   总产品数: 20,000
   分片大小: 3,000 产品/片
   分片数量: 7 片
   预估总时间: 33.3 小时
```

## 📊 分片处理优势

### **vs 手动分割**:
| 特性 | 手动分割 | 自动分片 |
|------|----------|----------|
| **操作复杂度** | 需要手动split命令 | 完全自动 |
| **文件管理** | 需要管理多个文件 | 脚本自动管理 |
| **结果合并** | 需要手动合并 | 自动合并 |
| **进度跟踪** | 难以跟踪总进度 | 实时显示总进度 |
| **错误恢复** | 需要手动重新处理 | 自动保存中间结果 |

### **vs 整体处理**:
| 特性 | 整体处理 | 自动分片 |
|------|----------|----------|
| **内存占用** | 高 (加载全部数据) | 低 (分片加载) |
| **中断恢复** | 从头开始 | 从中断点继续 |
| **稳定性** | 长时间运行风险高 | 分片降低风险 |
| **进度可见性** | 难以估算 | 清晰的分片进度 |

## 📈 2万行文件处理示例

### **自动分片处理流程**:
```
📁 输入: products_20k.txt (20,000行)

🔄 自动分片:
├── 分片1: 1-3,000行 (约5小时)
├── 分片2: 3,001-6,000行 (约5小时)  
├── 分片3: 6,001-9,000行 (约5小时)
├── 分片4: 9,001-12,000行 (约5小时)
├── 分片5: 12,001-15,000行 (约5小时)
├── 分片6: 15,001-18,000行 (约5小时)
└── 分片7: 18,001-20,000行 (约3.3小时)

📄 输出: products_20k_gemini.txt (完整结果)
```

### **处理时间对比**:
```
整体处理: 33小时连续运行 (高风险)
分片处理: 7个分片 × 5小时 = 35小时 (可中断)
```

## 🔍 分片处理详细流程

### **1. 文件分析阶段**:
```
🔍 检测到大文件 (20,000 行)
⏱️ 预估处理时间: 2000.0 分钟 (33.3 小时)

🤖 大文件处理选项:
1. 自动分片处理 (推荐) - 分成小块逐个处理
2. 整体处理 - 一次性处理全部数据
请选择处理方式 (1/2): 1
```

### **2. 分片处理阶段**:
```
🔄 启动自动分片处理:
   总产品数: 20,000
   分片大小: 3,000 产品/片
   分片数量: 7 片
   预估总时间: 33.3 小时

============================================================
🔄 处理分片 1/7
   产品范围: 1-3000
   产品数量: 3000
============================================================
   批次数量: 375
✅ 分片 1 处理完成
📊 总体进度: 14.3% (3000/20000 产品)
```

### **3. 结果合并阶段**:
```
🎉 分片处理完成!
📊 成功分片: 7/7
📊 处理产品: 20000/20000
📁 输出文件: products_20k_gemini.txt
```

## 💡 最佳实践

### **推荐的文件大小策略**:
```
< 5,000行: 直接处理 (无分片)
5,000-10,000行: 可选分片
10,000-50,000行: 推荐分片 (自动)
> 50,000行: 强制分片
```

### **分片大小优化**:
```
当前默认: 3,000行/片
小文件: 可以增加到5,000行/片
大文件: 建议保持3,000行/片
超大文件: 可以减少到2,000行/片
```

## 🔧 高级配置

### **自定义分片大小** (需要修改代码):
```python
# 在 process_file_in_chunks 函数调用处修改
chunk_size = 2000  # 改为2000行/片 (更保守)
chunk_size = 5000  # 改为5000行/片 (更激进)
```

### **分片处理监控**:
```bash
# 查看处理进度
tail -f gemini_processing.log

# 查看临时文件
ls -la output/temp_chunks/

# 监控系统资源
htop
```

## 🛡️ 错误恢复机制

### **中断恢复**:
```
✅ 自动保存: 每个分片完成后保存中间结果
✅ 部分结果: 中断时保存已处理的产品
✅ 临时文件: 保存在temp_chunks目录
✅ 手动恢复: 可以从临时文件手动合并
```

### **失败处理**:
```
分片失败: 继续处理下一个分片
网络中断: 自动重试机制
API限制: 自动延长等待时间
系统错误: 保存已处理结果
```

## 📊 性能对比总结

### **2万行文件处理对比**:
| 方式 | 操作复杂度 | 处理时间 | 稳定性 | 可控性 |
|------|------------|----------|--------|--------|
| **手动分割** | 复杂 | 33小时 | 中等 | 高 |
| **整体处理** | 简单 | 33小时 | 低 | 低 |
| **自动分片** | 简单 | 35小时 | 高 | 高 |

### **推荐方案**:
```
🎯 最佳选择: 自动分片处理
✅ 操作简单: 一键启动
✅ 稳定可靠: 分片降低风险
✅ 进度可控: 实时显示进度
✅ 错误恢复: 自动保存中间结果
```

**总结: 现在您可以直接上传大文件，脚本会自动处理分片，无需手动分割！**
