#!/bin/bash

# 设置文件夹路径
WEBP_FOLDER="/www/imagerdown/imagedownload"
WEBP_OUTPUT_FOLDER="/www/imagerdown/imagewebp"

# 创建目标文件夹，如果不存在
mkdir -p "$WEBP_OUTPUT_FOLDER"

# 定义调整图片尺寸并转换为 WebP 格式的函数
resize_and_convert_image() {
    local image="$1"
    local webp_image="$WEBP_OUTPUT_FOLDER/$(basename "${image%.*}.webp")"

    # 获取图片的当前尺寸
    local image_size=$(identify -format "%wx%h" "$image")
    local width=$(echo "$image_size" | cut -dx -f1)
    local height=$(echo "$image_size" | cut -dx -f2)

    # 判断图片尺寸是否大于 1000x1000
    if [[ "$width" -le 1000 && "$height" -le 1000 ]]; then
        # 图片尺寸小于等于 1000x1000，直接转换为 WebP
        echo "Image $(basename "$image") is already smaller than or equal to 1000x1000. Converting to WebP..."
        convert "$image" "$webp_image"
    else
        # 图片尺寸大于 1000x1000，调整大小后转换为 WebP
        echo "Resizing image $(basename "$image") to 1000x1000 and converting to WebP..."
        convert "$image" -resize 1000x1000 "$webp_image"
    fi
    echo "Saved as WebP: $webp_image"
}

export -f resize_and_convert_image
export WEBP_OUTPUT_FOLDER

# 使用 GNU Parallel 实现多线程
find "$WEBP_FOLDER" \( -name "*.webp" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) | parallel -j 8 resize_and_convert_image
