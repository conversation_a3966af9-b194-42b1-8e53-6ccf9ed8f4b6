# 🚀 WordPress Deploy Manager - 项目概览

## 📋 项目简介

WordPress Deploy Manager 是一个强大的WordPress站点批量部署和管理系统，提供了完整的本地开发环境和生产部署解决方案。

## ✨ 主要特性

### 🎯 核心功能
- **批量部署**: 支持单域名和批量域名WordPress站点部署
- **模板管理**: 上传、管理和使用WordPress模板文件
- **实时监控**: 系统资源监控和健康状态检查
- **日志管理**: 完整的操作日志和错误追踪
- **设置管理**: 灵活的系统配置和参数调整

### 🛠️ 技术特性
- **容器化部署**: 基于Docker的完整开发环境
- **RESTful API**: 标准化的API接口设计
- **响应式界面**: 现代化的Web管理界面
- **数据库支持**: MySQL数据库存储和管理
- **自动化测试**: 完整的功能测试套件

## 🏗️ 系统架构

```
WordPress Deploy Manager
├── 前端界面 (HTML/CSS/JavaScript)
│   ├── 仪表板 - 系统概览和统计
│   ├── 部署管理 - 单域名/批量部署
│   ├── 模板管理 - 模板上传和管理
│   ├── 监控中心 - 系统监控和状态
│   ├── 日志管理 - 日志查看和分析
│   └── 系统设置 - 配置管理
├── API接口 (PHP)
│   ├── 部署API - 处理部署请求
│   ├── 模板API - 模板文件管理
│   ├── 状态API - 系统状态查询
│   ├── 日志API - 日志数据获取
│   └── 设置API - 配置数据管理
├── 数据库 (MySQL)
│   ├── 模板表 - 存储模板信息
│   ├── 部署任务表 - 记录部署任务
│   ├── 日志表 - 存储操作日志
│   └── 设置表 - 保存系统配置
└── Docker环境
    ├── Web容器 - Apache + PHP
    ├── MySQL容器 - 数据库服务
    └── phpMyAdmin - 数据库管理
```

## 📁 项目结构

```
wp-deploy-manager/
├── api/                    # API端点文件
│   ├── deploy.php         # 部署管理API
│   ├── templates.php      # 模板管理API
│   ├── status.php         # 状态查询API
│   ├── logs.php           # 日志管理API
│   └── settings.php       # 设置管理API
├── config/                 # 配置文件
│   ├── config.php         # 主配置文件
│   ├── database.php       # 数据库配置
│   └── app.json           # 应用配置
├── database/               # 数据库相关
│   └── init_mysql.sql     # 数据库初始化脚本
├── docker/                 # Docker配置
│   ├── Dockerfile         # Web容器构建文件
│   └── apache/            # Apache配置
├── logs/                   # 日志文件目录
├── public/                 # Web根目录
│   ├── api/               # API路由
│   ├── assets/            # 静态资源
│   │   ├── css/           # 样式文件
│   │   ├── js/            # JavaScript文件
│   │   └── images/        # 图片资源
│   └── index.html         # 主页面
├── uploads/                # 上传文件目录
│   ├── templates/         # 模板文件存储
│   └── temp/              # 临时文件
├── docker-compose.yml      # Docker编排文件
├── install.sh             # 一键安装脚本
├── manage.sh              # 管理脚本
└── README.md              # 项目说明
```

## 🚀 快速开始

### 一键安装
```bash
# 下载安装脚本
curl -O https://raw.githubusercontent.com/your-repo/wp-deploy-manager/main/install.sh

# 运行安装
chmod +x install.sh
./install.sh
```

### 手动安装
```bash
# 1. 克隆项目
git clone https://github.com/your-repo/wp-deploy-manager.git
cd wp-deploy-manager

# 2. 运行安装脚本
chmod +x install.sh
./install.sh

# 3. 访问应用
open http://localhost
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost | WordPress Deploy Manager 主界面 |
| API接口 | http://localhost/api/ | RESTful API 接口 |
| phpMyAdmin | http://localhost:8080 | 数据库管理界面 |

## 🔑 默认配置

### 数据库配置
```
主机: localhost:3306
数据库: wp_deploy_manager
用户: wp_deploy
密码: wp_deploy_pass_2024
Root密码: wp_deploy_2024
```

### 系统配置
```
最大并发任务: 3
默认超时时间: 1800秒
调试模式: 开启
日志级别: DEBUG
```

## 🛠️ 开发指南

### 环境要求
- Docker 20.10+
- Docker Compose 1.29+
- 4GB+ 内存
- 10GB+ 磁盘空间

### 开发流程
1. **环境搭建**: 运行 `./install.sh` 创建开发环境
2. **代码修改**: 修改相应的PHP、HTML、CSS、JS文件
3. **测试验证**: 运行 `./test_installation.sh` 进行功能测试
4. **服务重启**: 使用 `./manage.sh restart` 重启服务
5. **日志查看**: 使用 `./manage.sh logs` 查看运行日志

### API开发
```php
// 添加新的API端点
// 1. 在 api/ 目录创建新的PHP文件
// 2. 在 public/api/index.php 中添加路由
// 3. 在前端 assets/js/api.js 中添加对应方法
```

### 前端开发
```javascript
// 添加新的功能模块
// 1. 在 public/assets/js/ 中创建新的JS文件
// 2. 在 public/index.html 中添加对应的HTML结构
// 3. 在 public/assets/css/main.css 中添加样式
```

## 🧪 测试指南

### 功能测试
```bash
# 运行完整测试套件
./test_installation.sh

# 测试特定功能
curl http://localhost/api/templates.php?action=list
curl http://localhost/api/status.php?action=system
```

### 性能测试
```bash
# 压力测试
ab -n 1000 -c 10 http://localhost/api/

# 内存使用监控
docker stats wp-deploy-web
```

## 📊 监控和维护

### 日志管理
```bash
# 查看应用日志
tail -f logs/system_*.log

# 查看Docker日志
./manage.sh logs

# 查看Apache日志
docker-compose exec web tail -f /var/log/apache2/error.log
```

### 数据备份
```bash
# 备份数据库
./manage.sh backup

# 手动备份
docker-compose exec mysql mysqldump -u root -pwp_deploy_2024 wp_deploy_manager > backup.sql
```

### 性能优化
```bash
# 清理Docker缓存
docker system prune -f

# 优化数据库
docker-compose exec mysql mysql -u root -pwp_deploy_2024 -e "OPTIMIZE TABLE wp_deploy_manager.templates;"
```

## 🚀 生产部署

### 部署准备
1. **环境配置**: 修改生产环境配置文件
2. **安全设置**: 更改默认密码和密钥
3. **性能优化**: 启用缓存和压缩
4. **监控配置**: 设置日志和监控

### 部署步骤
```bash
# 1. 创建生产配置
cp config/config.php config/config.production.php

# 2. 修改生产配置
sed -i "s/development/production/" config/config.production.php
sed -i "s/DEBUG_MODE', true/DEBUG_MODE', false/" config/config.production.php

# 3. 部署到服务器
rsync -avz --exclude='logs/*' --exclude='uploads/*' ./ user@server:/path/to/deployment/

# 4. 启动生产服务
ssh user@server "cd /path/to/deployment && docker-compose -f docker-compose.prod.yml up -d"
```

## 🤝 贡献指南

### 开发规范
- **代码风格**: 遵循PSR-12 PHP编码标准
- **提交规范**: 使用语义化提交信息
- **测试要求**: 新功能必须包含测试用例
- **文档更新**: 更新相关文档和注释

### 提交流程
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

## 📞 支持和帮助

### 常见问题
- **端口冲突**: 修改docker-compose.yml中的端口映射
- **权限问题**: 检查文件和目录权限设置
- **服务异常**: 查看Docker日志和应用日志

### 获取帮助
- **文档**: 查看项目README和代码注释
- **日志**: 检查logs目录中的日志文件
- **测试**: 运行测试脚本进行诊断
- **社区**: 提交Issue或参与讨论

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

---

🎉 **感谢使用WordPress Deploy Manager！** 

如果您觉得这个项目有用，请给我们一个⭐️星标支持！
