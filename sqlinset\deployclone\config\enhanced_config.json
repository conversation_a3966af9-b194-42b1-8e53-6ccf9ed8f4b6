{"global": {"mother_domain": "astra.nestlyalli.shop", "mysql_root_user": "root", "mysql_root_pass": "${MYSQL_ROOT_PASS}", "wp_cli_path": "/usr/local/bin/wp", "web_user": "www", "backup_retention_days": 30}, "bt_panel": {"url": "${BT_PANEL_URL}", "api_key": "${BT_API_KEY}", "php_versions": ["74", "80", "81", "82"], "default_php": "81", "auto_ssl": true, "auto_backup": true}, "templates": {"base": {"wordpress_version": "latest", "essential_plugins": ["yoast-seo", "wordfence", "wp-super-cache", "contact-form-7"], "security_plugins": ["wordfence", "limit-login-attempts-reloaded"]}, "industry_templates": {"ecommerce": {"theme": "storefront", "plugins": ["woocommerce", "woocommerce-payments", "mailchimp-for-woocommerce"], "pages": ["shop", "cart", "checkout", "my-account"], "content_type": "product_focused"}, "corporate": {"theme": "astra", "plugins": ["elementor", "contact-form-7", "google-analytics-for-wordpress"], "pages": ["about", "services", "contact", "portfolio"], "content_type": "business_focused"}, "blog": {"theme": "generatepress", "plugins": ["yoast-seo", "social-warfare", "wp-fastest-cache"], "pages": ["blog", "about", "contact"], "content_type": "content_focused"}}}, "content_generation": {"seo": {"auto_generate_meta": true, "keyword_density": 2.5, "meta_description_length": 160, "title_length": 60}, "localization": {"languages": ["en", "de", "fr", "es"], "currency": ["USD", "EUR", "GBP"], "timezone_auto": true}, "personalization": {"company_name_pattern": "{{domain_base}} {{industry_suffix}}", "contact_email_pattern": "info@{{domain}}", "phone_pattern": "******-{{random_4_digits}}"}}, "deployment": {"parallel_limit": 3, "retry_attempts": 3, "timeout_seconds": 300, "post_deployment_checks": ["http_status", "ssl_certificate", "wp_admin_access", "database_connection"]}, "monitoring": {"health_check_interval": 300, "performance_metrics": true, "uptime_monitoring": true, "ssl_expiry_alerts": true, "disk_space_alerts": true}}