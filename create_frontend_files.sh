#!/bin/bash

# 创建前端文件脚本

echo "🎨 创建前端文件..."

# 创建主HTML文件
cat > public/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/images/logo.png" alt="Logo" class="nav-logo">
                <span class="nav-title">WordPress 部署管理系统</span>
            </div>
            
            <div class="nav-menu">
                <div class="nav-item active" data-tab="dashboard">
                    <i class="icon-dashboard"></i>
                    <span>仪表板</span>
                </div>
                <div class="nav-item" data-tab="deploy">
                    <i class="icon-deploy"></i>
                    <span>部署管理</span>
                </div>
                <div class="nav-item" data-tab="templates">
                    <i class="icon-template"></i>
                    <span>模板管理</span>
                </div>
                <div class="nav-item" data-tab="monitoring">
                    <i class="icon-monitor"></i>
                    <span>监控中心</span>
                </div>
                <div class="nav-item" data-tab="logs">
                    <i class="icon-logs"></i>
                    <span>日志管理</span>
                </div>
                <div class="nav-item" data-tab="settings">
                    <i class="icon-settings"></i>
                    <span>系统设置</span>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="btn btn-icon" id="refresh-btn" title="刷新">
                    <i class="icon-refresh"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 仪表板 -->
        <section id="dashboard-tab" class="tab-content active">
            <div class="page-header">
                <h1>系统仪表板</h1>
                <p>WordPress部署管理系统概览</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-sites">0</div>
                        <div class="stat-label">总站点数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number" id="successful-deploys">0</div>
                        <div class="stat-label">成功部署</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pending-tasks">0</div>
                        <div class="stat-label">待处理任务</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-number" id="active-templates">0</div>
                        <div class="stat-label">活跃模板</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3>系统状态</h3>
                    <div id="system-status">
                        <div class="status-item">
                            <span class="status-label">服务状态</span>
                            <span class="status-value status-running">运行中</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">数据库</span>
                            <span class="status-value status-connected">已连接</span>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3>最近活动</h3>
                    <div id="recent-activity">
                        <div class="activity-item">
                            <span class="activity-time">刚刚</span>
                            <span class="activity-text">系统启动完成</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 部署管理 -->
        <section id="deploy-tab" class="tab-content">
            <div class="page-header">
                <h1>部署管理</h1>
                <p>管理WordPress站点部署</p>
            </div>
            
            <div class="deploy-actions">
                <button class="btn btn-primary" id="single-deploy-btn">单域名部署</button>
                <button class="btn btn-secondary" id="batch-deploy-btn">批量部署</button>
            </div>
            
            <div class="deploy-history">
                <h3>部署历史</h3>
                <div id="deploy-history-list">
                    <p>暂无部署记录</p>
                </div>
            </div>
        </section>

        <!-- 模板管理 -->
        <section id="templates-tab" class="tab-content">
            <div class="page-header">
                <h1>模板管理</h1>
                <p>管理WordPress模板文件</p>
            </div>
            
            <div class="template-actions">
                <button class="btn btn-primary" id="upload-template-btn">上传模板</button>
            </div>
            
            <div class="templates-list">
                <div id="templates-container">
                    <p>正在加载模板...</p>
                </div>
            </div>
        </section>

        <!-- 监控中心 -->
        <section id="monitoring-tab" class="tab-content">
            <div class="page-header">
                <h1>监控中心</h1>
                <p>系统性能和健康状态监控</p>
            </div>
            
            <div class="monitoring-grid">
                <div class="monitor-card">
                    <h3>系统资源</h3>
                    <div id="system-resources">
                        <div class="resource-item">
                            <span>CPU使用率</span>
                            <span id="cpu-usage">0%</span>
                        </div>
                        <div class="resource-item">
                            <span>内存使用率</span>
                            <span id="memory-usage">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 日志管理 -->
        <section id="logs-tab" class="tab-content">
            <div class="page-header">
                <h1>日志管理</h1>
                <p>查看系统日志和错误信息</p>
            </div>
            
            <div class="logs-container">
                <div id="logs-content">
                    <p>正在加载日志...</p>
                </div>
            </div>
        </section>

        <!-- 系统设置 -->
        <section id="settings-tab" class="tab-content">
            <div class="page-header">
                <h1>系统设置</h1>
                <p>配置系统参数和选项</p>
            </div>
            
            <div class="settings-form">
                <div class="form-group">
                    <label>最大并发任务数</label>
                    <input type="number" id="max-concurrent-jobs" value="3" min="1" max="10">
                </div>
                <div class="form-group">
                    <label>默认超时时间（秒）</label>
                    <input type="number" id="default-timeout" value="1800" min="300" max="3600">
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" id="save-settings-btn">保存设置</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/api.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
EOF

echo "✅ index.html 创建完成"

# 创建CSS文件
cat > public/assets/css/main.css << 'EOF'
/* WordPress Deploy Manager - 主样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 导航栏 */
.navbar {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
}

.nav-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.nav-menu {
    display: flex;
    gap: 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #333;
}

.nav-item.active {
    background: #007bff;
    color: white;
}

.nav-actions {
    display: flex;
    gap: 10px;
}

/* 主内容区 */
.main-content {
    margin-top: 60px;
    padding: 20px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 8px;
}

.page-header p {
    color: #666;
    font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.dashboard-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

/* 状态项 */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-value {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-running {
    background: #d4edda;
    color: #155724;
}

.status-connected {
    background: #d1ecf1;
    color: #0c5460;
}

/* 活动项 */
.activity-item {
    display: flex;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #666;
    font-size: 12px;
    min-width: 60px;
}

/* 按钮 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-icon {
    padding: 8px;
    background: transparent;
    border: 1px solid #ddd;
}

.btn-icon:hover {
    background: #f8f9fa;
}

/* 表单 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-actions {
    margin-top: 20px;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .main-content {
        padding: 15px;
    }
}

/* 图标字体（简单实现） */
.icon-dashboard::before { content: "📊"; }
.icon-deploy::before { content: "🚀"; }
.icon-template::before { content: "📋"; }
.icon-monitor::before { content: "📈"; }
.icon-logs::before { content: "📝"; }
.icon-settings::before { content: "⚙️"; }
.icon-refresh::before { content: "🔄"; }
EOF

echo "✅ main.css 创建完成"

echo "🎨 前端文件创建完成！"
