<div class="welcome-content">
    <div class="welcome-icon">
        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
        </svg>
    </div>
    
    <h2>欢迎使用 WordPress 部署管理系统</h2>
    
    <p class="welcome-description">
        这是一个强大的WordPress站点批量部署管理系统，可以帮助您：
    </p>
    
    <div class="features-grid">
        <div class="feature-item">
            <div class="feature-icon">🚀</div>
            <h4>快速部署</h4>
            <p>一键部署WordPress站点，支持单域名和批量部署</p>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h4>实时监控</h4>
            <p>实时查看部署进度和系统状态</p>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h4>模板管理</h4>
            <p>上传和管理WordPress站点模板</p>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">🔧</div>
            <h4>智能配置</h4>
            <p>自动生成数据库和站点配置</p>
        </div>
    </div>
    
    <div class="system-info">
        <h4>系统要求</h4>
        <ul>
            <li>PHP 8.0 或更高版本</li>
            <li>MySQL 5.7 或更高版本</li>
            <li>Nginx Web服务器</li>
            <li>宝塔面板（推荐）</li>
        </ul>
    </div>
    
    <div class="installation-note">
        <div class="alert alert-info">
            <strong>注意：</strong>安装过程将创建数据库、配置文件和必要的目录结构。请确保您有足够的权限。
        </div>
    </div>
</div>

<style>
.welcome-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon {
    color: #3498db;
    margin-bottom: 20px;
}

.welcome-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.feature-item {
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
}

.feature-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.feature-item h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.feature-item p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.system-info {
    text-align: left;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 30px 0;
}

.system-info h4 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.system-info ul {
    margin: 0;
    padding-left: 20px;
}

.system-info li {
    margin-bottom: 8px;
    color: #666;
}

.installation-note {
    margin-top: 30px;
}
</style>
