<VirtualHost *:80>
    DocumentRoot /var/www/html/public
    ServerName localhost
    ServerAlias wp-deploy.local
    
    # 启用重写引擎
    RewriteEngine On
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/wp-deploy-error.log
    CustomLog ${APACHE_LOG_DIR}/wp-deploy-access.log combined
    LogLevel warn
    
    # 目录配置
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # API路由重写
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
        
        # 前端路由重写（SPA支持）
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_URI} !^/api/
        RewriteRule ^(.*)$ index.html [QSA,L]
    </Directory>
    
    # 安全配置
    <Directory /var/www/html/config>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/database>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/logs>
        Require all denied
    </Directory>
    
    <Directory /var/www/html/uploads/temp>
        Require all denied
    </Directory>
    
    # 隐藏敏感文件
    <Files ~ "^\.">
        Require all denied
    </Files>
    
    <Files ~ "\.(sql|md|sh|json)$">
        Require all denied
    </Files>
    
    # PHP配置
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
    
    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
        Header append Cache-Control "public"
    </LocationMatch>
    
    # 安全头
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # 开发环境配置
    SetEnv ENVIRONMENT development
    SetEnv DEBUG_MODE true
</VirtualHost>

# HTTPS配置（生产环境使用）
<IfModule mod_ssl.c>
<VirtualHost *:443>
    DocumentRoot /var/www/html/public
    ServerName localhost
    ServerAlias wp-deploy.local
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem
    SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key
    
    # 其他配置与HTTP相同
    RewriteEngine On
    
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    # 安全头（HTTPS增强）
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    
    ErrorLog ${APACHE_LOG_DIR}/wp-deploy-ssl-error.log
    CustomLog ${APACHE_LOG_DIR}/wp-deploy-ssl-access.log combined
</VirtualHost>
</IfModule>
