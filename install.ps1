# WordPress Deploy Manager 一键安装脚本 (PowerShell)

Write-Host "🚀 WordPress Deploy Manager 本地环境安装" -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan

# 检查Docker
Write-Host "🔍 检查Docker环境..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker 已安装: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未安装，请先安装Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查Docker Compose
try {
    $composeVersion = docker-compose --version
    Write-Host "✅ Docker Compose 已安装: $composeVersion" -ForegroundColor Green
    $dockerComposeCmd = "docker-compose"
} catch {
    try {
        $composeVersion = docker compose version
        Write-Host "✅ Docker Compose (Plugin) 已安装: $composeVersion" -ForegroundColor Green
        $dockerComposeCmd = "docker compose"
    } catch {
        Write-Host "❌ Docker Compose 未安装" -ForegroundColor Red
        exit 1
    }
}

# 创建项目结构
Write-Host "📁 创建项目结构..." -ForegroundColor Yellow
$directories = @(
    "api", "config", "core", "database", "logs", "public", "scripts", "templates", "uploads",
    "uploads/temp", "uploads/templates", "public/assets/css", "public/assets/js", 
    "public/assets/images", "public/api", "docker/apache", "docker/php", "docker/mysql"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "✅ 项目结构创建完成" -ForegroundColor Green

# 创建Docker配置文件
Write-Host "🐳 创建Docker配置..." -ForegroundColor Yellow

# docker-compose.yml
@"
version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: wp-deploy-web
    ports:
      - "80:80"
    volumes:
      - ./:/var/www/html
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/uploads
    depends_on:
      - mysql
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    networks:
      - wp-deploy-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_mysql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - wp-deploy-network
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
    depends_on:
      - mysql
    networks:
      - wp-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  wp-deploy-network:
    driver: bridge
"@ | Out-File -FilePath "docker-compose.yml" -Encoding UTF8

# Dockerfile
@"
FROM php:8.1-apache

WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev libzip-dev \
    zip unzip nano wget && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip xml curl

# 启用Apache模块
RUN a2enmod rewrite

# 复制Apache配置
COPY docker/apache/vhost.conf /etc/apache2/sites-available/000-default.conf

# 设置权限
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80
"@ | Out-File -FilePath "docker/Dockerfile" -Encoding UTF8

# Apache配置
@"
<VirtualHost *:80>
    DocumentRoot /var/www/html/public
    ServerName localhost
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
"@ | Out-File -FilePath "docker/apache/vhost.conf" -Encoding UTF8

Write-Host "✅ Docker配置创建完成" -ForegroundColor Green

# 创建配置文件
Write-Host "⚙️ 创建配置文件..." -ForegroundColor Yellow

# 数据库配置
@"
<?php
// 数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);

// 数据库连接函数
function getDatabase() {
    static `$db = null;
    if (`$db === null) {
        `$dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        `$db = new PDO(`$dsn, DB_USER, DB_PASS, DB_OPTIONS);
    }
    return `$db;
}
?>
"@ | Out-File -FilePath "config/database.php" -Encoding UTF8

Write-Host "✅ 配置文件创建完成" -ForegroundColor Green

# 启动Docker服务
Write-Host "🚀 启动Docker服务..." -ForegroundColor Yellow
Write-Host "   正在构建Docker镜像..." -ForegroundColor Cyan

if ($dockerComposeCmd -eq "docker-compose") {
    docker-compose build --no-cache
    Write-Host "   正在启动服务..." -ForegroundColor Cyan
    docker-compose up -d
} else {
    docker compose build --no-cache
    Write-Host "   正在启动服务..." -ForegroundColor Cyan
    docker compose up -d
}

Write-Host "   等待服务启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "📊 检查服务状态..." -ForegroundColor Yellow
if ($dockerComposeCmd -eq "docker-compose") {
    docker-compose ps
} else {
    docker compose ps
}

# 显示完成信息
Write-Host ""
Write-Host "🎉 WordPress Deploy Manager 安装完成！" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址:" -ForegroundColor Yellow
Write-Host "   主应用:      http://localhost" -ForegroundColor Cyan
Write-Host "   phpMyAdmin:  http://localhost:8080" -ForegroundColor Cyan
Write-Host "   API接口:     http://localhost/api/" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔑 数据库信息:" -ForegroundColor Yellow
Write-Host "   主机: localhost:3306" -ForegroundColor White
Write-Host "   数据库: wp_deploy_manager" -ForegroundColor White
Write-Host "   用户: wp_deploy" -ForegroundColor White
Write-Host "   密码: wp_deploy_pass_2024" -ForegroundColor White
Write-Host "   Root密码: wp_deploy_2024" -ForegroundColor White
Write-Host ""
Write-Host "🛠️ 常用命令:" -ForegroundColor Yellow
Write-Host "   启动服务: docker-compose start" -ForegroundColor White
Write-Host "   停止服务: docker-compose stop" -ForegroundColor White
Write-Host "   查看状态: docker-compose ps" -ForegroundColor White
Write-Host "   查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host ""
Write-Host "🚀 现在可以访问 http://localhost 开始使用！" -ForegroundColor Green

# 询问是否打开浏览器
$openBrowser = Read-Host "是否立即在浏览器中打开应用？(y/N)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost"
}
