#!/bin/bash

# 增强的WordPress批量部署主脚本
# 支持模板化、差异化内容生成、智能监控

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR/../.."
CONFIG_DIR="$PROJECT_ROOT/config"
DATA_DIR="$PROJECT_ROOT/data"
LOGS_DIR="$PROJECT_ROOT/logs"

# 创建必要目录
mkdir -p "$LOGS_DIR/deployment" "$LOGS_DIR/errors" "$LOGS_DIR/monitoring"

# 加载配置和工具
source "$SCRIPT_DIR/../utils/logger.sh" 2>/dev/null || {
    log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_warning() { echo "[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
}

# 全局变量
DEPLOYMENT_ID="deploy_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$LOGS_DIR/deployment/${DEPLOYMENT_ID}.log"
ERROR_LOG="$LOGS_DIR/errors/${DEPLOYMENT_ID}_errors.log"
PARALLEL_LIMIT=3
SUCCESS_COUNT=0
FAIL_COUNT=0
TOTAL_SITES=0

# 配置加载
load_config() {
    local config_file="$CONFIG_DIR/enhanced_config.json"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 从JSON配置中提取关键信息
    MOTHER_DOMAIN=$(jq -r '.global.mother_domain' "$config_file")
    MYSQL_ROOT_PASS=$(jq -r '.global.mysql_root_pass' "$config_file")
    WP_CLI_PATH=$(jq -r '.global.wp_cli_path' "$config_file")
    WEB_USER=$(jq -r '.global.web_user' "$config_file")
    PARALLEL_LIMIT=$(jq -r '.deployment.parallel_limit' "$config_file")
    
    # 环境变量替换
    MYSQL_ROOT_PASS="${MYSQL_ROOT_PASS//\$\{MYSQL_ROOT_PASS\}/${MYSQL_ROOT_PASS_ENV:-$MYSQL_ROOT_PASS}}"
    
    log_info "配置加载完成"
}

# 读取站点配置
read_site_configs() {
    local domains_file="$DATA_DIR/enhanced_domains.csv"
    
    if [ ! -f "$domains_file" ]; then
        log_error "域名配置文件不存在: $domains_file"
        exit 1
    fi
    
    # 跳过标题行，读取站点配置
    TOTAL_SITES=$(tail -n +2 "$domains_file" | grep -v '^$' | wc -l)
    log_info "发现 $TOTAL_SITES 个站点待部署"
}

# 单站点部署函数
deploy_single_site() {
    local site_config="$1"
    local site_log="$LOGS_DIR/deployment/site_$(echo "$site_config" | cut -d',' -f1)_${DEPLOYMENT_ID}.log"
    
    # 解析站点配置
    IFS=',' read -r domain template_type industry language region custom_config priority keywords <<< "$site_config"
    
    log_info "开始部署站点: $domain" | tee -a "$site_log"
    
    # 1. 生成站点特定配置
    local db_name=$(echo "${domain//./_}" | tr '-' '_')
    local db_user="$db_name"
    local db_pass=$(openssl rand -base64 16)
    local admin_email="info@$domain"
    local webroot="/www/wwwroot/$domain"
    
    # 2. 创建站点和数据库（通过宝塔API）
    if ! create_site_and_database "$domain" "$template_type" "$db_name" "$db_user" "$db_pass" >> "$site_log" 2>&1; then
        log_error "站点创建失败: $domain" | tee -a "$ERROR_LOG"
        return 1
    fi
    
    # 3. 部署WordPress文件
    if ! deploy_wordpress_files "$domain" "$webroot" "$template_type" >> "$site_log" 2>&1; then
        log_error "WordPress文件部署失败: $domain" | tee -a "$ERROR_LOG"
        return 1
    fi
    
    # 4. 配置数据库
    if ! setup_database "$domain" "$db_name" "$db_user" "$db_pass" >> "$site_log" 2>&1; then
        log_error "数据库配置失败: $domain" | tee -a "$ERROR_LOG"
        return 1
    fi
    
    # 5. 生成差异化内容
    if ! generate_site_content "$domain" "$template_type" "$industry" "$custom_config" "$keywords" "$webroot" >> "$site_log" 2>&1; then
        log_warning "内容生成失败: $domain" | tee -a "$ERROR_LOG"
        # 内容生成失败不算致命错误，继续部署
    fi
    
    # 6. 配置WordPress
    if ! configure_wordpress "$domain" "$webroot" "$db_name" "$db_user" "$db_pass" "$admin_email" >> "$site_log" 2>&1; then
        log_error "WordPress配置失败: $domain" | tee -a "$ERROR_LOG"
        return 1
    fi
    
    # 7. 执行域名替换和优化
    if ! optimize_and_replace "$domain" "$webroot" "$keywords" >> "$site_log" 2>&1; then
        log_error "优化和替换失败: $domain" | tee -a "$ERROR_LOG"
        return 1
    fi
    
    # 8. 健康检查
    if ! perform_health_check "$domain" >> "$site_log" 2>&1; then
        log_warning "健康检查失败: $domain" | tee -a "$ERROR_LOG"
        # 健康检查失败不算致命错误
    fi
    
    log_success "站点部署完成: $domain" | tee -a "$site_log"
    return 0
}

# 创建站点和数据库
create_site_and_database() {
    local domain="$1"
    local template_type="$2"
    local db_name="$3"
    local db_user="$4"
    local db_pass="$5"
    
    log_info "创建站点和数据库: $domain"
    
    # 调用增强的宝塔管理脚本
    if [ -f "$SCRIPT_DIR/../bt_integration/enhanced_bt_manager.sh" ]; then
        bash "$SCRIPT_DIR/../bt_integration/enhanced_bt_manager.sh" create "$domain" "$template_type" "81" "true"
        bash "$SCRIPT_DIR/../bt_integration/enhanced_bt_manager.sh" create_db "$db_name" "$db_user" "$db_pass" "utf8mb4"
    else
        # 回退到基础创建方法
        mkdir -p "/www/wwwroot/$domain"
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "CREATE DATABASE IF NOT EXISTS \`$db_name\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "CREATE USER IF NOT EXISTS '$db_user'@'localhost' IDENTIFIED BY '$db_pass';"
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "GRANT ALL PRIVILEGES ON \`$db_name\`.* TO '$db_user'@'localhost';"
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "FLUSH PRIVILEGES;"
    fi
    
    return $?
}

# 部署WordPress文件
deploy_wordpress_files() {
    local domain="$1"
    local webroot="$2"
    local template_type="$3"
    
    log_info "部署WordPress文件: $domain"
    
    # 清理目标目录
    if [ -d "$webroot" ]; then
        find "$webroot" -mindepth 1 -delete 2>/dev/null || true
    else
        mkdir -p "$webroot"
    fi
    
    # 根据模板类型选择源文件
    local source_dir="/www/wwwroot/$MOTHER_DOMAIN"
    local template_dir="$PROJECT_ROOT/templates/industry/$template_type"
    
    # 复制基础WordPress文件
    if [ -d "$source_dir" ]; then
        rsync -av --exclude=".user.ini" --exclude="*.log" "$source_dir/" "$webroot/"
    else
        log_error "母站目录不存在: $source_dir"
        return 1
    fi
    
    # 应用模板特定文件（如果存在）
    if [ -d "$template_dir" ]; then
        rsync -av "$template_dir/" "$webroot/"
        log_info "应用模板文件: $template_type"
    fi
    
    # 设置权限
    chown -R "$WEB_USER:$WEB_USER" "$webroot"
    find "$webroot" -type d -exec chmod 755 {} \;
    find "$webroot" -type f -exec chmod 644 {} \;
    
    return 0
}

# 设置数据库
setup_database() {
    local domain="$1"
    local db_name="$2"
    local db_user="$3"
    local db_pass="$4"
    
    log_info "设置数据库: $db_name"
    
    # 从母站导入数据
    local mother_db="astra_nestlyalli"
    local temp_sql="/tmp/${domain}_import_$(date +%s).sql"
    
    # 导出母站数据
    mysqldump -u root -p"$MYSQL_ROOT_PASS" --single-transaction --routines --triggers "$mother_db" > "$temp_sql"
    
    if [ $? -eq 0 ]; then
        # 导入到新数据库
        mysql -u "$db_user" -p"$db_pass" "$db_name" < "$temp_sql"
        local import_result=$?
        rm -f "$temp_sql"
        return $import_result
    else
        log_error "数据库导出失败: $mother_db"
        rm -f "$temp_sql"
        return 1
    fi
}

# 生成站点内容
generate_site_content() {
    local domain="$1"
    local template_type="$2"
    local industry="$3"
    local custom_config="$4"
    local keywords="$5"
    local webroot="$6"
    
    log_info "生成差异化内容: $domain"
    
    # 调用内容生成脚本
    if [ -f "$SCRIPT_DIR/content_generator.sh" ]; then
        local content_json=$(bash "$SCRIPT_DIR/content_generator.sh" "$domain" "$template_type" "$industry" "$custom_config" "$keywords")
        
        # 保存内容配置
        echo "$content_json" > "$webroot/site_content.json"
        
        # 这里可以进一步处理生成的内容，比如创建页面、设置SEO等
        log_success "内容生成完成: $domain"
    else
        log_warning "内容生成脚本不存在，跳过内容生成"
    fi
    
    return 0
}

# 配置WordPress
configure_wordpress() {
    local domain="$1"
    local webroot="$2"
    local db_name="$3"
    local db_user="$4"
    local db_pass="$5"
    local admin_email="$6"
    
    log_info "配置WordPress: $domain"
    
    # 更新wp-config.php
    local wp_config="$webroot/wp-config.php"
    if [ -f "$wp_config" ]; then
        # 使用WP-CLI配置数据库连接
        sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" config set DB_NAME "$db_name" --raw
        sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" config set DB_USER "$db_user" --raw
        sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" config set DB_PASSWORD "$db_pass" --type=string
        sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" config set DB_HOST "localhost" --raw
        
        # 重新生成安全密钥
        sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" config shuffle-salts
        
        # 强制HTTPS设置
        echo '$_SERVER["HTTPS"] = "on";' >> "$wp_config"
        echo 'define("FORCE_SSL_ADMIN", true);' >> "$wp_config"
        
        log_success "WordPress配置完成"
    else
        log_error "wp-config.php不存在: $wp_config"
        return 1
    fi
    
    return 0
}

# 优化和域名替换
optimize_and_replace() {
    local domain="$1"
    local webroot="$2"
    local keywords="$3"
    
    log_info "执行优化和域名替换: $domain"
    
    # WP-CLI域名替换
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" search-replace "$MOTHER_DOMAIN" "$domain" --all-tables --allow-root
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" search-replace "https://$MOTHER_DOMAIN" "https://$domain" --all-tables --allow-root
    
    # 更新WordPress选项
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" option update siteurl "https://$domain" --allow-root
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" option update home "https://$domain" --allow-root
    
    # 清理缓存
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" cache flush --allow-root
    sudo -u "$WEB_USER" "$WP_CLI_PATH" --path="$webroot" rewrite flush --hard --allow-root
    
    # 更新robots.txt
    local robots_file="$webroot/robots.txt"
    echo "Sitemap: https://$domain/sitemap_index.xml" > "$robots_file"
    
    log_success "优化和替换完成"
    return 0
}

# 健康检查
perform_health_check() {
    local domain="$1"
    
    log_info "执行健康检查: $domain"
    
    # 调用宝塔管理脚本的健康检查
    if [ -f "$SCRIPT_DIR/../bt_integration/enhanced_bt_manager.sh" ]; then
        bash "$SCRIPT_DIR/../bt_integration/enhanced_bt_manager.sh" health_check "$domain"
    else
        # 基础健康检查
        local http_status=$(curl -s -o /dev/null -w "%{http_code}" "http://$domain" --max-time 10)
        if [ "$http_status" = "200" ] || [ "$http_status" = "301" ] || [ "$http_status" = "302" ]; then
            log_success "健康检查通过: $domain"
            return 0
        else
            log_warning "健康检查失败: $domain (HTTP: $http_status)"
            return 1
        fi
    fi
}

# 并行部署管理
parallel_deploy() {
    local domains_file="$DATA_DIR/enhanced_domains.csv"
    local pids=()
    local current_jobs=0
    
    log_info "开始并行部署 (限制: $PARALLEL_LIMIT 个并发)"
    
    # 跳过标题行
    tail -n +2 "$domains_file" | while IFS= read -r site_config; do
        # 跳过空行和注释行
        [[ -z "$site_config" || "$site_config" =~ ^# ]] && continue
        
        # 等待空闲槽位
        while [ $current_jobs -ge $PARALLEL_LIMIT ]; do
            wait -n  # 等待任意一个后台任务完成
            current_jobs=$((current_jobs - 1))
        done
        
        # 启动新的部署任务
        (
            if deploy_single_site "$site_config"; then
                echo "SUCCESS:$(echo "$site_config" | cut -d',' -f1)"
            else
                echo "FAILED:$(echo "$site_config" | cut -d',' -f1)"
            fi
        ) &
        
        pids+=($!)
        current_jobs=$((current_jobs + 1))
        
        log_info "启动部署任务: $(echo "$site_config" | cut -d',' -f1) (PID: ${pids[-1]})"
    done
    
    # 等待所有任务完成
    log_info "等待所有部署任务完成..."
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
}

# 生成部署报告
generate_report() {
    local report_file="$LOGS_DIR/deployment/${DEPLOYMENT_ID}_report.txt"
    
    cat << EOF > "$report_file"
WordPress批量部署报告
==================

部署ID: $DEPLOYMENT_ID
开始时间: $(date)
总站点数: $TOTAL_SITES
成功数量: $SUCCESS_COUNT
失败数量: $FAIL_COUNT
成功率: $(( SUCCESS_COUNT * 100 / TOTAL_SITES ))%

详细日志: $LOG_FILE
错误日志: $ERROR_LOG

EOF
    
    log_info "部署报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始WordPress批量部署 (ID: $DEPLOYMENT_ID)"
    
    # 1. 加载配置
    load_config
    
    # 2. 读取站点配置
    read_site_configs
    
    # 3. 执行并行部署
    parallel_deploy
    
    # 4. 统计结果
    SUCCESS_COUNT=$(grep -c "SUCCESS:" "$LOG_FILE" 2>/dev/null || echo "0")
    FAIL_COUNT=$(grep -c "FAILED:" "$LOG_FILE" 2>/dev/null || echo "0")
    
    # 5. 生成报告
    generate_report
    
    log_info "批量部署完成！成功: $SUCCESS_COUNT, 失败: $FAIL_COUNT"
    
    if [ $FAIL_COUNT -gt 0 ]; then
        log_warning "存在失败的部署，请检查错误日志: $ERROR_LOG"
        exit 1
    fi
    
    exit 0
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
