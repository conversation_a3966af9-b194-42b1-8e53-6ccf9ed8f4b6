# 🚀 WordPress Deploy Manager - 本地部署快速指南

## 📋 概述

本指南提供了WordPress Deploy Manager项目的完整本地部署方案，包括环境搭建、问题修复、功能测试和生产部署准备。

## ⚡ 快速开始（推荐）

### 使用Docker（最简单）

```bash
# 1. 克隆或下载项目
git clone <repository-url> wp-deploy-manager
cd wp-deploy-manager

# 2. 运行快速启动脚本
chmod +x quick_start.sh
./quick_start.sh

# 3. 访问应用
open http://localhost
```

### 手动部署

```bash
# 1. 启动Docker服务
docker-compose up -d

# 2. 修复JavaScript问题
php fix_js_issues.php

# 3. 验证修复结果
./verify_js_fix.sh

# 4. 访问应用
open http://localhost
```

## 🔧 环境要求

### 最低要求
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 4GB+
- **磁盘**: 10GB+

### 推荐配置
- **Docker**: 最新版本
- **内存**: 8GB+
- **磁盘**: 20GB+
- **CPU**: 4核心+

## 📁 项目结构

```
wp-deploy-manager/
├── api/                    # API端点
├── config/                 # 配置文件
├── core/                   # 核心类库
├── database/               # 数据库文件
├── docker/                 # Docker配置
├── logs/                   # 日志文件
├── public/                 # Web根目录
│   ├── assets/            # 静态资源
│   ├── api/               # API路由
│   └── index.html         # 主页面
├── scripts/               # 脚本文件
├── templates/             # 模板文件
├── uploads/               # 上传文件
├── docker-compose.yml     # Docker编排
├── quick_start.sh         # 快速启动
├── fix_js_issues.php      # 问题修复
└── README.md              # 说明文档
```

## 🌐 访问地址

启动成功后，可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost | WordPress Deploy Manager |
| phpMyAdmin | http://localhost:8080 | 数据库管理 |
| MailHog | http://localhost:8025 | 邮件测试 |
| 健康检查 | http://localhost/health.php | 系统状态 |
| JS测试 | http://localhost/js-test.html | 功能测试 |

## 🔑 默认账户信息

### 数据库
- **主机**: localhost:3306
- **数据库**: wp_deploy_manager
- **用户**: wp_deploy
- **密码**: wp_deploy_pass_2024
- **Root密码**: wp_deploy_2024

### phpMyAdmin
- **用户**: root
- **密码**: wp_deploy_2024

## 🛠️ 常用命令

### Docker管理
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建
docker-compose up -d --build
```

### 容器操作
```bash
# 进入Web容器
docker-compose exec web bash

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p

# 查看容器资源使用
docker stats
```

### 问题修复
```bash
# 修复JavaScript问题
php fix_js_issues.php

# 验证修复结果
./verify_js_fix.sh

# 重置数据库
php manage_database.php reset

# 创建测试数据
php manage_database.php seed
```

## 🧪 功能测试

### 1. 基础功能测试
- [ ] 页面正常加载
- [ ] 无JavaScript错误
- [ ] API连接正常
- [ ] 导航功能工作

### 2. 模块测试
- [ ] **仪表板**: 统计信息显示
- [ ] **部署管理**: 部署界面功能
- [ ] **模板管理**: 模板列表和操作
- [ ] **监控中心**: 系统监控功能
- [ ] **日志管理**: 日志查看功能
- [ ] **系统设置**: 配置保存功能

### 3. API测试
```bash
# 测试基础API
curl http://localhost/api/

# 测试模板API
curl http://localhost/api/templates.php?action=list

# 测试状态API
curl http://localhost/api/status.php?action=system
```

## 🚨 常见问题

### 1. 端口冲突
```bash
# 检查端口占用
lsof -i :80
lsof -i :3306

# 修改端口（编辑docker-compose.yml）
ports:
  - "8080:80"  # 改为8080端口
```

### 2. JavaScript错误
```bash
# 运行修复脚本
php fix_js_issues.php

# 清除浏览器缓存
# Ctrl+Shift+Delete (Windows/Linux)
# Cmd+Shift+Delete (macOS)
```

### 3. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 重启MySQL服务
docker-compose restart mysql

# 检查配置文件
cat config/database.php
```

### 4. 权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
chmod -R 755 .
chmod -R 777 logs uploads
```

## 📊 性能优化

### 1. 开发环境优化
```bash
# 启用PHP OPcache
echo "opcache.enable=1" >> docker/php/php.ini

# 增加内存限制
echo "memory_limit=1G" >> docker/php/php.ini

# 启用Redis缓存
docker-compose exec redis redis-cli ping
```

### 2. 数据库优化
```sql
-- 优化MySQL配置
SET GLOBAL innodb_buffer_pool_size = 256M;
SET GLOBAL query_cache_size = 64M;
```

## 🔄 同步到生产环境

### 1. 创建部署包
```bash
# 运行部署包创建脚本
./create_deployment_package.sh

# 上传到服务器
scp wp-deploy-manager-production-*.tar.gz user@server:/path/
```

### 2. 生产环境配置
```bash
# 修改配置文件
cp config/config.php config/config.production.php
sed -i "s/development/production/" config/config.production.php
sed -i "s/DEBUG_MODE', true/DEBUG_MODE', false/" config/config.production.php
```

### 3. 同步脚本
```bash
# 使用同步脚本
./sync_to_production.sh
```

## 📚 开发指南

### 1. 添加新功能
```bash
# 创建新的API端点
cp api/templates.php api/new_feature.php

# 添加前端模块
cp public/assets/js/templates.js public/assets/js/new_feature.js

# 更新路由配置
# 编辑 public/api/index.php
```

### 2. 调试技巧
```bash
# 查看PHP错误日志
docker-compose exec web tail -f /var/log/php_errors.log

# 查看应用日志
tail -f logs/system_*.log

# 启用调试模式
# 编辑 config/config.php
define('DEBUG_MODE', true);
```

### 3. 数据库操作
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p wp_deploy_manager > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p wp_deploy_manager < backup.sql

# 查看表结构
docker-compose exec mysql mysql -u root -p -e "DESCRIBE wp_deploy_manager.templates"
```

## 🆘 获取帮助

### 1. 日志文件位置
- **应用日志**: `logs/system_*.log`
- **Apache日志**: `docker-compose logs web`
- **MySQL日志**: `docker-compose logs mysql`
- **PHP错误**: `/var/log/php_errors.log`

### 2. 调试工具
- **健康检查**: http://localhost/health.php
- **JavaScript测试**: http://localhost/js-test.html
- **phpMyAdmin**: http://localhost:8080
- **MailHog**: http://localhost:8025

### 3. 重置环境
```bash
# 完全重置
docker-compose down -v
docker-compose up -d --build
php fix_js_issues.php
```

## 📝 更新日志

- **v1.0.0**: 初始版本，基础功能完成
- **v1.1.0**: 修复JavaScript问题，优化Docker配置
- **v1.2.0**: 添加健康检查，完善测试工具

---

🎉 **恭喜！** 您已成功部署WordPress Deploy Manager本地开发环境。开始探索和开发吧！
