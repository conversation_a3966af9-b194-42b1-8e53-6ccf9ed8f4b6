version: '3.8'

services:
  web:
    image: php:8.1-apache
    container_name: wp-deploy-web
    ports:
      - "80:80"
    volumes:
      - ./public:/var/www/html
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/uploads
    depends_on:
      - mysql
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    networks:
      - wp-deploy-network
    restart: unless-stopped
    command: >
      bash -c "
      apt-get update && apt-get install -y libzip-dev &&
      docker-php-ext-install pdo_mysql zip &&
      a2enmod rewrite &&
      echo '<Directory /var/www/html>' > /etc/apache2/conf-available/rewrite.conf &&
      echo '    AllowOverride All' >> /etc/apache2/conf-available/rewrite.conf &&
      echo '    RewriteEngine On' >> /etc/apache2/conf-available/rewrite.conf &&
      echo '    RewriteCond %{REQUEST_FILENAME} !-f' >> /etc/apache2/conf-available/rewrite.conf &&
      echo '    RewriteCond %{REQUEST_FILENAME} !-d' >> /etc/apache2/conf-available/rewrite.conf &&
      echo '    RewriteRule ^api/(.*)$$ api/index.php [QSA,L]' >> /etc/apache2/conf-available/rewrite.conf &&
      echo '</Directory>' >> /etc/apache2/conf-available/rewrite.conf &&
      a2enconf rewrite &&
      apache2-foreground"

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d
    networks:
      - wp-deploy-network
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
    depends_on:
      - mysql
    networks:
      - wp-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  wp-deploy-network:
    driver: bridge
