import requests
import csv
import time
import argparse # 用于处理命令行参数
import os

# 输出 CSV 文件的基础名称，我们会为每个输入文件生成一个对应的输出文件
OUTPUT_FILE_BASE = "ssl_status_report_python"

# (check_domain_ssl 函数保持不变，和之前的例子一样)
def check_domain_ssl(domain):
    """检查单个域名的SSL状态，并查找特定的错误模式。"""
    url = f"https://{domain.strip()}"
    headers = {
        'User-Agent': 'Mozilla/5.0 BatchSSLCheckerPython/1.0'
    }
    status = "未知"
    details = ""

    try:
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=True, verify=True)
        content = response.text.lower()

        if "error code 526" in content or "invalid ssl certificate on origin" in content:
            status = "Cloudflare 错误 526"
            details = "源服务器 SSL 证书无效。"
        elif "error code 525" in content or ("ssl handshake failed" in content and "cloudflare" in content):
            status = "Cloudflare 错误 525"
            details = "Cloudflare 与源服务器 SSL 握手失败。"
        elif "error 520" in content and "cloudflare" in content:
            status = "Cloudflare 错误 520"
            details = "Web 服务器向 Cloudflare 返回了未知错误。"
        elif "invalid ssl certificate" in content:
             status = "SSL 无效 (HTML内容检测)"
             details = "页面内容指示 SSL 证书无效。"
        elif response.status_code >= 400:
            status = f"HTTP 错误 {response.status_code}"
            details = f"服务器返回 HTTP {response.status_code}."
            if "502 bad gateway" in content:
                details += " (错误网关)"
            elif "503 service unavailable" in content:
                details += " (服务不可用)"
        else:
            status = f"正常 (HTTP {response.status_code})"
            details = "客户端 SSL 握手可能正常。"

    except requests.exceptions.SSLError as e:
        status = "SSL 错误 (客户端检测)"
        error_str = str(e).lower()
        if "certificate verify failed" in error_str:
            if "certificate has expired" in error_str:
                details = "证书已过期。"
            elif "self signed certificate" in error_str or "unable to get local issuer" in error_str:
                details = "不受信任的颁发者 / 自签名 / 证书链问题。"
            elif "hostname mismatch" in error_str or "doesn't match" in error_str :
                details = "主机名不匹配。"
            else:
                details = "证书验证失败 (通用)。"
        else:
            details = f"SSL 错误: {str(e)[:100]}"
    except requests.exceptions.ConnectionError as e:
        status = "连接错误"
        if "dnsfailure" in str(e).lower() or "name or service not known" in str(e).lower():
            details = "DNS 解析失败。"
        else:
            details = f"无法连接: {str(e)[:100]}"
    except requests.exceptions.Timeout:
        status = "超时"
        details = "请求超时。"
    except Exception as e:
        status = "其他错误"
        details = f"发生未知错误: {str(e)[:100]}"
        
    return domain, status, details

def process_domain_file(domain_file_path):
    """处理单个域名文件并生成对应的CSV报告"""
    results = []
    print(f"\n--- 开始处理文件: {domain_file_path} ---")
    try:
        with open(domain_file_path, 'r', encoding='utf-8') as f_domains:
            domains = [line.strip() for line in f_domains if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        print(f"错误: 域名文件 '{domain_file_path}' 未找到。跳过此文件。")
        return

    if not domains:
        print(f"文件 '{domain_file_path}' 为空或只包含注释。跳过此文件。")
        return

    for domain_name in domains:
        print(f"正在检查 {domain_name} (来自 {os.path.basename(domain_file_path)})...")
        d, s, dt = check_domain_ssl(domain_name)
        results.append([d, s, dt])
        time.sleep(0.1) # 短暂休眠

    # 为每个输入文件生成一个单独的输出文件
    # 例如，输入 domains-eu.txt，输出 ssl_status_report_python_domains-eu.csv
    base_name = os.path.basename(domain_file_path)
    output_file_name = f"{OUTPUT_FILE_BASE}_{os.path.splitext(base_name)[0]}.csv"
    
    try:
        with open(output_file_name, 'w', newline='', encoding='utf-8') as f_output:
            writer = csv.writer(f_output)
            writer.writerow(["域名", "状态", "详情"])
            writer.writerows(results)
        print(f"处理完成 '{domain_file_path}'。结果已保存到 {output_file_name}")
    except IOError:
        print(f"错误: 无法写入输出文件 '{output_file_name}'。")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="批量检查域名的SSL状态。")
    parser.add_argument('domain_files', nargs='+', help="一个或多个包含域名列表的文本文件名 (例如: domains-eu.txt domains-us.txt)")
    
    args = parser.parse_args()

    if not args.domain_files:
        print("错误: 请至少提供一个域名文件名。")
        parser.print_help()
        exit(1)

    for file_path in args.domain_files:
        process_domain_file(file_path)
    
    print("\n所有指定的域名文件处理完毕。")