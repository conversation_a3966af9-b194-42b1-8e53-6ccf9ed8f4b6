<?php
/**
 * 日志管理器
 * 负责系统日志的记录、查询和管理
 */

class LogManager {
    private $db;
    private $logFile;
    
    public function __construct() {
        $this->db = getDatabase();
        $this->logFile = LOGS_PATH . '/system_' . date('Y-m-d') . '.log';

        // 确保日志目录存在
        if (!is_dir(LOGS_PATH)) {
            mkdir(LOGS_PATH, 0755, true);
        }
    }
    
    /**
     * 记录日志
     */
    public function log($level, $message, $context = []) {
        try {
            $timestamp = date(DATE_FORMAT);
            $contextJson = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : null;
            
            // 写入数据库
            $stmt = $this->db->prepare("
                INSERT INTO system_logs (level, message, context, source, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $level,
                $message,
                $contextJson,
                $this->getSource(),
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            // 写入文件
            $logEntry = sprintf(
                "[%s] %s: %s %s\n",
                $timestamp,
                $level,
                $message,
                $contextJson ? "Context: $contextJson" : ''
            );
            
            file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
            
            // 检查日志文件大小并轮转
            $this->rotateLogIfNeeded();
            
        } catch (Exception $e) {
            // 如果日志记录失败，写入错误日志
            error_log("LogManager error: " . $e->getMessage());
        }
    }
    
    /**
     * 获取日志列表
     */
    public function getLogs($filters = []) {
        $sql = "SELECT * FROM system_logs WHERE 1=1";
        $params = [];
        
        // 应用过滤器
        if (!empty($filters['level'])) {
            $sql .= " AND level = ?";
            $params[] = $filters['level'];
        }
        
        if (!empty($filters['source'])) {
            $sql .= " AND source LIKE ?";
            $params[] = '%' . $filters['source'] . '%';
        }
        
        if (!empty($filters['start_date'])) {
            $sql .= " AND created_at >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $sql .= " AND created_at <= ?";
            $params[] = $filters['end_date'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (message LIKE ? OR context LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // 分页
        $page = max(1, intval($filters['page'] ?? 1));
        $limit = min(100, max(10, intval($filters['limit'] ?? 50)));
        $offset = ($page - 1) * $limit;
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 解析context JSON
        foreach ($logs as &$log) {
            if ($log['context']) {
                $log['context'] = json_decode($log['context'], true);
            }
        }
        
        // 获取总数
        $countSql = str_replace('SELECT *', 'SELECT COUNT(*)', explode(' ORDER BY', $sql)[0]);
        $countParams = array_slice($params, 0, -2); // 移除LIMIT和OFFSET参数
        $countStmt = $this->db->prepare($countSql);
        $countStmt->execute($countParams);
        $total = $countStmt->fetchColumn();
        
        return [
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ];
    }
    
    /**
     * 获取日志统计
     */
    public function getLogStats($days = 7) {
        $stmt = $this->db->prepare("
            SELECT 
                level,
                COUNT(*) as count,
                DATE(created_at) as date
            FROM system_logs 
            WHERE created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY level, DATE(created_at)
            ORDER BY date DESC, level
        ");
        $stmt->execute([$days]);
        $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取总体统计
        $stmt = $this->db->prepare("
            SELECT 
                level,
                COUNT(*) as total_count
            FROM system_logs 
            WHERE created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY level
        ");
        $stmt->execute([$days]);
        $totals = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        return [
            'daily_stats' => $stats,
            'totals' => $totals,
            'period_days' => $days
        ];
    }
    
    /**
     * 获取部署任务日志
     */
    public function getJobLogs($jobId) {
        try {
            // 获取任务信息
            $stmt = $this->db->prepare("SELECT log_file FROM deploy_jobs WHERE id = ?");
            $stmt->execute([$jobId]);
            $logFile = $stmt->fetchColumn();
            
            if (!$logFile || !file_exists($logFile)) {
                return [
                    'content' => '',
                    'size' => 0,
                    'last_modified' => null
                ];
            }
            
            $content = file_get_contents($logFile);
            $size = filesize($logFile);
            $lastModified = date(DATE_FORMAT, filemtime($logFile));
            
            return [
                'content' => $content,
                'size' => $size,
                'size_formatted' => formatBytes($size),
                'last_modified' => $lastModified,
                'file_path' => $logFile
            ];
            
        } catch (Exception $e) {
            $this->log('ERROR', 'Failed to get job logs', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 实时获取任务日志（用于WebSocket/SSE）
     */
    public function getJobLogsTail($jobId, $lines = 50) {
        try {
            $stmt = $this->db->prepare("SELECT log_file FROM deploy_jobs WHERE id = ?");
            $stmt->execute([$jobId]);
            $logFile = $stmt->fetchColumn();
            
            if (!$logFile || !file_exists($logFile)) {
                return [];
            }
            
            $command = "tail -n $lines " . escapeshellarg($logFile);
            $output = shell_exec($command);
            
            if ($output) {
                return array_filter(explode("\n", trim($output)));
            }
            
            return [];
            
        } catch (Exception $e) {
            $this->log('ERROR', 'Failed to get job logs tail', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 清理旧日志
     */
    public function cleanupOldLogs($days = 30) {
        try {
            $deletedCount = 0;
            
            // 清理数据库日志
            $stmt = $this->db->prepare("
                DELETE FROM system_logs 
                WHERE created_at < datetime('now', '-' || ? || ' days')
            ");
            $stmt->execute([$days]);
            $deletedCount += $stmt->rowCount();
            
            // 清理日志文件
            $logFiles = glob(LOGS_PATH . '/*.log');
            $cutoffTime = time() - ($days * 24 * 3600);
            
            foreach ($logFiles as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $deletedCount++;
                }
            }
            
            $this->log('INFO', 'Old logs cleaned up', [
                'days' => $days,
                'deleted_count' => $deletedCount
            ]);
            
            return $deletedCount;
            
        } catch (Exception $e) {
            $this->log('ERROR', 'Failed to cleanup old logs', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 导出日志
     */
    public function exportLogs($filters = [], $format = 'json') {
        try {
            $result = $this->getLogs(array_merge($filters, ['limit' => 10000]));
            $logs = $result['logs'];
            
            switch ($format) {
                case 'csv':
                    return $this->exportToCsv($logs);
                case 'txt':
                    return $this->exportToText($logs);
                case 'json':
                default:
                    return json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            }
            
        } catch (Exception $e) {
            $this->log('ERROR', 'Failed to export logs', [
                'format' => $format,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCsv($logs) {
        $output = "Timestamp,Level,Message,Source,IP Address\n";
        
        foreach ($logs as $log) {
            $output .= sprintf(
                "%s,%s,\"%s\",\"%s\",\"%s\"\n",
                $log['created_at'],
                $log['level'],
                str_replace('"', '""', $log['message']),
                $log['source'] ?? '',
                $log['ip_address'] ?? ''
            );
        }
        
        return $output;
    }
    
    /**
     * 导出为文本格式
     */
    private function exportToText($logs) {
        $output = "WordPress Deploy Manager - Log Export\n";
        $output .= "Generated: " . date(DATE_FORMAT) . "\n";
        $output .= str_repeat("=", 50) . "\n\n";
        
        foreach ($logs as $log) {
            $output .= sprintf(
                "[%s] %s: %s\n",
                $log['created_at'],
                $log['level'],
                $log['message']
            );
            
            if ($log['context']) {
                $output .= "Context: " . json_encode($log['context'], JSON_UNESCAPED_UNICODE) . "\n";
            }
            
            $output .= "\n";
        }
        
        return $output;
    }
    
    /**
     * 轮转日志文件
     */
    private function rotateLogIfNeeded() {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $fileSize = filesize($this->logFile);
        if ($fileSize < LOG_MAX_SIZE) {
            return;
        }
        
        // 轮转日志文件
        $timestamp = date('Y-m-d_H-i-s');
        $rotatedFile = str_replace('.log', "_$timestamp.log", $this->logFile);
        
        rename($this->logFile, $rotatedFile);
        
        // 清理旧的轮转文件
        $this->cleanupRotatedLogs();
    }
    
    /**
     * 清理旧的轮转日志文件
     */
    private function cleanupRotatedLogs() {
        $pattern = LOGS_PATH . '/system_*_*.log';
        $files = glob($pattern);
        
        if (count($files) <= LOG_MAX_FILES) {
            return;
        }
        
        // 按修改时间排序
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // 删除最旧的文件
        $filesToDelete = array_slice($files, 0, count($files) - LOG_MAX_FILES);
        foreach ($filesToDelete as $file) {
            unlink($file);
        }
    }
    
    /**
     * 获取调用源
     */
    private function getSource() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        
        if (isset($backtrace[2])) {
            $caller = $backtrace[2];
            return ($caller['class'] ?? '') . ($caller['function'] ?? '');
        }
        
        return 'unknown';
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}
