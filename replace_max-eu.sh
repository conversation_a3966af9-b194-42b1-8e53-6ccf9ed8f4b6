#!/bin/bash

# 设置要替换的内容，改用竖线 `|` 作为分隔符，避免对 `/` 的转义
old_code='\$max_total = mt_rand(18801, 18959) / 100;'
new_code='\$max_total = mt_rand(18500, 18895) / 100;'

# 定义需要处理的文件路径
file_paths=(
"/www/wwwroot/hogaryreformas.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/tooltownes.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/bricoferes.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/ferrokeyes.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/mrokit.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/grilldeko.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/rabattmoebelshop.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/moebelundzubehoer.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/depotsito.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/energiekraft24.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/bioprodukte24.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/gesundnaturshop.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/moebelangebot.com/wp-content/themes/woodmart/functions.php"
"/www/wwwroot/jardinesokey.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/ferreteriayjardin.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/herramixycerra.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/hogarez.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/primetooldepot.com//wp-content/themes/woostify/functions.php"
"/www/wwwroot/ultimatetoolmart.com//wp-content/themes/woostify/functions.php"
"/www/wwwroot/wohnlichtshop.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/lichtundwohn.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/moebelunddeko.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/wohntraumshop.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/moebelparadies.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/gunstigewohnwelt.com/wp-content/themes/woostify/functions.php"
"/www/wwwroot/deutischeswerkzeug.com/wp-content/themes/woostify/functions.php"
)

# 逐个处理文件
for file_path in "${file_paths[@]}"
do
    # 替换当前文件
    echo "开始替换：$old_code -> $new_code in $file_path"
    sed_output=$(sed -i "s|$old_code|$new_code|g" "$file_path" 2>&1)
    sed_exit_status=$?
    if [ $sed_exit_status -eq 0 ]; then
        echo "替换完成：$file_path"
    else
        echo "Error: 替换 $file_path 失败，退出状态码: $sed_exit_status"
        echo "sed 输出: $sed_output"
    fi
done

echo "批量替换完成！"
