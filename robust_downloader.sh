#!/bin/bash

# 检查参数是否为空
if [ $# -ne 2 ]; then
    echo "Usage: $0 <image_links_file> <target_folder>"
    exit 1
fi

# 获取输入参数
image_links_file="$1"
target_folder="$2"

# --- 配置项 ---
# 并发下载数 (根据服务器的容忍度调整，可以从 1 或 2 开始)
PARALLEL_JOBS=2
# 每次下载前的最小延迟（秒）
MIN_SLEEP=2
# 每次下载前的最大延迟（秒）
MAX_SLEEP=5
# curl 下载重试次数
CURL_RETRIES=3
# curl 重试间隔（秒）
CURL_RETRY_DELAY=10
# curl 连接超时（秒）
CURL_CONNECT_TIMEOUT=15
# curl 最大传输时间（秒）
CURL_MAX_TIME=120
# 模拟浏览器的 User-Agent
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
# ---

# 创建目标文件夹 (如果脚本在 parallel 内部执行，这里可能不需要重复创建)
# 为确保只创建一次，我们将其移到 parallel 调用之前
base_download_dir="$target_folder/www/imagerdown/imagedownload"
mkdir -p "$base_download_dir"
echo "Base directory created/ensured: $base_download_dir"

# 失败链接记录文件
failed_links_file="${image_links_file%.*}_failed.txt"
> "$failed_links_file" # 清空或创建失败文件

# 定义下载函数
download_image() {
    local url="$1"
    local base_dir="$2" # 传递基础下载目录

    # 提取原文件名，并尝试清理可能导致问题的字符
    local original_filename
    original_filename=$(basename "$url")
    # 移除查询参数
    original_filename="${original_filename%%\?*}"
    # 替换掉不安全字符 (可以根据需要扩展这个列表)
    original_filename=$(echo "$original_filename" | sed 's/[^a-zA-Z0-9._-]/_/g')

    # 使用 URL 的 SHA1 哈希值作为前缀，确保文件名唯一且避免路径问题
    # 这样可以避免原脚本中 file_path 可能过长或包含非法字符的问题
    local url_hash
    url_hash=$(echo -n "$url" | sha1sum | awk '{print $1}')
    local new_filename="${url_hash}-${original_filename}"
    local target_file_path="$base_dir/$new_filename"

    # 检查文件是否已经存在
    if [ -f "$target_file_path" ]; then
        echo "INFO: File $new_filename (from $url) already exists, skipping download."
        return 0 # 成功退出
    fi

    # 添加随机延迟
    local sleep_time
    sleep_time=$(shuf -i "$MIN_SLEEP"-"$MAX_SLEEP" -n 1)
    echo "INFO: Sleeping for ${sleep_time}s before downloading $url"
    sleep "$sleep_time"

    echo "INFO: Attempting to download $url to $new_filename"

    # 下载图片
    # -L: 跟随重定向
    # -f: (fail silently) HTTP 服务器错误时，不输出 HTML 错误页，而是返回错误码 22
    # -sS: 静默模式，但显示错误信息
    # --retry: 重试次数
    # --retry-delay: 重试间隔
    # --connect-timeout: 连接超时
    # --max-time: 最大传输时间
    # -A: User-Agent
    # -o: 输出文件
    # --create-dirs: 如果目标路径中的目录不存在，则创建它们 (在这里我们已经提前创建了 base_dir)
    local temp_download_file="${target_file_path}.part"
    curl -L -f -sS \
         --retry "$CURL_RETRIES" \
         --retry-delay "$CURL_RETRY_DELAY" \
         --connect-timeout "$CURL_CONNECT_TIMEOUT" \
         --max-time "$CURL_MAX_TIME" \
         -A "$USER_AGENT" \
         -o "$temp_download_file" \
         "$url"

    local curl_exit_code=$?

    if [ $curl_exit_code -eq 0 ]; then
        # 检查下载的文件是否为空或者非常小 (可能是错误页面)
        local file_size
        file_size=$(stat -c%s "$temp_download_file" 2>/dev/null || echo 0)

        if [ "$file_size" -lt 1024 ]; then # 假设图片小于1KB可能是无效的 (可调整)
            echo "WARNING: Downloaded file $new_filename for $url is very small ($file_size bytes). Possible error page or corrupted file."
            # 可以尝试检查文件类型，例如：
            # file_type=$(file -b --mime-type "$temp_download_file")
            # if [[ "$file_type" == "text/html" ]]; then
            #     echo "ERROR: Downloaded file for $url appears to be an HTML page, not an image."
            #     rm -f "$temp_download_file"
            #     echo "$url" >> "$failed_links_file"
            #     return 1
            # fi
            # 如果只是小，但我们不确定是不是错误，还是先保留
        fi
        mv "$temp_download_file" "$target_file_path"
        echo "SUCCESS: Downloaded $url as $new_filename"
        return 0
    else
        echo "ERROR: Failed to download $url (curl exit code: $curl_exit_code). Adding to $failed_links_file"
        rm -f "$temp_download_file" # 清理部分下载的文件
        echo "$url" >> "$failed_links_file"
        return 1 # 失败退出
    fi
}

export -f download_image
export base_download_dir # 导出给 parallel 中的函数使用
export MIN_SLEEP MAX_SLEEP CURL_RETRIES CURL_RETRY_DELAY CURL_CONNECT_TIMEOUT CURL_MAX_TIME USER_AGENT failed_links_file

echo "Starting downloads with $PARALLEL_JOBS parallel jobs..."
# 使用 GNU Parallel 执行多线程下载
# --joblog: 记录每个任务的执行情况
# --retries N: 如果 download_image 返回非0，parallel 会重试整个任务N次 (这与curl内部重试不同)
# 我们这里依赖 curl 的重试和函数内部的错误处理，所以 parallel 的 retries 可以设为0或1
# 将 base_download_dir 作为参数传递给 download_image
parallel --eta --joblog parallel_joblog.log -j "$PARALLEL_JOBS" download_image {} "$base_download_dir" :::: "$image_links_file"

echo "Download process finished."
echo "Failed links (if any) are saved in: $failed_links_file"
if [ -s "$failed_links_file" ]; then
    echo "Some downloads failed. Check $failed_links_file"
else
    echo "All attempted downloads completed (check logs for individual success/warnings)."
    rm -f "$failed_links_file" # 如果没有失败的，删除空的失败文件
fi