#!/bin/bash

# 设置图片链接文件和目标下载文件夹
IMAGE_LINKS_FILE="/www/imagerdown/image_links.txt"
DOWNLOAD_FOLDER="/www/imagerdown/imagedownload"
FAILED_LOG="/www/imagerdown/failed_downloads.txt"

# 创建目标文件夹
mkdir -p "$DOWNLOAD_FOLDER"

# 清空失败日志文件
> "$FAILED_LOG"

# 使用 parallel 执行下载，下载失败的链接记录在 FAILED_LOG 中
parallel -j 8 wget -nc -P "$DOWNLOAD_FOLDER" {} "|| echo {} >> $FAILED_LOG" < "$IMAGE_LINKS_FILE"

# 提示信息
echo "Download completed. Failed downloads (if any) are saved in $FAILED_LOG."
