/* WordPress 部署管理系统 - 组件样式 */

/* 导航栏 */
.navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: var(--white);
    height: var(--navbar-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.nav-logo {
    height: 32px;
    width: auto;
}

.nav-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-2);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.nav-link:hover {
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

.nav-link.active {
    color: var(--white);
    background: rgba(255, 255, 255, 0.2);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--danger-color);
    color: var(--white);
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    display: none;
}

/* 主内容区域 */
.main-content {
    margin-top: var(--navbar-height);
    padding: var(--spacing-6);
    min-height: calc(100vh - var(--navbar-height));
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 页面头部 */
.page-header {
    margin-bottom: var(--spacing-8);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-4);
}

.page-header h1 {
    margin-bottom: var(--spacing-2);
}

.page-header p {
    color: var(--gray-600);
    margin-bottom: 0;
}

.page-actions {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
}

.stat-primary { background: var(--primary-color); }
.stat-success { background: var(--success-color); }
.stat-warning { background: var(--warning-color); }
.stat-danger { background: var(--danger-color); }
.stat-info { background: var(--info-color); }

.stat-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    color: var(--gray-800);
}

.stat-content p {
    color: var(--gray-600);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

/* 卡片组件 */
.dashboard-card,
.card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-5);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--gray-800);
}

.card-actions {
    display: flex;
    gap: var(--spacing-2);
}

.card-content {
    padding: var(--spacing-5);
}

/* 系统状态 */
.system-status {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.status-label {
    font-weight: 500;
    color: var(--gray-700);
}

.status-indicator {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-running {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-paused {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-error {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.status-value {
    font-weight: 600;
    color: var(--gray-800);
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.data-table th {
    background: var(--gray-50);
    padding: var(--spacing-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-800);
    border-bottom: 2px solid var(--gray-200);
}

.data-table td {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.data-table tr:hover {
    background: var(--gray-50);
}

/* 状态徽章 */
.status-badge {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-failed {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.status-running {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-cancelled {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* 进度条 */
.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-base);
}

.progress-text {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--gray-600);
    min-width: 35px;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: var(--spacing-1);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-6);
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-1);
}

/* 表单组件 */
.form-group {
    margin-bottom: var(--spacing-5);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-xl);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知组件 */
.notification-container {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-3);
    padding: var(--spacing-4);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    transform: translateX(100%);
    transition: transform var(--transition-base);
    border-left: 4px solid var(--info-color);
}

.notification.show {
    transform: translateX(0);
}

.notification.hide {
    transform: translateX(100%);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-content {
    flex: 1;
}

.notification-message {
    font-weight: 500;
    margin-bottom: var(--spacing-1);
}

.notification-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    font-size: 16px;
}

.notification-close:hover {
    color: var(--gray-600);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .main-content {
        padding: var(--spacing-4);
    }
}
