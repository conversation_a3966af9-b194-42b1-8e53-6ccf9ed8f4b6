<?php
/**
 * 模板管理API接口
 * 处理WordPress模板上传、管理相关的API请求
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 安全检查
checkApiAccess();

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $templateManager = new TemplateManager();
    
    switch ($action) {
        case 'upload':
            handleUploadTemplate($templateManager);
            break;
            
        case 'list':
            handleListTemplates($templateManager);
            break;
            
        case 'get':
            handleGetTemplate($templateManager);
            break;
            
        case 'update':
            handleUpdateTemplate($templateManager);
            break;
            
        case 'delete':
            handleDeleteTemplate($templateManager);
            break;
            
        case 'test':
            handleTestTemplate($templateManager);
            break;
            
        case 'download':
            handleDownloadTemplate($templateManager);
            break;
            
        case 'get_usage_stats':
            handleGetUsageStats($templateManager);
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Template API error: ' . $e->getMessage(), [
        'action' => $action ?? 'unknown',
        'request_data' => $_POST
    ]);
    errorResponse($e->getMessage(), 500);
}

/**
 * 处理模板上传
 */
function handleUploadTemplate($templateManager) {
    // 检查文件上传
    if (!isset($_FILES['template_file'])) {
        errorResponse('No file uploaded');
    }
    
    $fileInfo = $_FILES['template_file'];
    
    // 获取模板信息
    $templateData = [
        'name' => sanitizeInput($_POST['name'] ?? ''),
        'description' => sanitizeInput($_POST['description'] ?? ''),
        'mother_domain' => sanitizeInput($_POST['mother_domain'] ?? ''),
        'mother_db' => sanitizeInput($_POST['mother_db'] ?? ''),
        'mysql_root_pass' => $_POST['mysql_root_pass'] ?? DEFAULT_MYSQL_ROOT_PASS
    ];
    
    // 验证必填字段
    if (empty($templateData['name'])) {
        errorResponse('Template name is required');
    }
    
    if (empty($templateData['mother_domain'])) {
        errorResponse('Mother domain is required');
    }
    
    if (empty($templateData['mother_db'])) {
        errorResponse('Mother database is required');
    }
    
    try {
        $result = $templateManager->uploadTemplate($fileInfo, $templateData);
        
        successResponse($result, 'Template uploaded successfully');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取模板列表
 */
function handleListTemplates($templateManager) {
    $status = sanitizeInput($_GET['status'] ?? 'active');
    
    try {
        $templates = $templateManager->getTemplates($status);
        
        successResponse($templates);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取单个模板信息
 */
function handleGetTemplate($templateManager) {
    $templateId = intval($_GET['template_id'] ?? 0);
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    try {
        $template = $templateManager->getTemplate($templateId);
        
        if (!$template) {
            errorResponse('Template not found', 404);
        }
        
        successResponse($template);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 更新模板信息
 */
function handleUpdateTemplate($templateManager) {
    $templateId = intval($_POST['template_id'] ?? 0);
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    $updateData = [];
    $allowedFields = ['name', 'description', 'mother_domain', 'mother_db', 'mysql_root_pass'];
    
    foreach ($allowedFields as $field) {
        if (isset($_POST[$field])) {
            $updateData[$field] = sanitizeInput($_POST[$field]);
        }
    }
    
    // 处理配置数据
    if (isset($_POST['config_data'])) {
        $configData = $_POST['config_data'];
        if (is_string($configData)) {
            $configData = json_decode($configData, true);
        }
        if (is_array($configData)) {
            $updateData['config_data'] = $configData;
        }
    }
    
    if (empty($updateData)) {
        errorResponse('No valid fields to update');
    }
    
    try {
        $result = $templateManager->updateTemplate($templateId, $updateData);
        
        successResponse($result, 'Template updated successfully');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 删除模板
 */
function handleDeleteTemplate($templateManager) {
    $templateId = intval($_POST['template_id'] ?? 0);
    $purge = filter_var($_POST['purge'] ?? false, FILTER_VALIDATE_BOOLEAN);
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    try {
        if ($purge) {
            $result = $templateManager->purgeTemplate($templateId);
            $message = 'Template purged successfully';
        } else {
            $result = $templateManager->deleteTemplate($templateId);
            $message = 'Template deleted successfully';
        }
        
        successResponse(['template_id' => $templateId], $message);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 测试模板
 */
function handleTestTemplate($templateManager) {
    $templateId = intval($_GET['template_id'] ?? 0);
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    try {
        $result = $templateManager->testTemplate($templateId);
        
        successResponse($result);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 下载模板文件
 */
function handleDownloadTemplate($templateManager) {
    $templateId = intval($_GET['template_id'] ?? 0);
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    try {
        $template = $templateManager->getTemplate($templateId);
        
        if (!$template) {
            errorResponse('Template not found', 404);
        }
        
        $filePath = $template['file_path'];
        
        if (!file_exists($filePath)) {
            errorResponse('Template file not found', 404);
        }
        
        // 设置下载头
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $template['filename'] . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
        
        // 输出文件内容
        readfile($filePath);
        exit;
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取模板使用统计
 */
function handleGetUsageStats($templateManager) {
    $templateId = intval($_GET['template_id'] ?? 0);
    $days = min(365, max(1, intval($_GET['days'] ?? 30)));
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    try {
        $db = getDatabase();
        
        // 获取基础统计
        $stmt = $db->prepare("
            SELECT 
                COUNT(*) as total_deployments,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_deployments,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_deployments,
                AVG(CASE WHEN completed_at IS NOT NULL AND started_at IS NOT NULL 
                    THEN (julianday(completed_at) - julianday(started_at)) * 86400 
                    ELSE NULL END) as avg_duration,
                MAX(created_at) as last_used_at
            FROM deploy_jobs 
            WHERE template_id = ? AND created_at >= datetime('now', '-' || ? || ' days')
        ");
        $stmt->execute([$templateId, $days]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 计算成功率
        if ($stats['total_deployments'] > 0) {
            $stats['success_rate'] = round(($stats['successful_deployments'] / $stats['total_deployments']) * 100, 2);
        } else {
            $stats['success_rate'] = 0;
        }
        
        // 获取每日使用统计
        $stmt = $db->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as deployments,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful
            FROM deploy_jobs 
            WHERE template_id = ? AND created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        ");
        $stmt->execute([$templateId, $days]);
        $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取最近的部署
        $stmt = $db->prepare("
            SELECT domain, status, created_at, completed_at
            FROM deploy_jobs 
            WHERE template_id = ? 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$templateId]);
        $recentDeployments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        successResponse([
            'stats' => $stats,
            'daily_stats' => $dailyStats,
            'recent_deployments' => $recentDeployments,
            'period_days' => $days
        ]);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 处理批量模板操作
 */
function handleBatchTemplateOperation() {
    $operation = sanitizeInput($_POST['operation'] ?? '');
    $templateIds = $_POST['template_ids'] ?? [];
    
    if (!is_array($templateIds) || empty($templateIds)) {
        errorResponse('Template IDs are required');
    }
    
    $templateIds = array_map('intval', $templateIds);
    $templateIds = array_filter($templateIds, function($id) { return $id > 0; });
    
    if (empty($templateIds)) {
        errorResponse('Valid template IDs are required');
    }
    
    $templateManager = new TemplateManager();
    $results = [];
    $errors = [];
    
    try {
        foreach ($templateIds as $templateId) {
            try {
                switch ($operation) {
                    case 'delete':
                        $templateManager->deleteTemplate($templateId);
                        $results[] = $templateId;
                        break;
                        
                    case 'test':
                        $testResult = $templateManager->testTemplate($templateId);
                        $results[$templateId] = $testResult;
                        break;
                        
                    default:
                        throw new Exception("Unsupported operation: $operation");
                }
            } catch (Exception $e) {
                $errors[$templateId] = $e->getMessage();
            }
        }
        
        successResponse([
            'operation' => $operation,
            'results' => $results,
            'errors' => $errors,
            'success_count' => count($results),
            'error_count' => count($errors)
        ]);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}
