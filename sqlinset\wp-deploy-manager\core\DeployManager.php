<?php
/**
 * 部署管理器
 * 负责WordPress站点的部署逻辑
 */

class DeployManager {
    private $db;
    private $logger;
    private $queueManager;
    
    public function __construct() {
        $this->db = getDatabase();
        $this->logger = new LogManager();
        $this->queueManager = new QueueManager();
    }
    
    /**
     * 创建单域名部署任务
     */
    public function createSingleDeployJob($domain, $templateId, $config = []) {
        try {
            $jobUuid = generateUUID();
            
            // 验证域名
            if (!validateDomain($domain)) {
                throw new Exception("Invalid domain: $domain");
            }
            
            // 验证模板
            $template = $this->getTemplate($templateId);
            if (!$template) {
                throw new Exception("Template not found: $templateId");
            }
            
            // 检查域名是否已存在
            if ($this->isDomainExists($domain)) {
                throw new Exception("Domain already exists: $domain");
            }
            
            // 准备配置数据
            $configData = array_merge([
                'domain' => $domain,
                'template_id' => $templateId,
                'auto_ssl' => true,
                'backup_before_deploy' => true,
                'health_check_after_deploy' => true
            ], $config);
            
            // 创建部署任务
            $stmt = $this->db->prepare("
                INSERT INTO deploy_jobs (uuid, type, template_id, domain, config_data, total_steps)
                VALUES (?, 'single', ?, ?, ?, 7)
            ");
            
            $stmt->execute([
                $jobUuid,
                $templateId,
                $domain,
                json_encode($configData)
            ]);
            
            $jobId = $this->db->lastInsertId();
            
            $this->logger->log('INFO', "Created single deploy job", [
                'job_id' => $jobId,
                'job_uuid' => $jobUuid,
                'domain' => $domain,
                'template_id' => $templateId
            ]);
            
            // 添加到队列
            $this->queueManager->addJob($jobId);
            
            return [
                'job_id' => $jobId,
                'job_uuid' => $jobUuid,
                'domain' => $domain,
                'status' => 'pending'
            ];
            
        } catch (Exception $e) {
            $this->logger->log('ERROR', "Failed to create single deploy job", [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 创建批量部署任务
     */
    public function createBatchDeployJob($domains, $templateId, $config = []) {
        try {
            $jobUuid = generateUUID();
            $validDomains = [];
            
            // 验证所有域名
            foreach ($domains as $domain) {
                $domain = trim($domain);
                if (empty($domain) || strpos($domain, '#') === 0) {
                    continue; // 跳过空行和注释行
                }
                
                if (!validateDomain($domain)) {
                    throw new Exception("Invalid domain: $domain");
                }
                
                if ($this->isDomainExists($domain)) {
                    $this->logger->log('WARNING', "Domain already exists, skipping", ['domain' => $domain]);
                    continue;
                }
                
                $validDomains[] = $domain;
            }
            
            if (empty($validDomains)) {
                throw new Exception("No valid domains provided");
            }
            
            // 验证模板
            $template = $this->getTemplate($templateId);
            if (!$template) {
                throw new Exception("Template not found: $templateId");
            }
            
            // 准备配置数据
            $configData = array_merge([
                'domains' => $validDomains,
                'template_id' => $templateId,
                'parallel_limit' => min(count($validDomains), MAX_CONCURRENT_DEPLOYMENTS),
                'auto_ssl' => true,
                'backup_before_deploy' => true,
                'health_check_after_deploy' => true
            ], $config);
            
            // 创建主任务
            $stmt = $this->db->prepare("
                INSERT INTO deploy_jobs (uuid, type, template_id, config_data, total_steps)
                VALUES (?, 'batch', ?, ?, ?)
            ");
            
            $stmt->execute([
                $jobUuid,
                $templateId,
                json_encode($configData),
                count($validDomains)
            ]);
            
            $jobId = $this->db->lastInsertId();
            
            // 创建子任务
            $subJobStmt = $this->db->prepare("
                INSERT INTO deploy_sub_jobs (parent_job_id, domain, template_id, config_data)
                VALUES (?, ?, ?, ?)
            ");
            
            foreach ($validDomains as $domain) {
                $subConfig = array_merge($configData, ['domain' => $domain]);
                $subJobStmt->execute([
                    $jobId,
                    $domain,
                    $templateId,
                    json_encode($subConfig)
                ]);
            }
            
            $this->logger->log('INFO', "Created batch deploy job", [
                'job_id' => $jobId,
                'job_uuid' => $jobUuid,
                'domain_count' => count($validDomains),
                'template_id' => $templateId
            ]);
            
            // 添加到队列
            $this->queueManager->addJob($jobId);
            
            return [
                'job_id' => $jobId,
                'job_uuid' => $jobUuid,
                'domain_count' => count($validDomains),
                'status' => 'pending'
            ];
            
        } catch (Exception $e) {
            $this->logger->log('ERROR', "Failed to create batch deploy job", [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 执行部署任务
     */
    public function executeJob($jobId) {
        try {
            $job = $this->getJob($jobId);
            if (!$job) {
                throw new Exception("Job not found: $jobId");
            }
            
            if ($job['status'] !== 'pending') {
                throw new Exception("Job is not in pending status: " . $job['status']);
            }
            
            // 更新任务状态为运行中
            $this->updateJobStatus($jobId, 'running');
            
            if ($job['type'] === 'single') {
                return $this->executeSingleJob($jobId, $job);
            } else {
                return $this->executeBatchJob($jobId, $job);
            }
            
        } catch (Exception $e) {
            $this->updateJobStatus($jobId, 'failed', $e->getMessage());
            $this->logger->log('ERROR', "Job execution failed", [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 执行单域名部署
     */
    private function executeSingleJob($jobId, $job) {
        $config = json_decode($job['config_data'], true);
        $domain = $config['domain'];
        $templateId = $job['template_id'];
        
        $this->logger->log('INFO', "Starting single deployment", [
            'job_id' => $jobId,
            'domain' => $domain
        ]);
        
        try {
            // 步骤1: 准备部署环境
            $this->updateJobProgress($jobId, 10, '准备部署环境');
            $this->prepareDeploymentEnvironment($domain, $config);
            
            // 步骤2: 获取模板信息
            $this->updateJobProgress($jobId, 20, '获取模板信息');
            $template = $this->getTemplate($templateId);
            
            // 步骤3: 生成部署配置
            $this->updateJobProgress($jobId, 30, '生成部署配置');
            $deployConfig = $this->generateDeployConfig($domain, $template, $config);
            
            // 步骤4: 执行原始部署脚本
            $this->updateJobProgress($jobId, 50, '执行部署脚本');
            $result = $this->executeOriginalScript($deployConfig);
            
            // 步骤5: 后处理优化
            $this->updateJobProgress($jobId, 70, '后处理优化');
            $this->postDeploymentOptimization($domain, $config);
            
            // 步骤6: 健康检查
            $this->updateJobProgress($jobId, 90, '健康检查');
            $healthResult = $this->performHealthCheck($domain);
            
            // 步骤7: 完成部署
            $this->updateJobProgress($jobId, 100, '部署完成');
            $this->updateJobStatus($jobId, 'completed');
            
            // 记录部署历史
            $this->recordDeployHistory($jobId, $domain, $templateId, 'completed');
            
            $this->logger->log('INFO', "Single deployment completed", [
                'job_id' => $jobId,
                'domain' => $domain,
                'health_score' => $healthResult['score'] ?? 0
            ]);
            
            return [
                'status' => 'completed',
                'domain' => $domain,
                'health_result' => $healthResult
            ];
            
        } catch (Exception $e) {
            $this->updateJobStatus($jobId, 'failed', $e->getMessage());
            $this->recordDeployHistory($jobId, $domain, $templateId, 'failed');
            throw $e;
        }
    }
    
    /**
     * 执行批量部署
     */
    private function executeBatchJob($jobId, $job) {
        $config = json_decode($job['config_data'], true);
        $domains = $config['domains'];
        $parallelLimit = $config['parallel_limit'] ?? 3;
        
        $this->logger->log('INFO', "Starting batch deployment", [
            'job_id' => $jobId,
            'domain_count' => count($domains),
            'parallel_limit' => $parallelLimit
        ]);
        
        try {
            $completed = 0;
            $failed = 0;
            $total = count($domains);
            
            // 获取子任务
            $subJobs = $this->getSubJobs($jobId);
            
            // 并行处理子任务
            $running = [];
            $pending = $subJobs;
            
            while (!empty($pending) || !empty($running)) {
                // 启动新任务（在并发限制内）
                while (count($running) < $parallelLimit && !empty($pending)) {
                    $subJob = array_shift($pending);
                    $pid = $this->startSubJobProcess($subJob);
                    if ($pid) {
                        $running[$pid] = $subJob;
                    }
                }
                
                // 检查运行中的任务
                foreach ($running as $pid => $subJob) {
                    $status = $this->checkProcessStatus($pid);
                    if ($status !== 'running') {
                        unset($running[$pid]);
                        
                        if ($status === 'completed') {
                            $completed++;
                        } else {
                            $failed++;
                        }
                        
                        // 更新主任务进度
                        $progress = (($completed + $failed) / $total) * 100;
                        $this->updateJobProgress($jobId, $progress, "已完成: $completed, 失败: $failed");
                    }
                }
                
                // 短暂休眠避免CPU占用过高
                usleep(500000); // 0.5秒
            }
            
            // 更新最终状态
            $finalStatus = $failed === 0 ? 'completed' : ($completed > 0 ? 'partial' : 'failed');
            $this->updateJobStatus($jobId, $finalStatus);
            
            $this->logger->log('INFO', "Batch deployment completed", [
                'job_id' => $jobId,
                'total' => $total,
                'completed' => $completed,
                'failed' => $failed,
                'status' => $finalStatus
            ]);
            
            return [
                'status' => $finalStatus,
                'total' => $total,
                'completed' => $completed,
                'failed' => $failed
            ];
            
        } catch (Exception $e) {
            $this->updateJobStatus($jobId, 'failed', $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成部署配置
     */
    private function generateDeployConfig($domain, $template, $config) {
        // 生成数据库配置
        $dbName = str_replace(['.', '-'], '_', $domain);
        $dbUser = $dbName;
        $dbPass = bin2hex(random_bytes(8));
        $email = $config['admin_email'] ?? "admin@$domain";
        
        return [
            'domain' => $domain,
            'db_name' => $dbName,
            'db_user' => $dbUser,
            'db_pass' => $dbPass,
            'email' => $email,
            'template_path' => $template['file_path'],
            'mother_domain' => $template['mother_domain'],
            'mother_db' => $template['mother_db'],
            'mysql_root_pass' => $template['mysql_root_pass']
        ];
    }
    
    /**
     * 执行原始部署脚本
     */
    private function executeOriginalScript($config) {
        $tempFile = tempnam(sys_get_temp_dir(), 'deploy_config_');
        $configLine = sprintf(
            "%s %s %s %s %s",
            $config['domain'],
            $config['db_name'],
            $config['db_user'],
            $config['db_pass'],
            $config['email']
        );
        
        file_put_contents($tempFile, $configLine);
        
        try {
            $command = sprintf(
                'cd %s && %s < %s 2>&1',
                dirname(ORIGINAL_DEPLOY_SCRIPT),
                ORIGINAL_DEPLOY_SCRIPT,
                $tempFile
            );
            
            $output = shell_exec($command);
            $exitCode = 0; // shell_exec不返回退出码，需要其他方式检查
            
            if (strpos($output, 'ERROR') !== false || strpos($output, 'Failed') !== false) {
                throw new Exception("Deployment script failed: " . $output);
            }
            
            return [
                'success' => true,
                'output' => $output
            ];
            
        } finally {
            unlink($tempFile);
        }
    }
    
    // 其他辅助方法...
    private function getTemplate($templateId) {
        $stmt = $this->db->prepare("SELECT * FROM templates WHERE id = ? AND status = 'active'");
        $stmt->execute([$templateId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getJob($jobId) {
        $stmt = $this->db->prepare("SELECT * FROM deploy_jobs WHERE id = ?");
        $stmt->execute([$jobId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function updateJobStatus($jobId, $status, $errorMessage = null) {
        $sql = "UPDATE deploy_jobs SET status = ?, updated_at = CURRENT_TIMESTAMP";
        $params = [$status];
        
        if ($status === 'running') {
            $sql .= ", started_at = CURRENT_TIMESTAMP";
        } elseif (in_array($status, ['completed', 'failed', 'cancelled'])) {
            $sql .= ", completed_at = CURRENT_TIMESTAMP";
        }
        
        if ($errorMessage) {
            $sql .= ", error_message = ?";
            $params[] = $errorMessage;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $jobId;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
    }
    
    private function updateJobProgress($jobId, $progress, $currentStep = null) {
        $sql = "UPDATE deploy_jobs SET progress = ?, updated_at = CURRENT_TIMESTAMP";
        $params = [$progress];
        
        if ($currentStep) {
            $sql .= ", current_step = ?";
            $params[] = $currentStep;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $jobId;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
    }
    
    private function isDomainExists($domain) {
        return is_dir("/www/wwwroot/$domain");
    }
    
    private function prepareDeploymentEnvironment($domain, $config) {
        // 创建必要的目录和设置权限
        // 这里可以添加环境准备逻辑
    }
    
    private function postDeploymentOptimization($domain, $config) {
        // 部署后优化逻辑
        // 如SSL设置、缓存配置等
    }
    
    private function performHealthCheck($domain) {
        // 执行健康检查
        return ['score' => 100, 'status' => 'healthy'];
    }
    
    private function recordDeployHistory($jobId, $domain, $templateId, $status) {
        $stmt = $this->db->prepare("
            INSERT INTO deploy_history (job_id, domain, template_id, status)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$jobId, $domain, $templateId, $status]);
    }
    
    private function getSubJobs($jobId) {
        $stmt = $this->db->prepare("SELECT * FROM deploy_sub_jobs WHERE parent_job_id = ? ORDER BY id");
        $stmt->execute([$jobId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function startSubJobProcess($subJob) {
        // 启动子进程处理子任务
        // 返回进程ID
        return null; // 简化实现
    }
    
    private function checkProcessStatus($pid) {
        // 检查进程状态
        return 'completed'; // 简化实现
    }
}
