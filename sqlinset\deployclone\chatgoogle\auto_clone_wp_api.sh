#!/bin/bash

# ========== 配置区 ==========
BT_PANEL="http://74.50.75.82:35027"
BT_KEY="NV59tN5dzWzOBTJkwIhig51dTJQTXDSz"
MOTHER_DOMAIN="astra.nestlyalli.shop"
MOTHER_DIR="/www/wwwroot/astra.nestlyalli.shop"
MOTHER_DB="astra_nestlyalli"
MOTHER_DB_USER="astra_nestlyalli"
MOTHER_DB_PASS="z4NpCkFiJGS2asCd"
MYSQL_ROOT_PASS="2KHNS4XSsMBLZJ7B"
PHP_VERSION="81"  # 8.1
# ===========================

NEW_DOMAIN=$1
if [ -z "$NEW_DOMAIN" ]; then
  echo "用法: $0 newsite.com"
  exit 1
fi

echo "=============================="
echo "正在部署: $NEW_DOMAIN"

# 1. 用API创建网站
echo "正在通过宝塔API创建网站..."
SITE_JSON=$(curl -ks -X POST "$BT_PANEL/api/site/add" \
  -d "domain=$NEW_DOMAIN&path=/www/wwwroot/$NEW_DOMAIN&php_version=$PHP_VERSION&access_key=$BT_KEY")
WEBROOT="/www/wwwroot/$NEW_DOMAIN"
echo "API返回: $SITE_JSON"

# 检查目录是否存在，不存在则手动创建
if [ ! -d "$WEBROOT" ]; then
  echo "网站根目录 $WEBROOT 不存在，自动创建..."
  mkdir -p "$WEBROOT"
fi

# 2. 用API创建数据库
NEW_DB="wp_${NEW_DOMAIN//./_}"
NEW_DB_USER="wpuser_${NEW_DOMAIN//./_}"
NEW_DB_PASS=$(openssl rand -base64 12)
echo "正在通过宝塔API创建数据库..."
DB_JSON=$(curl -ks -X POST "$BT_PANEL/api/database/add" \
  -d "name=$NEW_DB&username=$NEW_DB_USER&password=$NEW_DB_PASS&access_key=$BT_KEY")
echo "API返回: $DB_JSON"

# 3. 复制母站文件
if [ ! -d "$MOTHER_DIR" ]; then
  echo "母站目录 $MOTHER_DIR 不存在，请检查！"
  exit 1
fi
echo "正在复制母站文件..."
cp -a $MOTHER_DIR/. $WEBROOT/

# 4. 导出/导入数据库
echo "正在导出母站数据库..."
mysqldump -u root -p"$MYSQL_ROOT_PASS" $MOTHER_DB > /tmp/tmp.sql
echo "正在导入新数据库..."
mysql -u root -p"$MYSQL_ROOT_PASS" $NEW_DB < /tmp/tmp.sql

# 5. 修改wp-config.php
if [ ! -f "$WEBROOT/wp-config.php" ]; then
  echo "新站点 $WEBROOT/wp-config.php 不存在，复制失败！"
  exit 1
fi
echo "正在修改wp-config.php..."
sed -i "s|'DB_NAME', '.*'|'DB_NAME', '$NEW_DB'|g" $WEBROOT/wp-config.php
sed -i "s|'DB_USER', '.*'|'DB_USER', '$NEW_DB_USER'|g" $WEBROOT/wp-config.php
ESCAPED_PASS=$(printf '%s\n' "$NEW_DB_PASS" | sed 's/[&/\]/\\&/g')
sed -i "s|'DB_PASSWORD', '.*'|'DB_PASSWORD', '$ESCAPED_PASS'|g" $WEBROOT/wp-config.php

# 6. WP-CLI批量替换域名
if ! command -v wp &> /dev/null; then
  echo "WP-CLI 未安装，请先安装 WP-CLI！"
  exit 1
fi
echo "正在用WP-CLI批量替换域名..."
wp --path=$WEBROOT search-replace $MOTHER_DOMAIN $NEW_DOMAIN --all-tables --allow-root

# 7. 权限
chown -R www:www $WEBROOT

echo "新站点 $NEW_DOMAIN 部署完成！"
echo "数据库名: $NEW_DB"
echo "数据库用户: $NEW_DB_USER"
echo "数据库密码: $NEW_DB_PASS"
echo "=============================="