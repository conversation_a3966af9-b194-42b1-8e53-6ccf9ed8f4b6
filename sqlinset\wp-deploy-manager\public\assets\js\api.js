/**
 * API工具类
 * 处理与后端API的通信
 */

class API {
    constructor() {
        this.baseUrl = '/api';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 发送HTTP请求
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            ...options
        };

        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'API request failed');
            }

            return data;
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.request(url.pathname + url.search, {
            method: 'GET'
        });
    }

    /**
     * POST请求
     */
    async post(endpoint, data = {}) {
        const body = data instanceof FormData ? data : JSON.stringify(data);
        
        return this.request(endpoint, {
            method: 'POST',
            body
        });
    }

    /**
     * PUT请求
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'DELETE',
            body: JSON.stringify(data)
        });
    }

    // ==================== 部署相关API ====================

    /**
     * 创建单域名部署任务
     */
    async createSingleDeploy(domain, templateId, config = {}) {
        return this.post('/deploy.php?action=single_deploy', {
            domain,
            template_id: templateId,
            config
        });
    }

    /**
     * 创建批量部署任务
     */
    async createBatchDeploy(domains, templateId, config = {}) {
        return this.post('/deploy.php?action=batch_deploy', {
            domains,
            template_id: templateId,
            config
        });
    }

    /**
     * 获取任务状态
     */
    async getJobStatus(jobId, jobUuid = null) {
        const params = jobId ? { job_id: jobId } : { job_uuid: jobUuid };
        return this.get('/deploy.php?action=get_job_status', params);
    }

    /**
     * 获取任务进度
     */
    async getJobProgress(jobId, includeLogs = false) {
        return this.get('/deploy.php?action=get_job_progress', {
            job_id: jobId,
            include_logs: includeLogs
        });
    }

    /**
     * 取消任务
     */
    async cancelJob(jobId) {
        return this.post('/deploy.php?action=cancel_job', {
            job_id: jobId
        });
    }

    /**
     * 重试任务
     */
    async retryJob(jobId) {
        return this.post('/deploy.php?action=retry_job', {
            job_id: jobId
        });
    }

    /**
     * 获取部署历史
     */
    async getDeployHistory(filters = {}) {
        return this.get('/deploy.php?action=get_deploy_history', filters);
    }

    /**
     * 获取部署统计
     */
    async getDeployStats(days = 30) {
        return this.get('/deploy.php?action=get_deploy_stats', { days });
    }

    // ==================== 模板相关API ====================

    /**
     * 上传模板
     */
    async uploadTemplate(formData) {
        return this.post('/templates.php?action=upload', formData);
    }

    /**
     * 获取模板列表
     */
    async getTemplates(status = 'active') {
        return this.get('/templates.php?action=list', { status });
    }

    /**
     * 获取单个模板
     */
    async getTemplate(templateId) {
        return this.get('/templates.php?action=get', { template_id: templateId });
    }

    /**
     * 更新模板
     */
    async updateTemplate(templateId, data) {
        return this.post('/templates.php?action=update', {
            template_id: templateId,
            ...data
        });
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId, purge = false) {
        return this.post('/templates.php?action=delete', {
            template_id: templateId,
            purge
        });
    }

    /**
     * 测试模板
     */
    async testTemplate(templateId) {
        return this.get('/templates.php?action=test', { template_id: templateId });
    }

    /**
     * 获取模板使用统计
     */
    async getTemplateUsageStats(templateId, days = 30) {
        return this.get('/templates.php?action=get_usage_stats', {
            template_id: templateId,
            days
        });
    }

    // ==================== 状态和监控API ====================

    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        return this.get('/status.php?action=system');
    }

    /**
     * 获取队列状态
     */
    async getQueueStatus() {
        return this.get('/status.php?action=queue');
    }

    /**
     * 暂停/恢复队列
     */
    async toggleQueue(action) {
        return this.post('/status.php?action=toggle_queue', { action });
    }

    /**
     * 获取站点健康状态
     */
    async getSitesHealth() {
        return this.get('/status.php?action=sites_health');
    }

    /**
     * 执行健康检查
     */
    async runHealthCheck(domain = null) {
        return this.post('/status.php?action=run_health_check', { domain });
    }

    // ==================== 日志相关API ====================

    /**
     * 获取系统日志
     */
    async getLogs(filters = {}) {
        return this.get('/logs.php?action=get_logs', filters);
    }

    /**
     * 获取任务日志
     */
    async getJobLogs(jobId) {
        return this.get('/logs.php?action=get_job_logs', { job_id: jobId });
    }

    /**
     * 获取日志统计
     */
    async getLogStats(days = 7) {
        return this.get('/logs.php?action=get_stats', { days });
    }

    /**
     * 导出日志
     */
    async exportLogs(filters = {}, format = 'json') {
        return this.get('/logs.php?action=export', { ...filters, format });
    }

    /**
     * 清理日志
     */
    async cleanupLogs(days = 30) {
        return this.post('/logs.php?action=cleanup', { days });
    }

    // ==================== 设置相关API ====================

    /**
     * 获取系统设置
     */
    async getSettings() {
        return this.get('/settings.php?action=get');
    }

    /**
     * 更新系统设置
     */
    async updateSettings(settings) {
        return this.post('/settings.php?action=update', settings);
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        return this.post('/settings.php?action=reset');
    }

    // ==================== 实时更新相关 ====================

    /**
     * 创建Server-Sent Events连接
     */
    createEventSource(endpoint, params = {}) {
        const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        return new EventSource(url);
    }

    /**
     * 监听任务进度
     */
    watchJobProgress(jobId, onProgress, onComplete, onError) {
        const eventSource = this.createEventSource('/sse.php?action=job_progress', {
            job_id: jobId
        });

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                if (data.type === 'progress') {
                    onProgress(data.data);
                } else if (data.type === 'complete') {
                    onComplete(data.data);
                    eventSource.close();
                }
            } catch (error) {
                onError(error);
            }
        };

        eventSource.onerror = (error) => {
            onError(error);
            eventSource.close();
        };

        return eventSource;
    }

    /**
     * 监听系统状态
     */
    watchSystemStatus(onUpdate, onError) {
        const eventSource = this.createEventSource('/sse.php?action=system_status');

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                onUpdate(data);
            } catch (error) {
                onError(error);
            }
        };

        eventSource.onerror = (error) => {
            onError(error);
        };

        return eventSource;
    }
}

// 创建全局API实例
window.api = new API();

// 导出API类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
