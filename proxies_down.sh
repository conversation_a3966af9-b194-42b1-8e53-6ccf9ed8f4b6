#!/bin/bash

# 检查参数是否为空
if [ $# -ne 3 ]; then
    echo "Usage: $0 <image_links_file> <target_folder> <proxy_file>"
    echo "Proxy file format: IP:PORT:USERNAME:PASSWORD (one per line)"
    exit 1
fi

# 获取输入参数
image_links_file="$1"
target_folder="$2"
proxy_file="$3"

# --- 配置项 ---
PARALLEL_JOBS=2
MIN_SLEEP=2
MAX_SLEEP=5
CURL_RETRIES_PER_PROXY=1 # curl 对单个代理的重试次数 (在切换代理前)
CURL_RETRY_DELAY=5
CURL_CONNECT_TIMEOUT=20 # 连接代理和目标服务器的超时可能需要更长
CURL_MAX_TIME=180
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
MIN_VALID_IMAGE_SIZE=1024 # 1KB
# ---

# 创建目标文件夹
base_download_dir="$target_folder/www/imagerdown/imagedownload"
mkdir -p "$base_download_dir"
echo "Base directory created/ensured: $base_download_dir"

# 失败链接记录文件
failed_links_file="${image_links_file%.*}_failed.txt"
> "$failed_links_file" # 清空或创建失败文件

# 读取代理列表到数组
declare -a proxies
if [ -f "$proxy_file" ]; then
    readarray -t proxies < "$proxy_file"
else
    echo "ERROR: Proxy file '$proxy_file' not found."
    exit 1
fi

if [ ${#proxies[@]} -eq 0 ]; then
    echo "WARNING: Proxy file '$proxy_file' is empty. Will attempt direct downloads."
fi
export -a proxies # 导出数组，使其在 parallel 的子 shell 中可用

# 定义下载函数
download_image() {
    local url="$1"
    local base_dir="$2"
    # proxies 数组现在是全局导出的

    local original_filename
    original_filename=$(basename "$url")
    original_filename="${original_filename%%\?*}"
    original_filename=$(echo "$original_filename" | sed 's/[^a-zA-Z0-9._-]/_/g')
    local url_hash
    url_hash=$(echo -n "$url" | sha1sum | awk '{print $1}')
    local new_filename="${url_hash}-${original_filename}"
    local target_file_path="$base_dir/$new_filename"

    if [ -f "$target_file_path" ]; then
        echo "INFO: [SUCCESS-SKIPPED] File $new_filename (from $url) already exists."
        return 0
    fi

    local attempt_successful=false
    local proxies_count=${#proxies[@]}
    local proxies_to_try=("${proxies[@]}") # 本地副本，或者直接使用全局的
    
    # 如果没有代理，尝试一次直接下载
    if [ "$proxies_count" -eq 0 ]; then
        proxies_to_try+=("DIRECT") # 添加一个特殊标记用于直接下载
    fi

    for proxy_spec in "${proxies_to_try[@]}"; do
        local proxy_ip proxy_port proxy_user proxy_pass
        local curl_proxy_opts=() # 初始化为空数组

        if [ "$proxy_spec" == "DIRECT" ]; then
            echo "INFO: [ATTEMPTING DIRECT] $url"
        else
            # 解析代理字符串 IP:PORT:USER:PASS
            IFS=':' read -r proxy_ip proxy_port proxy_user proxy_pass <<< "$proxy_spec"
            if [ -z "$proxy_ip" ] || [ -z "$proxy_port" ]; then
                echo "WARNING: [INVALID PROXY] Skipping malformed proxy spec: $proxy_spec for $url"
                continue
            fi
            curl_proxy_opts=(--socks5-hostname "$proxy_ip:$proxy_port")
            if [ -n "$proxy_user" ] && [ -n "$proxy_pass" ]; then
                curl_proxy_opts+=("--proxy-user" "$proxy_user:$proxy_pass")
            fi
            echo "INFO: [ATTEMPTING PROXY $proxy_ip:$proxy_port] $url"
        fi

        local sleep_time
        sleep_time=$(shuf -i "$MIN_SLEEP"-"$MAX_SLEEP" -n 1)
        echo "INFO: Sleeping for ${sleep_time}s before download via ${proxy_spec:-DIRECT}"
        sleep "$sleep_time"

        local temp_download_file="${target_file_path}.part"
        
        curl_exit_code=1 # Default to failure
        # shellcheck disable=SC2086 # We want word splitting for $curl_proxy_opts
        curl -L -f -sS \
             --retry "$CURL_RETRIES_PER_PROXY" \
             --retry-delay "$CURL_RETRY_DELAY" \
             --connect-timeout "$CURL_CONNECT_TIMEOUT" \
             --max-time "$CURL_MAX_TIME" \
             -A "$USER_AGENT" \
             "${curl_proxy_opts[@]}" \
             -o "$temp_download_file" \
             "$url"
        curl_exit_code=$?

        if [ $curl_exit_code -eq 0 ]; then
            local file_size
            file_size=$(stat -c%s "$temp_download_file" 2>/dev/null || echo 0)

            if [ "$file_size" -lt "$MIN_VALID_IMAGE_SIZE" ]; then
                echo "WARNING: [TOO_SMALL via ${proxy_spec:-DIRECT}] $temp_download_file for $url ($file_size bytes). Possible error page. Deleting."
                local actual_type_small=$(file -b --mime-type "$temp_download_file" 2>/dev/null || echo "unknown")
                rm -f "$temp_download_file"
                # 不立即将 URL 标记为失败，继续尝试下一个代理
                continue # 继续下一个代理
            fi

            if command -v identify >/dev/null 2>&1; then
                if identify "$temp_download_file" &>/dev/null; then
                    mv "$temp_download_file" "$target_file_path"
                    echo "SUCCESS: [VIA ${proxy_spec:-DIRECT}] Downloaded and verified $url as $new_filename"
                    attempt_successful=true
                    break # 成功，跳出代理循环
                else
                    local actual_type=$(file -b --mime-type "$temp_download_file" 2>/dev/null || echo "unknown")
                    echo "ERROR: [IDENTIFY_FAILED via ${proxy_spec:-DIRECT}] $temp_download_file for $url (type: $actual_type). Deleting."
                    rm -f "$temp_download_file"
                    # 不立即将 URL 标记为失败，继续尝试下一个代理
                    continue # 继续下一个代理
                fi
            else # identify 未安装
                echo "WARNING: [NO_IDENTIFY via ${proxy_spec:-DIRECT}] 'identify' not found. Basic MIME check for $url."
                local file_type_fallback
                file_type_fallback=$(file -b --mime-type "$temp_download_file" 2>/dev/null || echo "unknown")
                if [[ "$file_type_fallback" == "image/"* ]]; then
                    mv "$temp_download_file" "$target_file_path"
                    echo "SUCCESS: [MIME_OK VIA ${proxy_spec:-DIRECT}] Downloaded $url as $new_filename"
                    attempt_successful=true
                    break # 成功，跳出代理循环
                else
                    echo "ERROR: [BAD_MIME via ${proxy_spec:-DIRECT}] File $temp_download_file for $url (type: $file_type_fallback). Deleting."
                    rm -f "$temp_download_file"
                    # 不立即将 URL 标记为失败，继续尝试下一个代理
                    continue # 继续下一个代理
                fi
            fi
        else
            echo "ERROR: [CURL_ERROR $curl_exit_code via ${proxy_spec:-DIRECT}] Failed to download $url. Trying next proxy if available."
            rm -f "$temp_download_file" # 清理部分下载的文件
            # curl 失败，继续下一个代理
            continue
        fi
    done # 结束代理循环

    if [ "$attempt_successful" = true ]; then
        return 0
    else
        echo "FINAL_ERROR: [ALL_PROXIES_FAILED] $url. Adding to $failed_links_file"
        echo "$url #ALL_PROXIES_FAILED_OR_DIRECT_FAILED" >> "$failed_links_file"
        return 1
    fi
}
export -f download_image
export base_download_dir MIN_SLEEP MAX_SLEEP CURL_RETRIES_PER_PROXY CURL_RETRY_DELAY CURL_CONNECT_TIMEOUT CURL_MAX_TIME USER_AGENT failed_links_file MIN_VALID_IMAGE_SIZE

echo "Starting downloads with $PARALLEL_JOBS parallel jobs..."
if [ ${#proxies[@]} -gt 0 ]; then
    echo "Using ${#proxies[@]} proxies from $proxy_file for rotation."
else
    echo "No proxies loaded, attempting direct downloads."
fi

parallel --eta --joblog parallel_joblog.log -j "$PARALLEL_JOBS" download_image {} "$base_download_dir" :::: "$image_links_file"

echo "Download process finished."
echo "Failed links (if any) are saved in: $failed_links_file"
if [ -s "$failed_links_file" ]; then
    echo "Some downloads failed permanently after trying all proxies. Check $failed_links_file"
else
    echo "All attempted downloads completed (check logs for individual success/warnings)."
    # rm -f "$failed_links_file" # 只有当没有失败时才删除
fi