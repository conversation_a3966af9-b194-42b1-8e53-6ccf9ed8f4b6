#!/bin/bash

set -e

echo "🚀 启动WordPress Deploy Manager容器..."

# 等待MySQL启动
echo "等待MySQL服务启动..."
while ! mysqladmin ping -h mysql -u root -pwp_deploy_2024 --silent; do
    echo "等待MySQL连接..."
    sleep 2
done
echo "✅ MySQL连接成功"

# 检查并创建必要目录
echo "创建必要目录..."
mkdir -p /var/www/html/logs
mkdir -p /var/www/html/uploads
mkdir -p /var/www/html/uploads/temp
mkdir -p /var/www/html/uploads/templates

# 设置权限
echo "设置文件权限..."
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html
chmod -R 777 /var/www/html/logs
chmod -R 777 /var/www/html/uploads

# 检查配置文件
echo "检查配置文件..."
if [ ! -f "/var/www/html/config/database.php" ]; then
    echo "创建数据库配置文件..."
    cat > /var/www/html/config/database.php << 'EOF'
<?php
// Docker环境数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);
?>
EOF
fi

if [ ! -f "/var/www/html/config/app.json" ]; then
    echo "创建应用配置文件..."
    cat > /var/www/html/config/app.json << 'EOF'
{
    "mysql_root_password": "wp_deploy_2024",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "<EMAIL>",
    "backup_before_deploy": true,
    "auto_ssl_setup": false,
    "debug_mode": true,
    "log_level": "DEBUG",
    "redis_enabled": true,
    "redis_host": "redis",
    "redis_port": 6379
}
EOF
fi

# 初始化数据库
echo "检查数据库初始化..."
php -r "
require_once '/var/www/html/config/config.php';
try {
    \$db = getDatabase();
    \$tables = \$db->query('SHOW TABLES')->fetchAll(PDO::FETCH_COLUMN);
    if (empty(\$tables)) {
        echo '初始化数据库表...' . PHP_EOL;
        \$sql = file_get_contents('/var/www/html/database/init_mysql.sql');
        \$db->exec(\$sql);
        echo '✅ 数据库初始化完成' . PHP_EOL;
    } else {
        echo '✅ 数据库已存在，跳过初始化' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo '❌ 数据库初始化失败: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

# 修复JavaScript问题
echo "修复JavaScript配置..."
if [ -f "/var/www/html/public/assets/js/api.js" ]; then
    # 确保API baseUrl正确
    sed -i "s|this\.baseUrl = '/wp-deploy-manager/api'|this.baseUrl = '/api'|g" /var/www/html/public/assets/js/api.js
    sed -i "s|this\.baseUrl = \"/wp-deploy-manager/api\"|this.baseUrl = '/api'|g" /var/www/html/public/assets/js/api.js
    
    # 移除多余的版本注释
    sed -i '/^\/\* Version:/d' /var/www/html/public/assets/js/api.js
    
    # 添加版本注释
    VERSION=$(date +%Y%m%d%H%M%S)
    sed -i "1i/* Version: $VERSION - Docker Fixed */" /var/www/html/public/assets/js/api.js
    
    echo "✅ JavaScript配置修复完成"
fi

# 创建测试数据
echo "创建测试数据..."
php -r "
require_once '/var/www/html/config/config.php';
try {
    \$db = getDatabase();
    \$stmt = \$db->query('SELECT COUNT(*) FROM templates');
    \$count = \$stmt->fetchColumn();
    
    if (\$count == 0) {
        echo '插入测试模板数据...' . PHP_EOL;
        \$stmt = \$db->prepare('
            INSERT INTO templates (uuid, name, description, filename, file_path, file_size, file_hash, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ');
        
        \$testData = [
            [
                'uuid' => 'docker-test-' . uniqid(),
                'name' => 'Docker测试模板',
                'description' => 'Docker环境测试模板',
                'filename' => 'docker-test.tar.gz',
                'file_path' => '/uploads/templates/docker-test.tar.gz',
                'file_size' => 1024000,
                'file_hash' => md5('docker-test-template'),
                'status' => 'active'
            ]
        ];
        
        foreach (\$testData as \$data) {
            \$stmt->execute(array_values(\$data));
        }
        
        echo '✅ 测试数据创建完成' . PHP_EOL;
    } else {
        echo '✅ 测试数据已存在' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo '⚠️  测试数据创建失败: ' . \$e->getMessage() . PHP_EOL;
}
"

# 启动cron服务
echo "启动cron服务..."
service cron start

# 创建健康检查文件
echo "创建健康检查..."
cat > /var/www/html/public/health.php << 'EOF'
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'ok',
    'timestamp' => date('Y-m-d H:i:s'),
    'services' => []
];

// 检查数据库连接
try {
    require_once dirname(__DIR__) . '/config/config.php';
    $db = getDatabase();
    $health['services']['database'] = 'ok';
} catch (Exception $e) {
    $health['services']['database'] = 'error';
    $health['status'] = 'error';
}

// 检查Redis连接
try {
    if (class_exists('Redis')) {
        $redis = new Redis();
        $redis->connect('redis', 6379);
        $health['services']['redis'] = 'ok';
        $redis->close();
    } else {
        $health['services']['redis'] = 'not_available';
    }
} catch (Exception $e) {
    $health['services']['redis'] = 'error';
}

// 检查文件权限
$health['services']['file_permissions'] = [
    'logs_writable' => is_writable(dirname(__DIR__) . '/logs'),
    'uploads_writable' => is_writable(dirname(__DIR__) . '/uploads')
];

echo json_encode($health, JSON_PRETTY_PRINT);
?>
EOF

echo "✅ 容器初始化完成"
echo "🌐 访问地址: http://localhost"
echo "📊 健康检查: http://localhost/health.php"
echo "🗄️  phpMyAdmin: http://localhost:8080"
echo "📧 MailHog: http://localhost:8025"

# 执行传入的命令
exec "$@"
