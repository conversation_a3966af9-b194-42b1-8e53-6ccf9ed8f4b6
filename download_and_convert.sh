#!/bin/bash

# 设置 ImageMagick 环境变量
export MAGICK_HOME="/usr/lib64/ImageMagick-6.9.10"
export PATH="$MAGICK_HOME/bin:$PATH"
export MAGICK_CONFIGURE_PATH="$MAGICK_HOME/etc/ImageMagick"

# 并行下载图片到 imagedownload 文件夹
parallel -j 8 wget -nc -P /www/imagerdown/imagedownload < /www/imagerdown/image_links.txt

# 转换图片为 WebP 格式并保存到 imagewebp 文件夹
for img in /www/imagerdown/imagedownload/*; do
    if [ -f "$img" ]; then
        convert "$img" "/www/imagerdown/imagewebp/$(basename "${img%.*}.webp")"
    fi
done

# 删除原始图片（取消注释以启用）
rm -rf /www/imagerdown/imagedownload/*
