#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在原始处理文件中查找失败产品并用重新处理的结果替换
保持原始文件的顺序和格式
"""

import os
import sys
import re

def find_and_replace_failed_products(original_file, failed_retry_file, reprocessed_file, output_file):
    """
    在原始文件中找到失败产品，用重新处理的结果替换
    
    Args:
        original_file: 原始处理文件 (如 tpa4_optimized.txt)
        failed_retry_file: 失败产品列表 (failed_products_retry.txt)
        reprocessed_file: 重新处理的结果 (retry_failed_batch_optimized.txt)
        output_file: 输出文件
    """
    
    print("🔄 在原文件中替换失败产品")
    print("="*60)
    
    # 1. 读取失败产品列表
    failed_products = []
    try:
        with open(failed_retry_file, 'r', encoding='utf-8') as f:
            failed_products = [line.strip() for line in f if line.strip()]
        print(f"📋 失败产品列表: {len(failed_products):,} 个")
    except Exception as e:
        print(f"❌ 读取失败产品列表失败: {e}")
        return False
    
    # 2. 读取重新处理的结果
    reprocessed_results = []
    try:
        with open(reprocessed_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 跳过标题行，提取重新处理的结果
        for line in lines[2:]:  # 跳过标题和分隔线
            if line.strip() and not line.startswith('='):
                reprocessed_results.append(line.strip())
        
        print(f"🔄 重新处理结果: {len(reprocessed_results):,} 个")
    except Exception as e:
        print(f"❌ 读取重新处理结果失败: {e}")
        return False
    
    # 3. 读取原始文件
    original_lines = []
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            original_lines = f.readlines()
        print(f"📁 原始文件: {len(original_lines):,} 行")
    except Exception as e:
        print(f"❌ 读取原始文件失败: {e}")
        return False
    
    # 4. 创建失败产品到重新处理结果的映射
    failed_to_reprocessed = {}
    
    # 简单映射：按顺序对应
    for i, failed_product in enumerate(failed_products):
        if i < len(reprocessed_results):
            failed_to_reprocessed[failed_product.lower().strip()] = reprocessed_results[i]
    
    print(f"🔗 创建映射: {len(failed_to_reprocessed):,} 个")
    
    # 5. 处理原始文件，替换失败产品
    updated_lines = []
    replaced_count = 0
    failed_marked_count = 0
    
    print(f"\n🔄 开始处理原始文件...")
    
    for i, line in enumerate(original_lines):
        line_num = i + 1
        
        # 保留标题行和分隔线
        if i < 2 or line.startswith('=') or not line.strip():
            updated_lines.append(line)
            continue
        
        # 检查是否是失败产品行
        if '[FAILED]' in line:
            failed_marked_count += 1
            
            # 尝试从失败行中提取原始产品名
            original_product = extract_original_product_from_failed_line(line)
            
            if original_product:
                # 查找对应的重新处理结果
                original_key = original_product.lower().strip()
                
                if original_key in failed_to_reprocessed:
                    # 替换为重新处理的结果
                    updated_lines.append(failed_to_reprocessed[original_key] + '\n')
                    replaced_count += 1
                    
                    if replaced_count % 100 == 0:
                        print(f"   已替换: {replaced_count:,} 个失败产品")
                else:
                    # 没找到对应结果，保持原样但标记
                    updated_lines.append(f"[STILL_FAILED] {original_product} | FAILED > Still Failed After Retry | retry_failed\n")
            else:
                # 无法提取原始产品名，保持原样
                updated_lines.append(line)
        else:
            # 正常的成功产品行，保持不变
            updated_lines.append(line)
    
    print(f"✅ 处理完成!")
    print(f"📊 统计:")
    print(f"   原始失败标记: {failed_marked_count:,} 个")
    print(f"   成功替换: {replaced_count:,} 个")
    print(f"   仍然失败: {failed_marked_count - replaced_count:,} 个")
    
    # 6. 保存更新后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
        
        print(f"💾 已保存到: {output_file}")
        
        # 验证结果
        final_failed_count = sum(1 for line in updated_lines if '[FAILED]' in line or '[STILL_FAILED]' in line)
        final_success_count = len([line for line in updated_lines[2:] if line.strip() and not line.startswith('=') and '[FAILED]' not in line and '[STILL_FAILED]' not in line])
        
        print(f"\n📊 最终统计:")
        print(f"   成功产品: {final_success_count:,} 个")
        print(f"   失败产品: {final_failed_count:,} 个")
        print(f"   总计: {final_success_count + final_failed_count:,} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

def extract_original_product_from_failed_line(failed_line):
    """从失败行中提取原始产品名"""
    
    # 失败行格式通常是: [FAILED] 原始产品名 | FAILED > AI Processing Failed | [FAILED] 品牌
    
    # 方法1: 提取 [FAILED] 后面到第一个 | 之前的内容
    match = re.search(r'\[FAILED\]\s*(.+?)\s*\|', failed_line)
    if match:
        return match.group(1).strip()
    
    # 方法2: 如果没有找到，尝试其他格式
    if '|' in failed_line:
        parts = failed_line.split('|')
        if len(parts) > 0:
            first_part = parts[0].strip()
            # 移除 [FAILED] 标记
            original = first_part.replace('[FAILED]', '').strip()
            if original:
                return original
    
    # 方法3: 直接移除 [FAILED] 标记
    original = failed_line.replace('[FAILED]', '').strip()
    if '|' in original:
        original = original.split('|')[0].strip()
    
    return original if original else None

def smart_match_failed_products(original_file, failed_retry_file, reprocessed_file, output_file):
    """
    智能匹配失败产品的高级版本
    通过分析原始输入文件来精确匹配
    """
    
    print("🧠 智能匹配模式")
    print("="*60)
    
    # 尝试找到对应的原始输入文件
    input_file = None
    base_name = os.path.basename(original_file).replace('_optimized.txt', '.txt')
    input_dir = os.path.join(os.path.dirname(original_file), '..', 'input')
    potential_input = os.path.join(input_dir, base_name)
    
    if os.path.exists(potential_input):
        input_file = potential_input
        print(f"📁 找到原始输入文件: {input_file}")
    else:
        print(f"⚠️ 未找到原始输入文件: {potential_input}")
        print("使用基础匹配模式...")
        return find_and_replace_failed_products(original_file, failed_retry_file, reprocessed_file, output_file)
    
    # 读取原始输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            input_products = [line.strip() for line in f if line.strip()]
        print(f"📋 原始输入: {len(input_products):,} 个产品")
    except Exception as e:
        print(f"❌ 读取原始输入文件失败: {e}")
        return find_and_replace_failed_products(original_file, failed_retry_file, reprocessed_file, output_file)
    
    # 读取失败产品列表
    try:
        with open(failed_retry_file, 'r', encoding='utf-8') as f:
            failed_products = [line.strip() for line in f if line.strip()]
        print(f"📋 失败产品: {len(failed_products):,} 个")
    except Exception as e:
        print(f"❌ 读取失败产品列表失败: {e}")
        return False
    
    # 读取重新处理的结果
    try:
        with open(reprocessed_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        reprocessed_results = []
        for line in lines[2:]:
            if line.strip() and not line.startswith('='):
                reprocessed_results.append(line.strip())
        print(f"🔄 重新处理结果: {len(reprocessed_results):,} 个")
    except Exception as e:
        print(f"❌ 读取重新处理结果失败: {e}")
        return False
    
    # 读取原始输出文件
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            original_lines = f.readlines()
        print(f"📁 原始输出: {len(original_lines):,} 行")
    except Exception as e:
        print(f"❌ 读取原始输出文件失败: {e}")
        return False
    
    # 创建精确的位置映射
    print(f"\n🔗 创建精确映射...")
    
    # 为每个失败产品找到在原始输入中的位置
    failed_positions = {}
    for failed_product in failed_products:
        for i, input_product in enumerate(input_products):
            if input_product.lower().strip() == failed_product.lower().strip():
                failed_positions[i] = failed_product
                break
    
    print(f"📍 找到失败产品位置: {len(failed_positions):,} 个")
    
    # 按位置顺序创建替换映射
    position_to_reprocessed = {}
    sorted_positions = sorted(failed_positions.keys())
    
    for i, position in enumerate(sorted_positions):
        if i < len(reprocessed_results):
            position_to_reprocessed[position] = reprocessed_results[i]
    
    # 重建输出文件
    updated_lines = []
    current_product_index = 0
    replaced_count = 0
    
    print(f"\n🔄 重建输出文件...")
    
    for i, line in enumerate(original_lines):
        # 保留标题行
        if i < 2 or line.startswith('=') or not line.strip():
            updated_lines.append(line)
            continue
        
        # 这是一个产品行
        if current_product_index in position_to_reprocessed:
            # 这个位置的产品需要替换
            updated_lines.append(position_to_reprocessed[current_product_index] + '\n')
            replaced_count += 1
            
            if replaced_count % 100 == 0:
                print(f"   已替换: {replaced_count:,} 个产品")
        else:
            # 保持原样
            updated_lines.append(line)
        
        current_product_index += 1
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
        
        print(f"\n✅ 智能替换完成!")
        print(f"📊 成功替换: {replaced_count:,} 个失败产品")
        print(f"💾 保存到: {output_file}")
        
        # 最终验证
        final_failed_count = sum(1 for line in updated_lines if '[FAILED]' in line)
        final_product_count = len([line for line in updated_lines[2:] if line.strip() and not line.startswith('=')])
        
        print(f"\n📊 最终统计:")
        print(f"   总产品数: {final_product_count:,} 个")
        print(f"   剩余失败: {final_failed_count:,} 个")
        print(f"   成功率: {((final_product_count - final_failed_count) / final_product_count * 100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

def main():
    """主函数"""
    
    if len(sys.argv) == 5:
        # 命令行模式
        original_file = sys.argv[1]
        failed_retry_file = sys.argv[2]
        reprocessed_file = sys.argv[3]
        output_file = sys.argv[4]
        
        success = smart_match_failed_products(original_file, failed_retry_file, reprocessed_file, output_file)
        sys.exit(0 if success else 1)
    else:
        # 交互模式
        print("🔄 失败产品原位替换工具")
        print("="*60)
        
        print("📋 功能说明:")
        print("   1. 在原始处理文件中找到失败的产品")
        print("   2. 用重新处理的结果替换失败产品")
        print("   3. 保持原始文件的顺序和格式")
        print("   4. 生成更新后的完整文件")
        print()
        
        print("💡 使用方法:")
        print("   python3 update_failed_products_in_place.py \\")
        print("     <原始处理文件> \\")
        print("     <失败产品列表> \\")
        print("     <重新处理结果> \\")
        print("     <输出文件>")
        print()
        
        print("📝 针对您的情况:")
        print("   python3 update_failed_products_in_place.py \\")
        print("     tpa4_optimized.txt \\")
        print("     failed_products_retry.txt \\")
        print("     retry_failed_batch_optimized.txt \\")
        print("     tpa4_updated_complete.txt")

if __name__ == "__main__":
    main()
