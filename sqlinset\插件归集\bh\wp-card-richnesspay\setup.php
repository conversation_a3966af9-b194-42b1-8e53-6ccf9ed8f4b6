<?php
/*
Plugin Name: RichnessPay - WooCommerce Gateway
Plugin URI:
Description: Extends WooCommerce by Adding the RichnessPay Gateway.
Version: 1.3
Author:
Author URI:
*/

// Include our Gateway Class and register Payment Gateway with WooCommerce
add_action('plugins_loaded', 'richness_pay_init', 0);
function richness_pay_init()
{
    // If the parent WC_Payment_Gateway class doesn't exist
    // it means WooCommerce is not installed on the site
    // so do nothing
    if (!class_exists('WC_Payment_Gateway')) return;

    global $wpdb;
    $prefix = $wpdb->prefix;
    $count = $wpdb->get_var("select count(*) from information_schema.columns where table_name = '" . $prefix . "posts' and column_name = 'order_sn'");
    if (!$count)
        $wpdb->query("alter table " . $prefix . 'posts add `order_sn` varchar(50) NOT NULL DEFAULT ""; ');

    // If we made it this far, then include our Gateway Class
    include_once('gateway.php');

    // Now that we have successfully included our class,
    // Lets add it too WooCommerce
    add_filter('woocommerce_payment_gateways', 'richness_pay_add_gateway');
}

function richness_pay_add_gateway($methods)
{
    $methods[] = 'richness_pay'; //class name
    return $methods;
}

// Add custom action links
add_filter('plugin_action_links_' . plugin_basename(__FILE__), 'richness_pay_action_links');
function richness_pay_action_links($links)
{
    $plugin_links = array(
        '<a href="' . admin_url('admin.php?page=wc-settings&tab=checkout&section=niumu_stripe') . '">' . __('Settings', 'niumu stripe emb') . '</a>',
    );

    // Merge our new link with the default ones
    return array_merge($plugin_links, $links);
}