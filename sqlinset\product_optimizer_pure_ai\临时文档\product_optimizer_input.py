#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer.py Input目录专用版
专门处理 /www/imagerdown/sqlinset/product_optimizer_pure_ai/input 目录下的文件
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
from concurrent.futures import ThreadPoolExecutor, as_completed

# DeepSeek API配置
API_KEY = "***********************************"
API_URL = "https://api.deepseek.com/v1/chat/completions"

# 路径配置
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/output"

# 并发配置 - 多级配置
# 标准配置 (稳定)
STANDARD_WORKERS = 4
STANDARD_BATCH_SIZE = 20
STANDARD_DELAY = 0.2

# 高性能配置 (DeepSeek API无限制时)
HIGH_PERFORMANCE_WORKERS = 12
HIGH_PERFORMANCE_BATCH_SIZE = 30
HIGH_PERFORMANCE_DELAY = 0.05

# 当前默认配置
MAX_WORKERS = STANDARD_WORKERS
BATCH_SIZE = STANDARD_BATCH_SIZE
REQUEST_DELAY = STANDARD_DELAY
TIMEOUT_SECONDS = 90
MAX_RETRIES = 4

# 统计锁
stats_lock = threading.Lock()
processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_products": 0,
    "failed_products": 0,
    "network_errors": 0,
    "timeout_errors": 0
}

def ai_optimize_products_input(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """Input目录专用AI产品优化"""
    batch_num, products_batch = batch_data

    # 语言配置
    LANGUAGE_CONFIG = {
        "en": {"locale": "en-US", "name": "英语(美式)"},
        "fr": {"locale": "fr-FR", "name": "法语"},
        "es": {"locale": "es-ES", "name": "西班牙语"},
        "de": {"locale": "de-DE", "name": "德语"},
        "auto": {"locale": "auto", "name": "自动检测"}
    }

    lang_config = LANGUAGE_CONFIG.get(target_lang, LANGUAGE_CONFIG["auto"])
    lang_name = lang_config["name"]

    # 英文提示词 - 恢复动态语言支持
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: {lang_name}
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    for attempt in range(max_retries):
        try:
            # 随机延迟避免并发冲突
            time.sleep(random.uniform(0, REQUEST_DELAY * 2))
            
            # 请求数据
            data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.8
            }
            
            # 请求头
            headers = {
                'Authorization': f'Bearer {API_KEY}',
                'Content-Type': 'application/json'
            }
            
            # 发送请求 - 优化超时设置
            response = requests.post(
                API_URL,
                json=data,
                headers=headers,
                timeout=TIMEOUT_SECONDS
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()
                
                try:
                    # 清理JSON内容
                    clean_content = content.strip()
                    
                    # 移除markdown标记
                    if "```json" in clean_content:
                        start_idx = clean_content.find("```json") + 7
                        end_idx = clean_content.find("```", start_idx)
                        if end_idx > start_idx:
                            clean_content = clean_content[start_idx:end_idx].strip()
                    
                    # 解析JSON
                    parsed_result = json.loads(clean_content)
                    
                    # 提取结果数组
                    if isinstance(parsed_result, dict) and "results" in parsed_result:
                        results_array = parsed_result["results"]
                    elif isinstance(parsed_result, list):
                        results_array = parsed_result
                    else:
                        results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                    
                    # 清理和优化结果
                    cleaned_results = []
                    for result in results_array:
                        # 清理SEO标题中的管道符
                        seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                        
                        # 获取分类
                        category = result.get("category", "")
                        
                        # 优化关键词
                        tags = result.get("tags", "")
                        tags = optimize_tags(tags)
                        
                        cleaned_result = {
                            "seo_name": seo_name,
                            "category": category,
                            "tags": tags
                        }
                        cleaned_results.append(cleaned_result)
                    
                    # 更新统计
                    with stats_lock:
                        processing_stats["completed_batches"] += 1
                        processing_stats["successful_products"] += len(cleaned_results)
                        
                        # 显示进度
                        completed = processing_stats["completed_batches"]
                        total = processing_stats["total_batches"]
                        elapsed = time.time() - processing_stats["start_time"]
                        
                        if completed > 0:
                            rate = completed / elapsed
                            eta = (total - completed) / rate if rate > 0 else 0
                            progress = (completed / total) * 100
                            
                            print(f"✅ 批次 {batch_num} 完成 | 进度: {progress:.1f}% ({completed}/{total}) | ETA: {eta/60:.1f}分钟")
                    
                    return batch_num, cleaned_results
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        return batch_num, generate_basic_results(products_batch)
            
        except requests.exceptions.RequestException as e:
            # 统计网络错误
            with stats_lock:
                if "timeout" in str(e).lower():
                    processing_stats["timeout_errors"] += 1
                else:
                    processing_stats["network_errors"] += 1

            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                # 智能延迟：超时错误延迟更长
                delay = 3 ** attempt if "timeout" in str(e).lower() else 2 ** attempt
                time.sleep(delay)
        except Exception as e:
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    # 所有尝试失败
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_products"] += len(products_batch)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    return batch_num, generate_basic_results(products_batch, fail_mode="mark")

def optimize_tags(tags):
    """优化关键词质量"""
    if not tags:
        return ""
    
    # 分割并清理关键词
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    
    # 限制数量和长度
    optimized_tags = []
    for tag in tag_list[:2]:  # 最多2个
        # 限制单个关键词长度
        if len(tag) <= 25:
            optimized_tags.append(tag)
    
    result = ", ".join(optimized_tags)
    
    # 总长度控制
    if len(result) > 30:
        result = optimized_tags[0] if optimized_tags else ""
    
    return result

def generate_basic_results(products_batch, fail_mode="mark"):
    """生成基础结果（备用方案）"""
    results = []

    for product in products_batch:
        # 简单的产品分析
        words = product.split()
        brand = words[0] if words else "Unknown"

        if fail_mode == "mark":
            # 标记为处理失败的产品
            seo_name = f"[FAILED] {product.replace('|', '-')}"
            category = "FAILED > AI Processing Failed"
            tags = f"[FAILED] {brand}"
        elif fail_mode == "basic":
            # 正常的备用处理
            seo_name = product.replace("|", "-")
            category = "General > Products"
            tags = f"{brand} products"
        elif fail_mode == "empty":
            # 留空处理
            seo_name = product.replace("|", "-")
            category = ""
            tags = ""
        else:
            # 默认标记模式
            seo_name = f"[FAILED] {product.replace('|', '-')}"
            category = "FAILED > AI Processing Failed"
            tags = f"[FAILED] {brand}"

        results.append({
            "seo_name": seo_name,
            "category": category,
            "tags": tags
        })

    return results

def analyze_failed_products(results):
    """分析失败产品"""
    failed_products = []
    successful_products = []

    for result in results:
        seo_name = result.get("seo_name", "")
        category = result.get("category", "")

        if "[FAILED]" in seo_name or "FAILED >" in category:
            failed_products.append(result)
        else:
            successful_products.append(result)

    return failed_products, successful_products

def save_failed_products_report(failed_products, output_dir):
    """保存失败产品报告"""
    if not failed_products:
        return None

    report_file = os.path.join(output_dir, "failed_products_report.txt")

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("失败产品处理报告\n")
            f.write("="*50 + "\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"失败产品数量: {len(failed_products)}\n\n")

            f.write("失败产品列表:\n")
            f.write("-"*30 + "\n")

            for i, product in enumerate(failed_products, 1):
                original_name = product['seo_name'].replace('[FAILED] ', '')
                f.write(f"{i}. {original_name}\n")

            f.write(f"\n💡 建议:\n")
            f.write(f"1. 检查网络连接稳定性\n")
            f.write(f"2. 降低并发线程数\n")
            f.write(f"3. 增加请求延迟时间\n")
            f.write(f"4. 重新处理失败的产品\n")

        return report_file
    except Exception as e:
        print(f"⚠️ 保存失败报告错误: {e}")
        return None

def create_failed_products_file(failed_products, output_dir):
    """创建失败产品文件供重新处理"""
    if not failed_products:
        return None

    retry_file = os.path.join(output_dir, "failed_products_retry.txt")

    try:
        with open(retry_file, 'w', encoding='utf-8') as f:
            for product in failed_products:
                original_name = product['seo_name'].replace('[FAILED] ', '')
                f.write(f"{original_name}\n")

        print(f"📁 失败产品重试文件: {retry_file}")
        print(f"💡 重新处理命令: 将此文件移动到input目录重新处理")

        return retry_file
    except Exception as e:
        print(f"⚠️ 创建重试文件错误: {e}")
        return None

def detect_optimal_workers():
    """智能检测最优线程数"""
    import multiprocessing

    # 获取CPU信息
    cpu_count = multiprocessing.cpu_count()

    print(f"🖥️ 系统资源检测:")
    print(f"   CPU核心数: {cpu_count}")

    # 基于CPU核心数和网络状况计算推荐线程数
    if cpu_count >= 16:
        recommended = 6  # 降低以减少网络压力
        level = "高性能服务器"
    elif cpu_count >= 8:
        recommended = 4  # 降低以提高稳定性
        level = "标准服务器"
    elif cpu_count >= 4:
        recommended = 3
        level = "基础服务器"
    else:
        recommended = 2
        level = "低配置"

    print(f"   推荐配置: {level} ({recommended} 线程)")
    print(f"   💡 优化策略: 平衡速度与网络稳定性")

    return recommended

def save_progress(file_name, completed_files):
    """保存处理进度"""
    try:
        progress_file = os.path.join(OUTPUT_DIR, "processing_progress.json")
        progress_data = {
            "timestamp": time.time(),
            "completed_files": completed_files,
            "current_file": file_name
        }
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)
    except:
        pass  # 进度保存失败不影响主流程

def process_input_directory():
    """处理input目录下的所有txt文件"""
    print("🚀 Input目录产品优化器")
    print("="*60)
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"📋 支持格式: .txt, .csv, .tsv, .dat, .text, 无扩展名文件")

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找input目录的文本文件 - 支持多种格式
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    # 查找有扩展名的文件
    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    # 查找无扩展名的文件（排除目录和隐藏文件）
    for item in os.listdir(INPUT_DIR):
        item_path = os.path.join(INPUT_DIR, item)
        if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
            all_files.append(item_path)

    # 去重并排序
    txt_files = sorted(list(set(all_files)))

    if not txt_files:
        print(f"❌ 在 {INPUT_DIR} 目录中未找到支持的文件")
        print(f"💡 支持的文件格式: .txt, .csv, .tsv, .dat, .text")
        print(f"💡 请将要处理的文件放入input目录")
        return False

    print(f"\n📋 发现 {len(txt_files)} 个文件:")
    total_size = 0
    for i, file_path in enumerate(txt_files, 1):
        file_size = os.path.getsize(file_path) / (1024*1024)  # MB
        total_size += file_size
        print(f"   {i}. {os.path.basename(file_path)} ({file_size:.1f} MB)")

    print(f"📊 总大小: {total_size:.1f} MB")

    # 智能检测配置
    recommended_workers = detect_optimal_workers()

    # 选择处理模式
    print(f"\n📋 处理模式:")
    print(f"1. 处理所有文件 (推荐)")
    print(f"2. 选择特定文件")

    mode_choice = input("请选择模式 (1/2): ").strip()

    if mode_choice == "2":
        print(f"\n请选择要处理的文件 (输入文件编号，多个文件用空格分隔):")
        file_choice = input("文件编号: ").strip()
        try:
            indices = [int(x) - 1 for x in file_choice.split()]
            selected_files = [txt_files[i] for i in indices if 0 <= i < len(txt_files)]
            if not selected_files:
                print("❌ 无效的文件选择")
                return False
            txt_files = selected_files
        except:
            print("❌ 输入格式错误")
            return False

    # 并发配置
    print(f"\n🚀 并发配置:")
    print(f"1. 智能推荐 ({recommended_workers} 线程) - 平衡稳定性")
    print(f"2. 高性能模式 ({HIGH_PERFORMANCE_WORKERS} 线程, {HIGH_PERFORMANCE_BATCH_SIZE} 产品/批次) - DeepSeek无限制")
    print(f"3. 自定义配置")

    config_choice = input("请选择 (1/2/3): ").strip()
    if config_choice == "2":
        max_workers = HIGH_PERFORMANCE_WORKERS
        batch_size = HIGH_PERFORMANCE_BATCH_SIZE
        print(f"⚡ 启用高性能模式: {max_workers} 线程, {batch_size} 产品/批次")
        print(f"⚠️ 注意: 高性能模式可能增加网络压力，请监控成功率")
    elif config_choice == "3":
        try:
            max_workers = int(input("请输入线程数 (1-20): "))
            batch_size = int(input("请输入批次大小 (10-50): "))
            max_workers = max(1, min(20, max_workers))
            batch_size = max(10, min(50, batch_size))
            print(f"🔧 自定义配置: {max_workers} 线程, {batch_size} 产品/批次")
        except:
            max_workers = recommended_workers
            batch_size = BATCH_SIZE
    else:
        max_workers = recommended_workers
        batch_size = BATCH_SIZE

    # 输出格式
    print(f"\n📋 选择输出格式:")
    print(f"1. 文本格式 (.txt)")
    print(f"2. CSV格式 (.csv)")

    format_choice = input("请选择格式 (1/2): ").strip()
    output_format = "csv" if format_choice == "2" else "txt"

    # 失败产品处理方式
    print(f"\n⚠️ 失败产品处理方式:")
    print(f"1. 标记为失败 (推荐) - 便于识别和重新处理")
    print(f"2. 使用基础处理 - 生成简单的分类和标签")
    print(f"3. 留空 - 失败产品显示为空")

    fail_choice = input("请选择 (1/2/3): ").strip()
    fail_mode = "mark" if fail_choice == "1" else "basic" if fail_choice == "2" else "empty"

    # 处理每个文件
    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(txt_files, 1):
        print(f"\n{'='*60}")
        print(f"🔄 处理文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        if output_format == "csv":
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.csv")
        else:
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

        # 处理单个文件
        success = process_single_file(input_file, output_file, max_workers, batch_size, output_format, "auto")

        if success:
            success_count += 1
            print(f"✅ 文件 {i} 处理完成")
        else:
            print(f"❌ 文件 {i} 处理失败")

    # 总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print(f"🎉 Input目录处理完成!")
    print(f"📊 成功处理: {success_count}/{len(txt_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"{'='*60}")

    return success_count == len(txt_files)

def process_single_file(input_path, output_path, max_workers, batch_size, output_format, target_lang="auto"):
    """处理单个文件"""
    print(f"📁 输入文件: {input_path}")
    print(f"📄 输出文件: {output_path}")

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 产品数量: {len(products):,} 个")

    # 大文件处理策略
    if len(products) >= 10000:
        print(f"🔍 检测到大文件 ({len(products):,} 行)")
        estimated_time = len(products) / 60  # 按60产品/分钟估算
        print(f"⏱️ 预估处理时间: {estimated_time:.1f} 分钟 ({estimated_time/60:.1f} 小时)")

        if len(products) >= 20000:
            print(f"💡 建议: 考虑分割文件以获得更好的处理体验")
            print(f"   分割命令: split -l 10000 {os.path.basename(input_path)} part_")

        # 大文件优化配置
        if len(products) >= 50000:
            print(f"🔧 自动启用大文件优化配置")
            max_workers = min(max_workers, 4)  # 限制线程数避免过载
            batch_size = min(batch_size, 15)   # 减少批次大小
            print(f"   优化后配置: {max_workers} 线程, {batch_size} 产品/批次")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), batch_size):
        batch_num = (i // batch_size) + 1
        batch = products[i:i+batch_size]
        batch_data.append((batch_num, batch))

    # 初始化统计
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0

    print(f"🔄 开始处理 ({len(batch_data)} 个批次，{max_workers} 个线程)...")

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_input, batch, target_lang): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                print(f"❌ 批次 {batch_num} 执行异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    # 保存结果
    try:
        if output_format == "csv":
            with open(output_path, 'w', encoding='utf-8-sig') as f:
                f.write("seo_name,category,tags\n")
                for result in final_results:
                    seo_name = result.get("seo_name", "").replace('"', '""')
                    category = result.get("category", "").replace('"', '""')
                    tags = result.get("tags", "").replace('"', '""')
                    f.write(f'"{seo_name}","{category}","{tags}"\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in final_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_products"]
        failed = processing_stats["failed_products"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        # 分析失败产品
        failed_products, successful_products = analyze_failed_products(final_results)

        print(f"✅ 处理完成!")
        print(f"📊 成功: {len(successful_products):,} | 失败: {len(failed_products):,} | 成功率: {success_rate:.1f}%")
        print(f"⏱️ 耗时: {elapsed_time/60:.1f} 分钟")
        print(f"🚀 速度: {len(products)/(elapsed_time/60):.0f} 产品/分钟")

        # 显示网络统计
        network_errors = processing_stats.get("network_errors", 0)
        timeout_errors = processing_stats.get("timeout_errors", 0)
        if network_errors > 0 or timeout_errors > 0:
            print(f"🌐 网络统计: 超时错误 {timeout_errors} 次, 其他网络错误 {network_errors} 次")

        # 处理失败产品报告
        if failed_products:
            print(f"⚠️ 发现 {len(failed_products)} 个失败产品")

            # 保存失败产品报告
            report_file = save_failed_products_report(failed_products, os.path.dirname(output_path))
            if report_file:
                print(f"📋 失败产品报告: {report_file}")

            # 创建重试文件
            retry_file = create_failed_products_file(failed_products, os.path.dirname(output_path))

            # 显示失败产品示例
            print(f"📝 失败产品示例:")
            for i, product in enumerate(failed_products[:3], 1):
                original_name = product['seo_name'].replace('[FAILED] ', '')
                print(f"   {i}. {original_name}")
            if len(failed_products) > 3:
                print(f"   ... 还有 {len(failed_products) - 3} 个失败产品")

            print(f"\n💡 失败产品处理建议:")
            print(f"   1. 检查网络连接稳定性")
            print(f"   2. 降低并发线程数重新处理")
            print(f"   3. 使用生成的重试文件重新处理失败产品")
        else:
            print(f"🎉 所有产品处理成功！")

        return True

    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def is_background_mode():
    """检测是否为后台运行模式"""
    import sys
    return not sys.stdout.isatty()

def main():
    """主函数"""
    background_mode = is_background_mode()

    print("🚀 Input目录专用产品优化器")
    print("="*60)
    print(f"🎯 专门处理: {INPUT_DIR}")
    print(f"📁 输出到: {OUTPUT_DIR}")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")

    if background_mode:
        print("🔄 检测到后台运行模式")
        print(f"📝 日志将保存到: {OUTPUT_DIR}/processing.log")

    # 命令行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "auto":
            # 自动处理所有文件
            print("\n🤖 自动模式: 处理所有文件")
            process_input_directory_auto()
        elif sys.argv[1] == "background":
            # 后台模式
            print("\n🔄 后台模式: 自动处理所有文件")
            setup_logging()
            process_input_directory_auto()
        else:
            print("❌ 无效参数")
            print("💡 支持的参数:")
            print("   auto - 自动处理所有文件")
            print("   background - 后台模式处理")
            return
    elif background_mode:
        # 检测到后台运行，自动启用后台模式
        print("\n🔄 自动启用后台模式")
        setup_logging()
        process_input_directory_auto()
    else:
        # 交互模式
        process_input_directory()

def setup_logging():
    """设置日志记录"""
    import logging
    log_file = os.path.join(OUTPUT_DIR, "processing.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    print(f"📝 日志文件: {log_file}")

def process_input_directory_auto():
    """自动处理模式 - 无交互"""
    print("🤖 自动处理模式启动")

    # 使用默认配置
    recommended_workers = detect_optimal_workers()
    max_workers = recommended_workers
    output_format = "txt"

    print(f"⚙️ 自动配置: {max_workers} 线程, 文本格式输出")

    # 直接调用处理逻辑
    return process_files_with_config(max_workers, output_format)

def process_files_with_config(max_workers, output_format):
    """使用指定配置处理文件"""
    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找文件 (复用现有逻辑)
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    for item in os.listdir(INPUT_DIR):
        item_path = os.path.join(INPUT_DIR, item)
        if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
            all_files.append(item_path)

    txt_files = sorted(list(set(all_files)))

    if not txt_files:
        print(f"❌ 在 {INPUT_DIR} 目录中未找到支持的文件")
        return False

    print(f"📋 发现 {len(txt_files)} 个文件，开始自动处理...")

    # 处理所有文件
    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(txt_files, 1):
        print(f"\n{'='*60}")
        print(f"🔄 处理文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        base_name = os.path.splitext(os.path.basename(input_file))[0]
        if output_format == "csv":
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.csv")
        else:
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

        success = process_single_file(input_file, output_file, max_workers, BATCH_SIZE, output_format, "auto")

        if success:
            success_count += 1
            print(f"✅ 文件 {i} 处理完成")
        else:
            print(f"❌ 文件 {i} 处理失败")

    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print(f"🎉 自动处理完成!")
    print(f"📊 成功处理: {success_count}/{len(txt_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"{'='*60}")

    return success_count == len(txt_files)

if __name__ == "__main__":
    main()
