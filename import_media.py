#!/usr/bin/env python3
import os
import subprocess
import concurrent.futures
import time
import requests
from pathlib import Path
import tempfile
from urllib.parse import urlparse
import json
import hashlib
import logging
import sys
import argparse
from datetime import datetime

# 配置信息
SITES = {
    'livuora.com': {
        'path': '/www/wwwroot/livuora.com',
        'image_list': '/www/wwwroot/livuora.com/livuora_images.txt',
        'import_log': '/www/wwwroot/livuora.com/import_log.json'
    },
    'kitchencovely.com': {
        'path': '/www/wwwroot/kitchencovely.com',
        'image_list': '/www/wwwroot/kitchencovely.com/kitchencovely_images.txt',
        'import_log': '/www/wwwroot/kitchencovely.com/import_log.json'
    }
    # 添加新站点示例：
    # 'newsite.com': {
    #     'path': '/www/wwwroot/newsite.com',
    #     'image_list': '/www/wwwroot/newsite.com/newsite_images.txt',
    #     'import_log': '/www/wwwroot/newsite.com/import_log.json'
    # }
}

# 性能配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟（秒）
DOWNLOAD_TIMEOUT = 30  # 下载超时（秒）
MAX_SITES = 8  # 最大并行站点数
MAX_WORKERS_PER_SITE = 10  # 每个站点的最大并行线程数
BATCH_SIZE = 100  # 日志批量写入大小
IMAGES_PER_BATCH = 1000  # 每批处理的图片数

def setup_logging(daemon_mode=False):
    """设置日志系统"""
    log_dir = '/www/wwwlogs/import_media'
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'import_media_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    handlers = [logging.FileHandler(log_file)]
    if not daemon_mode:
        handlers.append(logging.StreamHandler())
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=handlers
    )
    return log_file

class ImportLogger:
    def __init__(self, site_name):
        self.site_name = site_name
        self.log_file = SITES[site_name]['import_log']
        self.import_log = self.load_log()
        self.pending_updates = {}
        self.last_save = time.time()
        
    def load_log(self):
        if os.path.exists(self.log_file):
            try:
                with open(self.log_file, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_log(self, force=False):
        current_time = time.time()
        if force or len(self.pending_updates) >= BATCH_SIZE or (current_time - self.last_save) > 60:
            if self.pending_updates:
                self.import_log.update(self.pending_updates)
                try:
                    with open(self.log_file, 'w') as f:
                        json.dump(self.import_log, f, indent=2)
                    self.pending_updates.clear()
                    self.last_save = current_time
                except Exception as e:
                    logging.error(f"无法保存导入记录 {self.log_file}: {str(e)}")
    
    def add_record(self, url_hash, data):
        self.pending_updates[url_hash] = data
        self.save_log()
    
    def is_imported(self, url_hash):
        return url_hash in self.import_log or url_hash in self.pending_updates
    
    def get_imported_count(self, urls):
        return sum(1 for url in urls if get_url_hash(url) in self.import_log)

def get_url_hash(url):
    """生成URL的唯一哈希值"""
    return hashlib.md5(url.encode()).hexdigest()

def download_image(url):
    """从URL下载图片到临时文件"""
    try:
        response = requests.get(url, timeout=DOWNLOAD_TIMEOUT)
        response.raise_for_status()
        
        filename = os.path.basename(urlparse(url).path)
        if not filename:
            filename = 'image.jpg'
        
        if '_' in filename:
            parts = filename.split('_')
            if parts[0].startswith('tmp'):
                filename = '_'.join(parts[1:])
        
        temp_dir = tempfile.mkdtemp()
        temp_file = os.path.join(temp_dir, filename)
        
        with open(temp_file, 'wb') as f:
            f.write(response.content)
        
        return temp_file
    except Exception as e:
        logging.error(f"下载图片失败 {url}: {str(e)}")
        return None

def import_media(site_name, image_url, import_logger, retry_count=0):
    """从URL导入单个媒体文件到指定站点"""
    site_config = SITES[site_name]
    wp_path = site_config['path']
    url_hash = get_url_hash(image_url)
    
    if import_logger.is_imported(url_hash):
        logging.info(f"跳过已导入的图片: {os.path.basename(urlparse(image_url).path)}")
        return True
    
    try:
        temp_file = download_image(image_url)
        if not temp_file:
            return False
        
        try:
            cmd = [
                'wp',
                '--allow-root',
                f'--path={wp_path}',
                'media',
                'import',
                temp_file,
                '--porcelain'
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode == 0:
                import_logger.add_record(url_hash, {
                    'url': image_url,
                    'filename': os.path.basename(temp_file),
                    'import_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'attachment_id': result.stdout.strip()
                })
                
                logging.info(f"成功导入 {os.path.basename(temp_file)} 到 {site_name}")
                return True
            else:
                error_msg = result.stderr.strip()
                logging.error(f"导入失败 {os.path.basename(temp_file)} 到 {site_name}: {error_msg}")
                
                if "Could not insert attachment into the database" in error_msg and retry_count < MAX_RETRIES:
                    logging.info(f"等待 {RETRY_DELAY} 秒后重试... (尝试 {retry_count + 1}/{MAX_RETRIES})")
                    time.sleep(RETRY_DELAY)
                    return import_media(site_name, image_url, import_logger, retry_count + 1)
                
                return False
        finally:
            try:
                os.unlink(temp_file)
                os.rmdir(os.path.dirname(temp_file))
            except:
                pass
                
    except Exception as e:
        logging.error(f"导入出错 {image_url} 到 {site_name}: {str(e)}")
        return False

def process_site(site_name):
    """处理单个站点的所有图片"""
    site_config = SITES[site_name]
    image_list_file = site_config['image_list']
    
    logging.info(f"正在处理站点 {site_name} 的图片列表: {image_list_file}")
    
    import_logger = ImportLogger(site_name)
    
    with open(image_list_file, 'r') as f:
        image_urls = [line.strip() for line in f if line.strip()]
    
    total_images = len(image_urls)
    already_imported = import_logger.get_imported_count(image_urls)
    
    logging.info(f"开始处理站点 {site_name} 的 {total_images} 个图片 (已导入: {already_imported}, 待导入: {total_images - already_imported})")
    
    # 分批处理图片
    for i in range(0, len(image_urls), IMAGES_PER_BATCH):
        batch_urls = image_urls[i:i + IMAGES_PER_BATCH]
        logging.info(f"处理批次 {i//IMAGES_PER_BATCH + 1}/{(len(image_urls) + IMAGES_PER_BATCH - 1)//IMAGES_PER_BATCH}")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS_PER_SITE) as executor:
            futures = [
                executor.submit(import_media, site_name, image_url, import_logger)
                for image_url in batch_urls
            ]
            concurrent.futures.wait(futures)
        
        # 每批处理完强制保存日志
        import_logger.save_log(force=True)

def daemonize():
    """将进程转为守护进程"""
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
    except OSError:
        sys.exit(1)
    
    os.setsid()
    
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
    except OSError:
        sys.exit(1)
    
    os.chdir('/')
    
    sys.stdout.flush()
    sys.stderr.flush()
    
    with open('/dev/null', 'r') as f:
        os.dup2(f.fileno(), sys.stdin.fileno())
    with open('/dev/null', 'a+') as f:
        os.dup2(f.fileno(), sys.stdout.fileno())
    with open('/dev/null', 'a+') as f:
        os.dup2(f.fileno(), sys.stderr.fileno())

def main():
    parser = argparse.ArgumentParser(description='WordPress媒体库导入工具')
    parser.add_argument('--daemon', action='store_true', help='以守护进程模式运行')
    args = parser.parse_args()
    
    if args.daemon:
        daemonize()
    
    log_file = setup_logging(args.daemon)
    if args.daemon:
        logging.info(f"守护进程模式启动，日志文件: {log_file}")
    
    try:
        # 使用进程池处理多个站点
        with concurrent.futures.ProcessPoolExecutor(max_workers=min(len(SITES), MAX_SITES)) as executor:
            executor.map(process_site, SITES.keys())
    except Exception as e:
        logging.error(f"错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 