#!/bin/bash
# 火山引擎版产品优化器后台运行脚本

echo "🚀 火山引擎版产品优化器后台运行脚本"
echo "================================================"

# 配置变量
SCRIPT_NAME="product_optimizer_volcengine_simple.py"
INPUT_FILE="input/products.txt"
OUTPUT_FILE="output/products_optimized.csv"
LOG_FILE="logs/process.log"
PID_FILE="logs/process.pid"

# 创建必要目录
mkdir -p input output logs

# 检查脚本文件
if [ ! -f "$SCRIPT_NAME" ]; then
    echo "❌ 脚本文件不存在: $SCRIPT_NAME"
    exit 1
fi

# 检查输入文件
if [ ! -f "$INPUT_FILE" ]; then
    echo "❌ 输入文件不存在: $INPUT_FILE"
    echo "💡 请将产品文件放入 input/ 目录"
    exit 1
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 显示配置信息
echo "📋 运行配置:"
echo "   脚本: $SCRIPT_NAME"
echo "   输入: $INPUT_FILE"
echo "   输出: $OUTPUT_FILE"
echo "   日志: $LOG_FILE"
echo "   PID文件: $PID_FILE"

# 语言选择
echo ""
echo "🌍 选择输出语言:"
echo "1. Auto (自动检测)"
echo "2. English (英语)"
echo "3. Chinese (中文)"
echo "4. Spanish (西班牙语)"
echo "5. French (法语)"
echo "6. German (德语)"
echo "7. Japanese (日语)"
echo "8. Korean (韩语)"

read -p "请选择语言 (1-8, 默认Auto): " lang_choice

case $lang_choice in
    2) LANG="en" ;;
    3) LANG="zh" ;;
    4) LANG="es" ;;
    5) LANG="fr" ;;
    6) LANG="de" ;;
    7) LANG="ja" ;;
    8) LANG="ko" ;;
    *) LANG="auto" ;;
esac

echo "🌍 选择语言: $LANG"

# 配置选择
echo ""
echo "⚙️ 选择处理配置:"
echo "1. Auto模式 (推荐)"
echo "2. 快速模式 (8线程)"
echo "3. 稳定模式 (4线程)"
echo "4. 自定义配置"

read -p "请选择配置 (1-4, 默认Auto): " config_choice

case $config_choice in
    2)
        WORKERS=8
        BATCH_SIZE=25
        DELAY=0.2
        CONFIG_NAME="快速模式"
        ;;
    3)
        WORKERS=4
        BATCH_SIZE=15
        DELAY=0.5
        CONFIG_NAME="稳定模式"
        ;;
    4)
        read -p "线程数 (1-12): " WORKERS
        read -p "批次大小 (10-50): " BATCH_SIZE
        read -p "请求间隔 (0.1-1.0): " DELAY
        CONFIG_NAME="自定义配置"
        ;;
    *)
        WORKERS=""
        BATCH_SIZE=""
        DELAY=""
        CONFIG_NAME="Auto模式"
        ;;
esac

echo "⚙️ 选择配置: $CONFIG_NAME"

# 构建命令
CMD="python3 $SCRIPT_NAME --input $INPUT_FILE --output $OUTPUT_FILE --format csv --lang $LANG --log $LOG_FILE --non-interactive"

if [ -n "$WORKERS" ]; then
    CMD="$CMD --workers $WORKERS --batch-size $BATCH_SIZE --delay $DELAY"
fi

echo ""
echo "🚀 启动后台进程..."
echo "命令: $CMD"

# 启动后台进程
nohup $CMD > /dev/null 2>&1 &
PROCESS_PID=$!

# 保存PID
echo $PROCESS_PID > $PID_FILE

echo "✅ 后台进程已启动"
echo "   PID: $PROCESS_PID"
echo "   日志文件: $LOG_FILE"
echo "   PID文件: $PID_FILE"

echo ""
echo "📋 管理命令:"
echo "   查看进程: ps aux | grep $SCRIPT_NAME"
echo "   查看日志: tail -f $LOG_FILE"
echo "   停止进程: kill $PROCESS_PID"
echo "   或者运行: ./stop_background.sh"

echo ""
echo "💡 提示:"
echo "   - 进程将在后台运行，可以关闭终端"
echo "   - 使用 tail -f $LOG_FILE 查看实时进度"
echo "   - 处理完成后结果保存在 $OUTPUT_FILE"

# 创建停止脚本
cat > stop_background.sh << EOF
#!/bin/bash
# 停止后台进程脚本

PID_FILE="$PID_FILE"

if [ -f "\$PID_FILE" ]; then
    PID=\$(cat \$PID_FILE)
    if ps -p \$PID > /dev/null; then
        echo "🛑 停止进程 PID: \$PID"
        kill \$PID
        echo "✅ 进程已停止"
        rm -f \$PID_FILE
    else
        echo "⚠️ 进程 \$PID 不存在"
        rm -f \$PID_FILE
    fi
else
    echo "❌ PID文件不存在: \$PID_FILE"
fi
EOF

chmod +x stop_background.sh

echo "📁 已创建停止脚本: stop_background.sh"
echo ""
echo "🎉 后台运行设置完成！"
