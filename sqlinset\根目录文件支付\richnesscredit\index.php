<?php
session_start();
$data = urldecode($_GET['token']);
$data = base64_decode($data);
$data = json_decode($data,true);
//把失败，成功，异步url 存入session
$_SESSION['success_uri'] = $data['success_uri'];
$_SESSION['return_uri'] = $data['return_uri'];
$_SESSION['notify_url'] = $data['notify_url'];

//剩下8-30
$data     = data($data);
$data['success_uri'] = 'https://' . $_SERVER['HTTP_HOST'] . '/richnesscredit/success.php?order=' . $data['order_no'];
$data['return_uri'] = 'https://' . $_SERVER['HTTP_HOST'] . '/richnesscredit/return.php?order=' . $data['order_no'];
$data['notify_url'] = 'https://' . $_SERVER['HTTP_HOST'] . '/richnesscredit/notify.php?order=' . $data['order_no'];

$gateway = 'https://net.mrphper.xyz/pay/create_payment';
$response = request($gateway, $data);
if($response===false){
    header('Location: '.$_SESSION['return_uri']);
    exit;
}
$result = json_decode($response, true);

//error_log(json_encode($result)."\n",3,'test.log');
//error_log(date('Y-m-d H:i:s').'|'.json_encode($result)."\n",3,date('Y-m-d').'forpay.log');
error_log(date('Y-m-d H:i:s').'|'.$_SESSION['success_uri'].'|'.json_encode($result)."\n",3,date('Y-m-d').'forpay.log');
if($result['result']['success']){
    //$order->update_status('pendding');
    if($result['result']['url']){
        $redirect =  $result['result']['url'];
    }else{
        $redirect =  $_SESSION['return_uri'];
    }

    header('Location: '.$redirect);
    exit;
}elseif($result['result']['msg']){
    header('Location: '.$_SESSION['return_uri']);
    exit;
}else{
    header('Location: '.$_SESSION['return_uri']);
    exit;
}

//========== function

function request($url, $data) {
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        $data_string = json_encode($data);
        $headers = ['Expect: '.(strlen($data_string) >= 1048576 ? '100-Continue' : '')];
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($curl, CURLOPT_URL, $url);

        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            return false;
        }
        curl_close($curl);
        return $response;
}

function data($data)
{
     $fields = array(
        'username',
        'token',
        'client_ip',
		'client_agent',
		'client_language',
        'invoice_id',
        'order_no',
        'currency',
        'subject',
        'body',
        'amount',
        'success_uri',
        'return_uri',
        'notify_url',
        'first_name',
        'last_name',
        'address',
        'country',
        'zone',
        'city',
        'email',
        'zip_code',
        'telephone',
        'payment_method',
    );

    $params = array();
    foreach ($data as $key => $value) {
            if (in_array($key, $fields)) {
                switch ($key) {
                    case 'amount':
                        $row = sprintf('%.2f', $value);
                        break;
                    default:
                        $row = trim($value);
                }

                $params[$key] = $value;
            }
    }
    $params['username'] = 'us77777'; //用户，改成自己的
    $params = request_hash($params);
    return $params;
}
function request_hash($data)
{
        $hash_src = '';
        $hash_key = array('invoice_id', 'order_no');
        foreach ($hash_key as $key) {
            $hash_src .= $data[$key];
        }
        // 密钥改成自己的
        $hash_src = $hash_src . 'QUGu07bvG0ykKUNzDc9Husyj5zTg4lddeSABeX36ZaqojL54hA4hcvkCMYOtXhVAfJUiPJT6VFZknFLXLfcmxpVMs9sfp7tQxm0d9OxgCl8eHOPYLknQofylleEupjBC6wY2vWDoa23R3K9ntTnDHISD2I8GRAzEjMmFKZrCLMSoE76RgtZeqmxsQBnwAWcJ18itT4bbXH8rSh67mlxI3BJPuvabvSiYq2NB4NYGCzQREWgI8qP0bW1j3D71pwywazqWfFkKUaiV1yXFuTdrhi15rMgH5JwN5RsUp0ZOEVdo';//$this->get_option('secret_key');//
        // sha256 算法
        $hash = hash('sha256', $hash_src);
        $data['token'] = strtoupper($hash);
        return $data;
}
