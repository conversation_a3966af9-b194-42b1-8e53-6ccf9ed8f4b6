#!/bin/bash

# Cloudflare API 令牌
API_TOKEN="****************************************"

# Cloudflare 帐户 ID
ACCOUNT_ID="8f36efbd6839bd54fb680af3f54bc84a"

# R2 储存桶名称
BUCKET_NAME="stooges"

# 图片文件夹路径
IMAGE_FOLDER="/www/imagerdown/testiamge"

# 上传函数
upload_file() {
    FILE=$1
    FILENAME=$(basename "$FILE")
    curl -X PUT \
         -H "Authorization: Bearer $API_TOKEN" \
         -F "file=@$FILE" \
         "https://api.cloudflare.com/client/v4/accounts/$ACCOUNT_ID/storage/kv/namespaces/$BUCKET_NAME/values/$FILENAME"
    echo "Uploaded $FILENAME"
}

# 导出上传函数，以便并行调用
export -f upload_file

# 并行上传文件
find "$IMAGE_FOLDER" -type f | parallel upload_file
