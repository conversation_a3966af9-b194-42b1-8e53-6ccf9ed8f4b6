@echo off
echo 🚀 启动WordPress Deploy Manager...
echo.

echo 检查Docker...
docker --version
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    pause
    exit /b 1
)

echo ✅ Docker已就绪
echo.

echo 启动服务...
docker-compose up -d

echo.
echo 等待服务启动...
timeout /t 15 /nobreak

echo.
echo 检查服务状态...
docker-compose ps

echo.
echo ✅ 服务启动完成！
echo.
echo 🌐 访问地址:
echo    主应用:      http://localhost
echo    phpMyAdmin:  http://localhost:8080
echo    API接口:     http://localhost/api/
echo.
echo 🔑 数据库信息:
echo    主机: localhost:3306
echo    数据库: wp_deploy_manager
echo    用户: wp_deploy
echo    密码: wp_deploy_pass_2024
echo    Root密码: wp_deploy_2024
echo.

pause
