<?php
/**
 * Site Differentiation Functions
 * 
 * This file contains functions for handling site differentiation
 * including layout variations, theme styles, and customizations.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}


function flatsome_child_get_preset_styles() {
    return [
        'default' => [
            'text_color' => '#222',
        ],
        'corporate' => [
            'header_style' => 'minimal',
            'border_radius' => 0,
            'text_color' => '#222',
        ],
        'modern' => [
            'layout_style' => 'wide',
            'header_style' => 'modern',
            'footer_style' => 'modern',
            'product_style' => 'modern',
            'spacing' => 25,
            'border_radius' => 8,
            'text_color' => '#222',
        ],
        'minimal' => [
            'layout_style' => 'boxed',
            'header_style' => 'minimal',
            'footer_style' => 'minimal',
            'product_style' => 'minimal',
            'spacing' => 12,
            'border_radius' => 0,
            'text_color' => '#222',
        ],
        'creative' => [
            'layout_style' => 'wide',
            'header_style' => 'default',
            'footer_style' => 'default',
            'product_style' => 'creative',
            'spacing' => 28,
            'border_radius' => 8,
            'text_color' => '#222',
        ],
        'tech' => [
            'layout_style' => 'wide',
            'header_style' => 'modern',
            'footer_style' => 'modern',
            'product_style' => 'modern',
            'spacing' => 30,
            'border_radius' => 12,
            'text_color' => '#fff',
        ],
        'minimalist' => [
            'layout_style' => 'boxed',
            'header_style' => 'minimal',
            'footer_style' => 'minimal',
            'product_style' => 'minimal',
            'spacing' => 12,
            'border_radius' => 0,
            'text_color' => '#222',
        ],
        'magazine' => [
            'layout_style' => 'wide',
            'header_style' => 'default',
            'footer_style' => 'default',
            'product_style' => 'modern',
            'spacing' => 22,
            'border_radius' => 6,
            'text_color' => '#222',
        ],
        'fashion' => [
            'layout_style' => 'wide',
            'header_style' => 'modern',
            'footer_style' => 'modern',
            'product_style' => 'creative',
            'spacing' => 28,
            'border_radius' => 16,
            'text_color' => '#fff',
        ],
        'warm' => [
            'layout_style' => 'standard',
            'header_style' => 'minimal',
            'footer_style' => 'minimal',
            'product_style' => 'default',
            'spacing' => 18,
            'border_radius' => 8,
            'text_color' => '#222',
        ],
    ];
}


function flatsome_child_apply_preset_style($preset_key) {
    $defaults = flatsome_child_get_default_style();
    $presets = flatsome_child_get_preset_styles();
    $preset = isset($presets[$preset_key]) ? $presets[$preset_key] : [];
    $final = array_merge($defaults, $preset);
    foreach ($final as $k => $v) {
        set_theme_mod($k, $v);
        }
    }


function flatsome_child_add_differentiation_classes($classes) {
    $layout_style  = get_theme_mod('layout_style', 'standard');
    $header_style  = get_theme_mod('header_style', 'default');
    $footer_style  = get_theme_mod('footer_style', 'default');
    $product_style = get_theme_mod('product_style', 'default');
    
    if (!empty($layout_style)) {
        $classes[] = 'layout-' . sanitize_html_class($layout_style);
    }
    
    if (!empty($header_style)) {
        $classes[] = 'header-style-' . sanitize_html_class($header_style);
    }
    
    if (!empty($footer_style)) {
        $classes[] = 'footer-style-' . sanitize_html_class($footer_style);
    }
    
    if (!empty($product_style)) {
        $classes[] = 'product-style-' . sanitize_html_class($product_style);
    }
    
    return $classes;
}
add_filter('body_class', 'flatsome_child_add_differentiation_classes');


function flatsome_child_get_default_style() {
    return [
        'layout_style' => 'standard',
        'header_style' => 'default',
        'footer_style' => 'default',
        'product_style' => 'default',
        'spacing' => 20,
        'border_radius' => 3,
        'text_color' => '#222',
    ];
} 