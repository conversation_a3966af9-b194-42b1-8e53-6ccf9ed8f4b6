#!/bin/bash

# 增强的宝塔面板集成管理脚本
# 支持完整的站点生命周期管理

# 加载配置
source "$(dirname "$0")/../../config/global.conf"
source "$(dirname "$0")/../utils/logger.sh"
source "$(dirname "$0")/../utils/json_parser.sh"

# 宝塔API基础函数
bt_api_call() {
    local endpoint="$1"
    local method="${2:-POST}"
    local data="$3"
    local response
    
    log_info "调用宝塔API: $endpoint"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -X POST "${BT_PANEL_URL}/api/${endpoint}" \
            -d "access_key=${BT_API_KEY}&${data}" \
            --connect-timeout 30 \
            --max-time 60)
    else
        response=$(curl -s -X GET "${BT_PANEL_URL}/api/${endpoint}?access_key=${BT_API_KEY}&${data}" \
            --connect-timeout 30 \
            --max-time 60)
    fi
    
    echo "$response"
}

# 创建站点（增强版）
create_site_enhanced() {
    local domain="$1"
    local template_type="$2"
    local php_version="${3:-81}"
    local enable_ssl="${4:-true}"
    
    log_info "开始创建站点: $domain (模板: $template_type, PHP: $php_version)"
    
    # 1. 创建基础站点
    local site_data="domain=${domain}&path=/www/wwwroot/${domain}&php_version=${php_version}"
    local site_response=$(bt_api_call "site/add" "POST" "$site_data")
    
    if echo "$site_response" | grep -q '"status":true'; then
        log_success "站点创建成功: $domain"
    else
        log_error "站点创建失败: $domain - $site_response"
        return 1
    fi
    
    # 2. 配置PHP版本和扩展
    configure_php_extensions "$domain" "$php_version"
    
    # 3. 设置目录权限
    set_directory_permissions "$domain"
    
    # 4. 配置SSL证书（如果启用）
    if [ "$enable_ssl" = "true" ]; then
        setup_ssl_certificate "$domain"
    fi
    
    # 5. 配置防火墙规则
    configure_firewall_rules "$domain"
    
    # 6. 设置备份策略
    setup_backup_strategy "$domain"
    
    return 0
}

# 配置PHP扩展
configure_php_extensions() {
    local domain="$1"
    local php_version="$2"
    
    log_info "配置PHP扩展: $domain (PHP $php_version)"
    
    # WordPress推荐的PHP扩展
    local extensions=("curl" "gd" "mbstring" "xml" "zip" "mysqli" "imagick")
    
    for ext in "${extensions[@]}"; do
        local ext_data="php_version=${php_version}&extension=${ext}&action=install"
        bt_api_call "php/install_extension" "POST" "$ext_data" >/dev/null
    done
    
    # 优化PHP配置
    local php_config="php_version=${php_version}&memory_limit=256M&max_execution_time=300&upload_max_filesize=64M"
    bt_api_call "php/set_config" "POST" "$php_config" >/dev/null
    
    log_success "PHP扩展配置完成"
}

# 设置目录权限
set_directory_permissions() {
    local domain="$1"
    local webroot="/www/wwwroot/$domain"
    
    log_info "设置目录权限: $domain"
    
    # 设置基础权限
    chown -R www:www "$webroot"
    find "$webroot" -type d -exec chmod 755 {} \;
    find "$webroot" -type f -exec chmod 644 {} \;
    
    # WordPress特殊权限
    if [ -d "$webroot/wp-content" ]; then
        chmod 755 "$webroot/wp-content"
        chmod -R 755 "$webroot/wp-content/themes"
        chmod -R 755 "$webroot/wp-content/plugins"
        chmod -R 755 "$webroot/wp-content/uploads"
    fi
    
    log_success "目录权限设置完成"
}

# 设置SSL证书
setup_ssl_certificate() {
    local domain="$1"
    
    log_info "配置SSL证书: $domain"
    
    # 检查域名是否已解析
    if ! check_domain_resolution "$domain"; then
        log_warning "域名未解析，跳过SSL配置: $domain"
        return 1
    fi
    
    # 申请Let's Encrypt证书
    local ssl_data="domain=${domain}&auto_wildcard=false"
    local ssl_response=$(bt_api_call "ssl/apply_cert_api" "POST" "$ssl_data")
    
    if echo "$ssl_response" | grep -q '"status":true'; then
        log_success "SSL证书配置成功: $domain"
        
        # 强制HTTPS重定向
        local redirect_data="domain=${domain}&redirect=1"
        bt_api_call "ssl/set_redirect" "POST" "$redirect_data" >/dev/null
    else
        log_warning "SSL证书配置失败: $domain - $ssl_response"
    fi
}

# 检查域名解析
check_domain_resolution() {
    local domain="$1"
    local server_ip=$(curl -s ifconfig.me)
    local resolved_ip=$(dig +short "$domain" | tail -n1)
    
    if [ "$resolved_ip" = "$server_ip" ]; then
        return 0
    else
        return 1
    fi
}

# 配置防火墙规则
configure_firewall_rules() {
    local domain="$1"
    
    log_info "配置防火墙规则: $domain"
    
    # 基础安全规则
    local rules=(
        "port=80&protocol=tcp&action=accept&description=HTTP for $domain"
        "port=443&protocol=tcp&action=accept&description=HTTPS for $domain"
    )
    
    for rule in "${rules[@]}"; do
        bt_api_call "firewall/add_rule" "POST" "$rule" >/dev/null
    done
    
    log_success "防火墙规则配置完成"
}

# 设置备份策略
setup_backup_strategy() {
    local domain="$1"
    
    log_info "设置备份策略: $domain"
    
    # 创建自动备份任务
    local backup_data="name=${domain}_backup&type=site&save_local=1&save_ftp=0&save_qiniu=0"
    backup_data="${backup_data}&cycle=week&save=4&path=/www/wwwroot/${domain}"
    
    bt_api_call "backup/add_task" "POST" "$backup_data" >/dev/null
    
    log_success "备份策略设置完成"
}

# 创建数据库（增强版）
create_database_enhanced() {
    local db_name="$1"
    local db_user="$2"
    local db_pass="$3"
    local charset="${4:-utf8mb4}"
    
    log_info "创建数据库: $db_name"
    
    # 创建数据库
    local db_data="name=${db_name}&username=${db_user}&password=${db_pass}&charset=${charset}"
    local db_response=$(bt_api_call "database/add" "POST" "$db_data")
    
    if echo "$db_response" | grep -q '"status":true'; then
        log_success "数据库创建成功: $db_name"
        
        # 优化数据库配置
        optimize_database_config "$db_name"
        
        return 0
    else
        log_error "数据库创建失败: $db_name - $db_response"
        return 1
    fi
}

# 优化数据库配置
optimize_database_config() {
    local db_name="$1"
    
    log_info "优化数据库配置: $db_name"
    
    # MySQL优化设置
    mysql -u root -p"$MYSQL_ROOT_PASS" -e "
        SET GLOBAL innodb_buffer_pool_size = 256M;
        SET GLOBAL query_cache_size = 64M;
        SET GLOBAL max_connections = 200;
        OPTIMIZE TABLE ${db_name}.*;
    " 2>/dev/null
    
    log_success "数据库优化完成"
}

# 站点健康检查
check_site_health() {
    local domain="$1"
    local checks_passed=0
    local total_checks=5
    
    log_info "执行站点健康检查: $domain"
    
    # 1. HTTP状态检查
    local http_status=$(curl -s -o /dev/null -w "%{http_code}" "http://$domain" --max-time 10)
    if [ "$http_status" = "200" ] || [ "$http_status" = "301" ] || [ "$http_status" = "302" ]; then
        log_success "HTTP状态检查通过: $http_status"
        ((checks_passed++))
    else
        log_warning "HTTP状态检查失败: $http_status"
    fi
    
    # 2. HTTPS状态检查
    local https_status=$(curl -s -o /dev/null -w "%{http_code}" "https://$domain" --max-time 10)
    if [ "$https_status" = "200" ]; then
        log_success "HTTPS状态检查通过"
        ((checks_passed++))
    else
        log_warning "HTTPS状态检查失败: $https_status"
    fi
    
    # 3. WordPress检查
    if curl -s "https://$domain/wp-admin/" | grep -q "WordPress"; then
        log_success "WordPress检查通过"
        ((checks_passed++))
    else
        log_warning "WordPress检查失败"
    fi
    
    # 4. 数据库连接检查
    local webroot="/www/wwwroot/$domain"
    if [ -f "$webroot/wp-config.php" ]; then
        if wp --path="$webroot" db check --allow-root >/dev/null 2>&1; then
            log_success "数据库连接检查通过"
            ((checks_passed++))
        else
            log_warning "数据库连接检查失败"
        fi
    fi
    
    # 5. 文件权限检查
    if [ -w "$webroot/wp-content" ]; then
        log_success "文件权限检查通过"
        ((checks_passed++))
    else
        log_warning "文件权限检查失败"
    fi
    
    log_info "健康检查完成: $checks_passed/$total_checks 项通过"
    
    if [ $checks_passed -ge 4 ]; then
        return 0
    else
        return 1
    fi
}

# 主函数
main() {
    local action="$1"
    local domain="$2"
    
    case "$action" in
        "create")
            create_site_enhanced "$domain" "${3:-corporate}" "${4:-81}" "${5:-true}"
            ;;
        "create_db")
            create_database_enhanced "$2" "$3" "$4" "${5:-utf8mb4}"
            ;;
        "health_check")
            check_site_health "$domain"
            ;;
        *)
            echo "用法: $0 {create|create_db|health_check} [参数...]"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
