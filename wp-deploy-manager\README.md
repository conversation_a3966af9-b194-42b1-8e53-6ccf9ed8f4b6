# 🚀 WordPress Deploy Manager - 本地开发环境

## 📋 项目概述

WordPress Deploy Manager 是一个强大的WordPress站点批量部署和管理系统。这个项目提供了完整的本地开发环境，包含前端界面、API接口和数据库管理。

## 🎯 主要功能

- **批量部署**: 支持单域名和批量域名WordPress站点部署
- **模板管理**: 上传、管理和使用WordPress模板文件
- **实时监控**: 系统资源监控和健康状态检查
- **日志管理**: 完整的操作日志和错误追踪
- **设置管理**: 灵活的系统配置和参数调整

## 🚀 快速开始

### 一键安装（推荐）
```bash
# 双击运行安装脚本
install.bat
```

### 手动安装
```bash
# 1. 启动Docker服务
docker-compose up -d

# 2. 等待服务启动
# 等待约20秒让所有服务完全启动

# 3. 访问应用
# 打开浏览器访问 http://localhost
```

## 🌐 访问地址

安装完成后，您可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost | WordPress Deploy Manager 主界面 |
| API接口 | http://localhost/api/ | RESTful API 接口 |
| phpMyAdmin | http://localhost:8080 | 数据库管理界面 |

## 🔑 默认配置

### 数据库配置
```
主机: localhost:3306
数据库: wp_deploy_manager
用户: wp_deploy
密码: wp_deploy_pass_2024
Root密码: wp_deploy_2024
```

### 系统配置
```
最大并发任务: 3
默认超时时间: 1800秒
调试模式: 开启
日志级别: DEBUG
```

## 📁 项目结构

```
wp-deploy-manager/
├── api/                    # API端点文件（预留）
├── config/                 # 配置文件（预留）
├── database/               # 数据库初始化文件
├── logs/                   # 日志文件目录
├── public/                 # Web根目录
│   ├── api/               # API路由
│   ├── assets/            # 静态资源
│   │   ├── css/           # 样式文件
│   │   ├── js/            # JavaScript文件
│   │   └── images/        # 图片资源
│   └── index.html         # 主页面
├── uploads/                # 上传文件目录
├── docker-compose.yml      # Docker编排文件
├── install.bat            # 安装脚本
├── manage.bat             # 管理脚本
└── README.md              # 项目说明
```

## 🛠️ 开发指南

### 环境要求
- Docker Desktop for Windows
- 4GB+ 内存
- 10GB+ 磁盘空间

### 常用命令
```bash
# 启动服务
manage.bat start

# 停止服务
manage.bat stop

# 重启服务
manage.bat restart

# 查看状态
manage.bat status

# 查看日志
manage.bat logs

# 运行测试
manage.bat test

# 清理环境
manage.bat clean
```

### 开发流程
1. **环境搭建**: 运行 `install.bat` 创建开发环境
2. **代码修改**: 修改 `public/` 目录下的文件
3. **实时预览**: 刷新浏览器查看修改效果
4. **功能测试**: 使用 `manage.bat test` 测试功能
5. **日志查看**: 使用 `manage.bat logs` 查看运行日志

## 🧪 功能测试

### 基础测试
- [ ] 访问主页面 http://localhost
- [ ] 检查API接口 http://localhost/api/
- [ ] 登录phpMyAdmin http://localhost:8080
- [ ] 查看Docker容器状态

### 功能模块测试
- [ ] **仪表板**: 统计数据显示正常
- [ ] **部署管理**: 界面加载正常，按钮响应
- [ ] **模板管理**: 模板列表显示，操作按钮工作
- [ ] **监控中心**: 系统资源数据显示
- [ ] **日志管理**: 日志列表正常显示
- [ ] **系统设置**: 设置表单可以操作

### API测试
```bash
# 测试基础API
curl http://localhost/api/

# 测试状态API
curl http://localhost/api/status.php?action=system

# 测试模板API
curl http://localhost/api/templates.php?action=list
```

## 🚨 常见问题

### 端口冲突
如果80端口被占用，可以修改docker-compose.yml中的端口映射：
```yaml
ports:
  - "8080:80"  # 改为8080端口
```

### Docker服务异常
```bash
# 重启Docker Desktop
# 或者重新构建容器
docker-compose down
docker-compose up -d --build
```

### 权限问题
确保Docker Desktop有足够的权限访问项目目录。

### 浏览器缓存问题
如果页面显示异常，请清除浏览器缓存：
- Chrome: Ctrl+Shift+Delete
- Firefox: Ctrl+Shift+Delete
- Edge: Ctrl+Shift+Delete

## 📊 性能优化

### 开发环境优化
- 启用Docker的文件共享优化
- 增加Docker分配的内存和CPU
- 使用SSD存储提高I/O性能

### 前端优化
- 启用浏览器开发者工具
- 使用网络面板监控API请求
- 检查控制台错误信息

## 🔧 自定义配置

### 修改端口
编辑 `docker-compose.yml` 文件：
```yaml
services:
  web:
    ports:
      - "8080:80"  # 修改为其他端口
```

### 添加新功能
1. 在 `public/assets/js/main.js` 中添加JavaScript逻辑
2. 在 `public/assets/css/main.css` 中添加样式
3. 在 `public/api/index.php` 中添加API端点
4. 在 `public/index.html` 中添加HTML结构

## 📞 获取帮助

### 问题排查
1. 检查Docker服务状态: `docker-compose ps`
2. 查看容器日志: `docker-compose logs`
3. 验证端口占用情况: `netstat -an | findstr :80`
4. 检查文件权限设置

### 日志位置
- **应用日志**: logs/目录
- **Docker日志**: `docker-compose logs`
- **浏览器日志**: F12开发者工具控制台

### 重置环境
如果遇到无法解决的问题，可以完全重置环境：
```bash
manage.bat clean
install.bat
```

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

---

🎉 **感谢使用WordPress Deploy Manager！**

如果您觉得这个项目有用，请给我们一个⭐️星标支持！

## 🔗 相关链接

- [Docker官方文档](https://docs.docker.com/)
- [PHP官方文档](https://www.php.net/docs.php)
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [WordPress官方文档](https://wordpress.org/support/)

---

**项目状态**: ✅ 可用于本地开发和测试  
**最后更新**: 2024年7月26日  
**版本**: v1.0.0
