#!/bin/bash

# 设置 ImageMagick 环境变量
export MAGICK_HOME="/usr/lib64/ImageMagick-6.9.10"
export PATH="$MAGICK_HOME/bin:$PATH"
export MAGICK_CONFIGURE_PATH="$MAGICK_HOME/etc/ImageMagick"

# 检查输入参数是否正确
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <source_folder> <num_processes>"
    exit 1
fi

# 获取源文件夹路径和并行处理转换任务数量
source_folder="$1"
num_processes="$2"

# 转换图片为 WebP 格式并保存到 imagewebp 文件夹
convert_image() {
    img="$1"
    convert "$img" "/www/imagerdown/imagewebp/$(basename "${img%.*}.webp")"
}

export -f convert_image

# 并行处理转换任务
find "$source_folder" -type f | parallel -j "$num_processes" convert_image
