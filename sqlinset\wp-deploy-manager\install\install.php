<?php
/**
 * WordPress 部署管理系统安装向导
 */

// 防止直接访问
if (!defined('WP_DEPLOY_INSTALLER')) {
    define('WP_DEPLOY_INSTALLER', true);
}

// 基础路径
define('INSTALL_ROOT', dirname(__DIR__));

// 安装步骤
$steps = [
    'welcome' => '欢迎',
    'requirements' => '环境检查',
    'configuration' => '系统配置',
    'database' => '数据库初始化',
    'completion' => '安装完成'
];

$currentStep = $_GET['step'] ?? 'welcome';

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handlePostRequest();
}

/**
 * 处理POST请求
 */
function handlePostRequest() {
    global $currentStep;
    
    switch ($currentStep) {
        case 'requirements':
            if (checkRequirements()) {
                redirect('configuration');
            }
            break;
            
        case 'configuration':
            if (saveConfiguration()) {
                redirect('database');
            }
            break;
            
        case 'database':
            if (initializeDatabase()) {
                redirect('completion');
            }
            break;
    }
}

/**
 * 重定向到指定步骤
 */
function redirect($step) {
    header("Location: install.php?step=$step");
    exit;
}

/**
 * 检查系统要求
 */
function checkRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'sqlite_extension' => extension_loaded('pdo_sqlite'),
        'json_extension' => extension_loaded('json'),
        'curl_extension' => extension_loaded('curl'),
        'zip_extension' => extension_loaded('zip'),
        'writable_root' => is_writable(INSTALL_ROOT),
        'original_script_exists' => file_exists('/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh'),
        'original_script_executable' => is_executable('/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh')
    ];
    
    return !in_array(false, $requirements, true);
}

/**
 * 保存配置
 */
function saveConfiguration() {
    $config = [
        'mysql_root_password' => $_POST['mysql_root_password'] ?? '',
        'max_concurrent_jobs' => intval($_POST['max_concurrent_jobs'] ?? 3),
        'default_timeout' => intval($_POST['default_timeout'] ?? 1800),
        'enable_notifications' => isset($_POST['enable_notifications']),
        'notification_email' => $_POST['notification_email'] ?? '',
        'backup_before_deploy' => isset($_POST['backup_before_deploy']),
        'auto_ssl_setup' => isset($_POST['auto_ssl_setup'])
    ];
    
    // 验证配置
    if (empty($config['mysql_root_password'])) {
        return false;
    }
    
    // 保存配置文件
    $configFile = INSTALL_ROOT . '/config/app.json';
    return file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT)) !== false;
}

/**
 * 初始化数据库
 */
function initializeDatabase() {
    try {
        $dbFile = INSTALL_ROOT . '/database/deploy.db';
        $initSql = INSTALL_ROOT . '/database/init.sql';
        
        // 创建数据库目录
        $dbDir = dirname($dbFile);
        if (!is_dir($dbDir)) {
            mkdir($dbDir, 0755, true);
        }
        
        // 创建数据库连接
        $db = new PDO('sqlite:' . $dbFile);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 执行初始化SQL
        if (file_exists($initSql)) {
            $sql = file_get_contents($initSql);
            $db->exec($sql);
        }
        
        // 设置权限
        chmod($dbFile, 0644);
        
        return true;
    } catch (Exception $e) {
        error_log('Database initialization failed: ' . $e->getMessage());
        return false;
    }
}

/**
 * 获取要求检查结果
 */
function getRequirementsStatus() {
    return [
        'PHP 8.0+' => [
            'status' => version_compare(PHP_VERSION, '8.0.0', '>='),
            'current' => PHP_VERSION,
            'required' => '8.0.0+'
        ],
        'SQLite PDO扩展' => [
            'status' => extension_loaded('pdo_sqlite'),
            'current' => extension_loaded('pdo_sqlite') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'JSON扩展' => [
            'status' => extension_loaded('json'),
            'current' => extension_loaded('json') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'cURL扩展' => [
            'status' => extension_loaded('curl'),
            'current' => extension_loaded('curl') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'ZIP扩展' => [
            'status' => extension_loaded('zip'),
            'current' => extension_loaded('zip') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        '目录写入权限' => [
            'status' => is_writable(INSTALL_ROOT),
            'current' => is_writable(INSTALL_ROOT) ? '可写' : '不可写',
            'required' => '可写'
        ],
        '原始部署脚本' => [
            'status' => file_exists('/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh'),
            'current' => file_exists('/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh') ? '存在' : '不存在',
            'required' => '存在且可执行'
        ]
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统 - 安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .installer {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .installer-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .installer-header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
            color: rgba(255,255,255,0.6);
        }
        
        .step.active {
            color: white;
        }
        
        .step.completed {
            color: #2ecc71;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: white;
            color: #3498db;
        }
        
        .step.completed .step-number {
            background: #2ecc71;
            color: white;
        }
        
        .installer-content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
        
        .requirements-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .requirements-table th,
        .requirements-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirements-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .installer-footer {
            padding: 20px 40px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="installer-header">
            <h1>WordPress 部署管理系统</h1>
            <p>安装向导</p>
            
            <div class="step-indicator">
                <?php foreach ($steps as $stepKey => $stepName): ?>
                    <?php
                    $stepIndex = array_search($stepKey, array_keys($steps));
                    $currentIndex = array_search($currentStep, array_keys($steps));
                    $stepClass = '';
                    
                    if ($stepIndex < $currentIndex) {
                        $stepClass = 'completed';
                    } elseif ($stepIndex === $currentIndex) {
                        $stepClass = 'active';
                    }
                    ?>
                    <div class="step <?php echo $stepClass; ?>">
                        <div class="step-number"><?php echo $stepIndex + 1; ?></div>
                        <span><?php echo $stepName; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="installer-content">
            <?php include "steps/{$currentStep}.php"; ?>
        </div>
        
        <div class="installer-footer">
            <div>
                <?php if ($currentStep !== 'welcome'): ?>
                    <a href="install.php?step=<?php echo array_keys($steps)[array_search($currentStep, array_keys($steps)) - 1]; ?>" class="btn btn-secondary">上一步</a>
                <?php endif; ?>
            </div>
            
            <div>
                <?php if ($currentStep !== 'completion'): ?>
                    <form method="post" style="display: inline;">
                        <button type="submit" class="btn">
                            <?php echo $currentStep === 'welcome' ? '开始安装' : '下一步'; ?>
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
