@echo off
REM 后台启动脚本 - Windows版本
REM 使用方法: start_background.bat

echo 🚀 启动产品优化器后台处理
echo ==================================================

REM 检查Python版本
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 🐍 使用Python: python

REM 检查脚本文件
if not exist "product_optimizer_input.py" (
    echo ❌ 未找到 product_optimizer_input.py 文件
    echo 💡 请在脚本所在目录运行此命令
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "output" mkdir output

REM 启动后台进程
echo 🔄 启动后台进程...
start /B python product_optimizer_input.py auto > output\processing.log 2>&1

echo ✅ 后台进程已启动
echo 📝 日志文件: output\processing.log
echo ""
echo 📋 管理命令:
echo    查看日志: type output\processing.log
echo    查看进程: tasklist ^| findstr python
echo    停止进程: 使用任务管理器或 taskkill /f /im python.exe
echo ""
echo 💡 提示: 可以关闭此窗口，进程将继续在后台运行
pause
