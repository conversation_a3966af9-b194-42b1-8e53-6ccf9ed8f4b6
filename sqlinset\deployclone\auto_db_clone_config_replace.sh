#!/bin/bash

MOTHER_DOMAIN="astra.nestlyalli.shop"
MOTHER_TAR="/www/backup/site/astra.nestlyalli.shop/astra.nestlyalli.shop_de2_115357.tar.gz"
MOTHER_DB_FOR_DUMP="astra_nestly-de"
MYSQL_ROOT_PASS="2KHNS4XSsMBLZJ7B"

# set -x

while read NEW_DOMAIN NEW_DB NEW_DB_USER NEW_DB_PASS NEW_EMAIL; do
  [[ -z "$NEW_DOMAIN" || "$NEW_DOMAIN" =~ ^# ]] && continue
  WEBROOT="/www/wwwroot/$NEW_DOMAIN"

  echo "=============================="
  echo "$(date '+%Y-%m-%d %H:%M:%S') 正在处理: $NEW_DOMAIN"
  # ... (文件解压、数据库创建导入、wp-config修改等部分保持不变，参考之前的版本) ...
  # 确保这部分代码是你验证过的最新版本，特别是数据库导入和wp-config修改部分
  # 我这里省略这些部分，直接跳到 WP-CLI

  # 1. 解压母站程序包到新站点目录 (使用压缩包)
  mkdir -p "$WEBROOT"
  tar -xzf "$MOTHER_TAR" -C "$WEBROOT" --strip-components=1
  if [ -f "$WEBROOT/.user.ini" ]; then
    chattr -i "$WEBROOT/.user.ini" 2>/dev/null
  fi
  chown -R www:www "$WEBROOT"
  echo "$(date '+%Y-%m-%d %H:%M:%S') 程序文件解压完成"

  # 2. 创建数据库和用户
  mysql -u root -p"$MYSQL_ROOT_PASS" -e "CREATE DATABASE IF NOT EXISTS \`$NEW_DB\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
  mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP USER IF EXISTS '$NEW_DB_USER'@'localhost';"
  mysql -u root -p"$MYSQL_ROOT_PASS" -e "CREATE USER '$NEW_DB_USER'@'localhost' IDENTIFIED WITH mysql_native_password BY '$NEW_DB_PASS';"
  mysql -u root -p"$MYSQL_ROOT_PASS" -e "GRANT ALL PRIVILEGES ON \`$NEW_DB\`.* TO '$NEW_DB_USER'@'localhost';"
  mysql -u root -p"$MYSQL_ROOT_PASS" -e "FLUSH PRIVILEGES;"
  echo "$(date '+%Y-%m-%d %H:%M:%S') 数据库和用户创建完成"

  # 3. 从指定的母站数据库实时dump并导入新数据库
  echo "$(date '+%Y-%m-%d %H:%M:%S') 正在从 $MOTHER_DB_FOR_DUMP 导出数据到 $NEW_DB..."
  mysqldump --default-character-set=utf8mb4 "$MOTHER_DB_FOR_DUMP" -uroot -p"$MYSQL_ROOT_PASS" | mysql --default-character-set=utf8mb4 "$NEW_DB" -uroot -p"$MYSQL_ROOT_PASS"
  if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') 数据库导入完成"
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') 错误: 数据库导入失败！检查 $MOTHER_DB_FOR_DUMP 是否存在以及MySQL连接。"
    continue
  fi

  # 4. 修改wp-config.php数据库配置
  CONFIG_FILE="$WEBROOT/wp-config.php"
  if [ ! -f "$CONFIG_FILE" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') 警告: 新站点 $CONFIG_FILE 不存在，跳过配置修改！"
    continue
  fi
  sed -i "s|define( *'DB_NAME',.*);|define('DB_NAME', '$NEW_DB');|g" "$CONFIG_FILE"
  sed -i "s|define( *'DB_USER',.*);|define('DB_USER', '$NEW_DB_USER');|g" "$CONFIG_FILE"
  PHP_ESCAPED_DB_PASS=$(printf '%s\n' "$NEW_DB_PASS" | sed -e 's/\\/\\\\/g' -e "s/'/\\\'/g")
  sed -i "s|define( *'DB_PASSWORD',.*);|define('DB_PASSWORD', '$PHP_ESCAPED_DB_PASS');|g" "$CONFIG_FILE"
  sed -i "s|define( *'DB_HOST',.*);|define('DB_HOST', 'localhost');|g" "$CONFIG_FILE"
  if ! grep -q "define( *'DB_HOST'" "$CONFIG_FILE"; then
    sed -i "/define( *'DB_PASSWORD'/a define('DB_HOST', 'localhost');" "$CONFIG_FILE"
  fi
  echo "$(date '+%Y-%m-%d %H:%M:%S') wp-config.php 配置完成"


  # 5. WP-CLI替换命令
  if ! command -v wp &> /dev/null; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') 错误: WP-CLI 未安装，请先安装 WP-CLI！"
    exit 1
  fi

  # 基础的 WP-CLI 选项，供所有命令使用
  WP_CLI_BASE_OPTS="--path=$WEBROOT --allow-root"

  echo "$(date '+%Y-%m-%d %H:%M:%S') 开始执行 WP-CLI search-replace..."
  # 5.1 替换域名（不使用 --skip-columns，恢复你之前的行为）
  wp $WP_CLI_BASE_OPTS search-replace "$MOTHER_DOMAIN" "$NEW_DOMAIN" --all-tables
  wp $WP_CLI_BASE_OPTS search-replace "https://$MOTHER_DOMAIN" "https://$NEW_DOMAIN" --all-tables
  wp $WP_CLI_BASE_OPTS search-replace "http://$MOTHER_DOMAIN" "http://$NEW_DOMAIN" --all-tables

  # 5.2 替换邮箱（不使用 --skip-columns）
  if [ -n "$NEW_EMAIL" ]; then
    wp $WP_CLI_BASE_OPTS search-replace "sale@$MOTHER_DOMAIN" "$NEW_EMAIL" --all-tables
  fi
  echo "$(date '+%Y-%m-%d %H:%M:%S') search-replace 完成."
  
  # 5.3 更新关键WordPress选项
  wp $WP_CLI_BASE_OPTS option update siteurl "https://$NEW_DOMAIN"
  wp $WP_CLI_BASE_OPTS option update home "https://$NEW_DOMAIN"
  if [ -n "$NEW_EMAIL" ]; then
    wp $WP_CLI_BASE_OPTS option update admin_email "$NEW_EMAIL"
  fi

  # 处理 robots.txt 的 Sitemap 行
  ROBOTS="$WEBROOT/robots.txt"
  SITEMAP_LINE="Sitemap: https://$NEW_DOMAIN/sitemap_index.xml"

  if [ -f "$ROBOTS" ]; then
    if grep -q "^Sitemap:" "$ROBOTS"; then
      sed -i "s|^Sitemap:.*|$SITEMAP_LINE|g" "$ROBOTS"
    else
      echo "$SITEMAP_LINE" >> "$ROBOTS"
    fi
  else
    echo "$SITEMAP_LINE" > "$ROBOTS"
  fi
  echo "$(date '+%Y-%m-%d %H:%M:%S') robots.txt 处理完成"

  # 5.4 清理缓存
  wp $WP_CLI_BASE_OPTS cache flush
  wp $WP_CLI_BASE_OPTS rewrite flush --hard # --hard 是个好习惯
  echo "$(date '+%Y-%m-%d %H:%M:%S') 缓存清理完成"

  echo "$NEW_DOMAIN 部署完成！"
  echo "数据库名: $NEW_DB"
  echo "数据库用户: $NEW_DB_USER"
  echo "数据库密码: $NEW_DB_PASS"
  echo "邮箱: $NEW_EMAIL"
  echo "=============================="
done < sites.txt

# set +x
echo "所有站点部署完成！"