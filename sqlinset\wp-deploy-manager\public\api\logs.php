<?php
/**
 * 日志管理API接口
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 安全检查
checkApiAccess();

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $logManager = new LogManager();
    
    switch ($action) {
        case 'get_logs':
            handleGetLogs($logManager);
            break;
            
        case 'get_job_logs':
            handleGetJobLogs($logManager);
            break;
            
        case 'get_stats':
            handleGetStats($logManager);
            break;
            
        case 'export':
            handleExportLogs($logManager);
            break;
            
        case 'cleanup':
            handleCleanupLogs($logManager);
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Logs API error: ' . $e->getMessage());
    errorResponse($e->getMessage(), 500);
}

/**
 * 获取系统日志
 */
function handleGetLogs($logManager) {
    $filters = [
        'level' => sanitizeInput($_GET['level'] ?? ''),
        'source' => sanitizeInput($_GET['source'] ?? ''),
        'start_date' => sanitizeInput($_GET['start_date'] ?? ''),
        'end_date' => sanitizeInput($_GET['end_date'] ?? ''),
        'search' => sanitizeInput($_GET['search'] ?? ''),
        'page' => max(1, intval($_GET['page'] ?? 1)),
        'limit' => min(100, max(10, intval($_GET['limit'] ?? 50)))
    ];
    
    try {
        $result = $logManager->getLogs($filters);
        successResponse($result);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取任务日志
 */
function handleGetJobLogs($logManager) {
    $jobId = intval($_GET['job_id'] ?? 0);
    
    if ($jobId <= 0) {
        errorResponse('Job ID is required');
    }
    
    try {
        $result = $logManager->getJobLogs($jobId);
        successResponse($result);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取日志统计
 */
function handleGetStats($logManager) {
    $days = min(365, max(1, intval($_GET['days'] ?? 7)));
    
    try {
        $result = $logManager->getLogStats($days);
        successResponse($result);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 导出日志
 */
function handleExportLogs($logManager) {
    $filters = [
        'level' => sanitizeInput($_GET['level'] ?? ''),
        'start_date' => sanitizeInput($_GET['start_date'] ?? ''),
        'end_date' => sanitizeInput($_GET['end_date'] ?? ''),
        'search' => sanitizeInput($_GET['search'] ?? '')
    ];
    
    $format = sanitizeInput($_GET['format'] ?? 'json');
    
    if (!in_array($format, ['json', 'csv', 'txt'])) {
        errorResponse('Invalid export format');
    }
    
    try {
        $content = $logManager->exportLogs($filters, $format);
        
        // 设置下载头
        $filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.' . $format;
        
        switch ($format) {
            case 'csv':
                header('Content-Type: text/csv');
                break;
            case 'txt':
                header('Content-Type: text/plain');
                break;
            case 'json':
            default:
                header('Content-Type: application/json');
                break;
        }
        
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($content));
        
        echo $content;
        exit;
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 清理日志
 */
function handleCleanupLogs($logManager) {
    $days = min(365, max(1, intval($_POST['days'] ?? 30)));
    
    try {
        $deletedCount = $logManager->cleanupOldLogs($days);
        
        successResponse([
            'deleted_count' => $deletedCount,
            'days' => $days
        ], "Cleaned up $deletedCount old log entries");
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取实时日志流
 */
function handleLogStream() {
    $jobId = intval($_GET['job_id'] ?? 0);
    $lines = min(100, max(10, intval($_GET['lines'] ?? 50)));
    
    if ($jobId <= 0) {
        errorResponse('Job ID is required');
    }
    
    // 设置SSE头
    header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache');
    header('Connection: keep-alive');
    
    // 获取日志文件路径
    $db = getDatabase();
    $stmt = $db->prepare("SELECT log_file FROM deploy_jobs WHERE id = ?");
    $stmt->execute([$jobId]);
    $logFile = $stmt->fetchColumn();
    
    if (!$logFile || !file_exists($logFile)) {
        echo "event: error\n";
        echo "data: Log file not found\n\n";
        exit;
    }
    
    $lastSize = 0;
    $maxIterations = 300; // 5分钟 (每秒检查一次)
    $iteration = 0;
    
    while ($iteration < $maxIterations) {
        $currentSize = filesize($logFile);
        
        if ($currentSize > $lastSize) {
            // 文件有新内容
            $handle = fopen($logFile, 'r');
            fseek($handle, $lastSize);
            $newContent = fread($handle, $currentSize - $lastSize);
            fclose($handle);
            
            if (!empty($newContent)) {
                $lines = array_filter(explode("\n", $newContent));
                foreach ($lines as $line) {
                    echo "event: log\n";
                    echo "data: " . json_encode(['line' => $line, 'timestamp' => time()]) . "\n\n";
                }
                
                $lastSize = $currentSize;
            }
        }
        
        // 检查任务是否完成
        $stmt = $db->prepare("SELECT status FROM deploy_jobs WHERE id = ?");
        $stmt->execute([$jobId]);
        $status = $stmt->fetchColumn();
        
        if (in_array($status, ['completed', 'failed', 'cancelled'])) {
            echo "event: complete\n";
            echo "data: " . json_encode(['status' => $status]) . "\n\n";
            break;
        }
        
        // 发送心跳
        echo "event: heartbeat\n";
        echo "data: " . json_encode(['timestamp' => time()]) . "\n\n";
        
        ob_flush();
        flush();
        
        sleep(1);
        $iteration++;
    }
    
    echo "event: timeout\n";
    echo "data: Stream timeout\n\n";
    exit;
}
