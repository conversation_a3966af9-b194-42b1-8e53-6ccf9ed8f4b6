#!/usr/bin/env python3
import os
import shutil
import sys

if len(sys.argv) != 4:
    print(f"Usage: {sys.argv[0]} <image_names_file> <source_folder> <target_folder>")
    sys.exit(1)

image_names_file = sys.argv[1]
source_folder = sys.argv[2]
target_folder = sys.argv[3]

# 创建目标目录
os.makedirs(target_folder, exist_ok=True)

with open(image_names_file, 'r', encoding='utf-8') as f:
    lines = f.readlines()

for line in lines:
    image_name = line.strip()
    if not image_name.endswith('.webp') or not image_name:
        continue  # 跳过非图片行

    src = os.path.join(source_folder, image_name)
    dst = os.path.join(target_folder, image_name)

    if os.path.isfile(src):
        shutil.copy2(src, dst)
        print(f"✅ Copied: {image_name}")
    else:
        print(f"❌ File not found: {image_name}")

print("✅ Operation completed.")
