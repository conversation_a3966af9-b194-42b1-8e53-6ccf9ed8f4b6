@echo off
chcp 65001 >nul
echo 🚀 火山引擎版产品优化器
echo ================================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查主脚本
if not exist "product_optimizer_volcengine_simple.py" (
    echo ❌ 主脚本文件不存在
    echo 💡 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 创建必要目录
if not exist "input" mkdir input
if not exist "output" mkdir output
if not exist "logs" mkdir logs

REM 检查输入文件
if not exist "input\*.txt" (
    echo ⚠️ input目录中没有找到txt文件
    echo 💡 请将产品文件放入input目录
    echo.
    echo 📁 示例文件已创建: input\example_products.txt
    echo 🔧 您可以编辑此文件或添加自己的产品文件
    echo.
    pause
)

echo 📁 当前目录: %CD%
echo 📥 输入目录: input\
echo 📤 输出目录: output\
echo 📊 日志目录: logs\
echo.

echo 🚀 启动产品优化器...
echo ================================================
python product_optimizer_volcengine_simple.py

echo.
echo ✅ 处理完成！
echo 📁 查看结果: output\ 目录
pause
