@echo off
chcp 65001 >nul

if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
if "%1"=="test" goto test
if "%1"=="clean" goto clean
goto help

:start
echo 🚀 启动服务...
docker-compose start
goto end

:stop
echo 🛑 停止服务...
docker-compose stop
goto end

:restart
echo 🔄 重启服务...
docker-compose restart
goto end

:status
echo 📊 服务状态...
docker-compose ps
goto end

:logs
echo 📋 查看日志...
docker-compose logs -f
goto end

:test
echo 🧪 运行测试...
echo 测试Web服务...
curl -s http://localhost | findstr "WordPress" >nul
if %errorlevel%==0 (
    echo    ✅ Web服务正常
) else (
    echo    ❌ Web服务异常
)

echo 测试API服务...
curl -s http://localhost/api/ | findstr "WordPress Deploy Manager API" >nul
if %errorlevel%==0 (
    echo    ✅ API服务正常
) else (
    echo    ❌ API服务异常
)

echo 测试phpMyAdmin...
curl -s http://localhost:8080 | findstr "phpMyAdmin" >nul
if %errorlevel%==0 (
    echo    ✅ phpMyAdmin正常
) else (
    echo    ❌ phpMyAdmin异常
)
goto end

:clean
echo 🧹 清理环境...
docker-compose down -v
docker system prune -f
goto end

:help
echo WordPress Deploy Manager 管理脚本
echo.
echo 用法: %0 {start^|stop^|restart^|status^|logs^|test^|clean}
echo.
echo 命令说明:
echo   start   - 启动所有服务
echo   stop    - 停止所有服务
echo   restart - 重启所有服务
echo   status  - 查看服务状态
echo   logs    - 查看服务日志
echo   test    - 运行功能测试
echo   clean   - 清理所有数据

:end
