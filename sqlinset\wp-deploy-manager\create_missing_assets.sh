#!/bin/bash

# 创建缺失的资源文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  创建缺失的资源文件"
echo "==================================================${NC}"

# 创建目录
mkdir -p public/assets/images

# 创建简单的logo.png (使用SVG转换)
echo -e "${BLUE}[1/3]${NC} 创建logo.png..."

# 创建SVG logo
cat > public/assets/images/logo.svg << 'EOF'
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" rx="6" fill="#667eea"/>
  <text x="16" y="22" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">WP</text>
</svg>
EOF

# 如果系统有ImageMagick，转换SVG为PNG
if command -v convert >/dev/null 2>&1; then
    convert public/assets/images/logo.svg public/assets/images/logo.png
    echo "✓ logo.png 已创建"
else
    # 复制SVG作为备用
    cp public/assets/images/logo.svg public/assets/images/logo.png
    echo "✓ logo.svg 已创建 (作为logo.png的备用)"
fi

# 创建favicon.ico
echo -e "${BLUE}[2/3]${NC} 创建favicon.ico..."

# 创建简单的favicon SVG
cat > public/assets/images/favicon.svg << 'EOF'
<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <rect width="16" height="16" rx="3" fill="#667eea"/>
  <text x="8" y="12" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">W</text>
</svg>
EOF

# 如果系统有ImageMagick，转换为ICO
if command -v convert >/dev/null 2>&1; then
    convert public/assets/images/favicon.svg public/assets/images/favicon.ico
    echo "✓ favicon.ico 已创建"
else
    # 复制SVG作为备用
    cp public/assets/images/favicon.svg public/assets/images/favicon.ico
    echo "✓ favicon.svg 已创建 (作为favicon.ico的备用)"
fi

# 修复API路径问题
echo -e "${BLUE}[3/3]${NC} 检查和修复API路径..."

# 检查当前API baseUrl设置
if grep -q "this.baseUrl = '/api'" public/assets/js/api.js; then
    echo "✓ API baseUrl 设置正确"
else
    echo "⚠️  API baseUrl 可能有问题"
fi

# 测试API路径
echo "测试API路径..."
if [ -f "public/api/index.php" ]; then
    echo "✓ public/api/index.php 存在"
else
    echo "✗ public/api/index.php 不存在"
fi

# 检查实际的API文件
echo "检查API文件:"
for api_file in deploy.php templates.php status.php logs.php settings.php sse.php; do
    if [ -f "api/$api_file" ]; then
        echo "✓ api/$api_file 存在"
    else
        echo "✗ api/$api_file 不存在"
    fi
done

# 设置权限
echo -e "${BLUE}设置文件权限...${NC}"
chown -R www:www public/assets/images/
chmod 644 public/assets/images/*

echo -e "${GREEN}=================================================="
echo "  ✅ 资源文件创建完成！"
echo "=================================================="
echo "已创建的文件:"
echo "- public/assets/images/logo.svg"
echo "- public/assets/images/logo.png"
echo "- public/assets/images/favicon.svg" 
echo "- public/assets/images/favicon.ico"
echo ""
echo "下一步: 修复API路径问题"
echo "==================================================${NC}"

exit 0
