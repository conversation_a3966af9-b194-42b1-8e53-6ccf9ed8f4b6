<?php
/**
 * 部署API接口
 * 处理WordPress站点部署相关的API请求
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 安全检查
checkApiAccess();

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $deployManager = new DeployManager();
    
    switch ($action) {
        case 'single_deploy':
            handleSingleDeploy($deployManager);
            break;
            
        case 'batch_deploy':
            handleBatchDeploy($deployManager);
            break;
            
        case 'get_job_status':
            handleGetJobStatus($deployManager);
            break;
            
        case 'get_job_progress':
            handleGetJobProgress($deployManager);
            break;
            
        case 'cancel_job':
            handleCancelJob($deployManager);
            break;
            
        case 'retry_job':
            handleRetryJob($deployManager);
            break;
            
        case 'get_deploy_history':
            handleGetDeployHistory($deployManager);
            break;
            
        case 'get_deploy_stats':
            handleGetDeployStats($deployManager);
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Deploy API error: ' . $e->getMessage(), [
        'action' => $action ?? 'unknown',
        'request_data' => $_POST
    ]);
    errorResponse($e->getMessage(), 500);
}

/**
 * 处理单域名部署
 */
function handleSingleDeploy($deployManager) {
    $domain = sanitizeInput($_POST['domain'] ?? '');
    $templateId = intval($_POST['template_id'] ?? 0);
    $config = $_POST['config'] ?? [];
    
    // 验证输入
    if (empty($domain)) {
        errorResponse('Domain is required');
    }
    
    if (!validateDomain($domain)) {
        errorResponse('Invalid domain format');
    }
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    // 清理配置数据
    $config = sanitizeInput($config);
    
    // 添加默认配置
    $defaultConfig = [
        'auto_ssl' => true,
        'backup_before_deploy' => true,
        'health_check_after_deploy' => true,
        'admin_email' => $config['admin_email'] ?? "admin@$domain"
    ];
    
    $config = array_merge($defaultConfig, $config);
    
    try {
        $result = $deployManager->createSingleDeployJob($domain, $templateId, $config);
        
        successResponse($result, 'Single deployment job created successfully');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 处理批量部署
 */
function handleBatchDeploy($deployManager) {
    $domains = $_POST['domains'] ?? [];
    $templateId = intval($_POST['template_id'] ?? 0);
    $config = $_POST['config'] ?? [];
    
    // 处理域名输入
    if (is_string($domains)) {
        // 如果是字符串，按行分割
        $domains = array_filter(array_map('trim', explode("\n", $domains)));
    } elseif (!is_array($domains)) {
        errorResponse('Domains must be an array or string');
    }
    
    // 验证输入
    if (empty($domains)) {
        errorResponse('At least one domain is required');
    }
    
    if ($templateId <= 0) {
        errorResponse('Valid template ID is required');
    }
    
    // 验证域名数量限制
    if (count($domains) > 50) {
        errorResponse('Maximum 50 domains allowed per batch');
    }
    
    // 清理配置数据
    $config = sanitizeInput($config);
    
    // 添加默认配置
    $defaultConfig = [
        'auto_ssl' => true,
        'backup_before_deploy' => true,
        'health_check_after_deploy' => true,
        'parallel_limit' => min(count($domains), MAX_CONCURRENT_DEPLOYMENTS)
    ];
    
    $config = array_merge($defaultConfig, $config);
    
    try {
        $result = $deployManager->createBatchDeployJob($domains, $templateId, $config);
        
        successResponse($result, 'Batch deployment job created successfully');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取任务状态
 */
function handleGetJobStatus($deployManager) {
    $jobId = intval($_GET['job_id'] ?? 0);
    $jobUuid = sanitizeInput($_GET['job_uuid'] ?? '');
    
    if ($jobId <= 0 && empty($jobUuid)) {
        errorResponse('Job ID or UUID is required');
    }
    
    try {
        $db = getDatabase();
        
        if ($jobId > 0) {
            $stmt = $db->prepare("SELECT * FROM deploy_jobs WHERE id = ?");
            $stmt->execute([$jobId]);
        } else {
            $stmt = $db->prepare("SELECT * FROM deploy_jobs WHERE uuid = ?");
            $stmt->execute([$jobUuid]);
        }
        
        $job = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$job) {
            errorResponse('Job not found', 404);
        }
        
        // 解析配置数据
        $job['config_data'] = json_decode($job['config_data'], true);
        
        // 获取子任务状态（如果是批量任务）
        if ($job['type'] === 'batch') {
            $stmt = $db->prepare("
                SELECT domain, status, progress, current_step, error_message 
                FROM deploy_sub_jobs 
                WHERE parent_job_id = ? 
                ORDER BY id
            ");
            $stmt->execute([$job['id']]);
            $job['sub_jobs'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        successResponse($job);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取任务进度（用于实时更新）
 */
function handleGetJobProgress($deployManager) {
    $jobId = intval($_GET['job_id'] ?? 0);
    
    if ($jobId <= 0) {
        errorResponse('Job ID is required');
    }
    
    try {
        $db = getDatabase();
        
        $stmt = $db->prepare("
            SELECT id, status, progress, current_step, error_message, 
                   started_at, completed_at, updated_at
            FROM deploy_jobs 
            WHERE id = ?
        ");
        $stmt->execute([$jobId]);
        
        $job = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$job) {
            errorResponse('Job not found', 404);
        }
        
        // 计算运行时间
        if ($job['started_at']) {
            $startTime = new DateTime($job['started_at']);
            $endTime = $job['completed_at'] ? new DateTime($job['completed_at']) : new DateTime();
            $job['duration'] = $endTime->getTimestamp() - $startTime->getTimestamp();
        }
        
        // 获取最新日志（如果需要）
        if (isset($_GET['include_logs']) && $_GET['include_logs'] === 'true') {
            $logManager = new LogManager();
            $logs = $logManager->getJobLogsTail($jobId, 10);
            $job['recent_logs'] = $logs;
        }
        
        successResponse($job);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 取消任务
 */
function handleCancelJob($deployManager) {
    $jobId = intval($_POST['job_id'] ?? 0);
    
    if ($jobId <= 0) {
        errorResponse('Job ID is required');
    }
    
    try {
        $queueManager = new QueueManager();
        $result = $queueManager->killJob($jobId, 'user_cancelled');
        
        if ($result) {
            successResponse(['job_id' => $jobId], 'Job cancelled successfully');
        } else {
            errorResponse('Failed to cancel job');
        }
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 重试任务
 */
function handleRetryJob($deployManager) {
    $jobId = intval($_POST['job_id'] ?? 0);
    
    if ($jobId <= 0) {
        errorResponse('Job ID is required');
    }
    
    try {
        $db = getDatabase();
        
        // 获取原任务信息
        $stmt = $db->prepare("SELECT * FROM deploy_jobs WHERE id = ?");
        $stmt->execute([$jobId]);
        $originalJob = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$originalJob) {
            errorResponse('Job not found', 404);
        }
        
        if (!in_array($originalJob['status'], ['failed', 'cancelled'])) {
            errorResponse('Only failed or cancelled jobs can be retried');
        }
        
        // 重置任务状态
        $stmt = $db->prepare("
            UPDATE deploy_jobs 
            SET status = 'pending', progress = 0, current_step = NULL, 
                error_message = NULL, started_at = NULL, completed_at = NULL,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->execute([$jobId]);
        
        // 如果是批量任务，重置子任务
        if ($originalJob['type'] === 'batch') {
            $stmt = $db->prepare("
                UPDATE deploy_sub_jobs 
                SET status = 'pending', progress = 0, current_step = NULL,
                    error_message = NULL, started_at = NULL, completed_at = NULL,
                    updated_at = CURRENT_TIMESTAMP
                WHERE parent_job_id = ?
            ");
            $stmt->execute([$jobId]);
        }
        
        // 重新添加到队列
        $queueManager = new QueueManager();
        $queueManager->addJob($jobId);
        
        successResponse(['job_id' => $jobId], 'Job retried successfully');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取部署历史
 */
function handleGetDeployHistory($deployManager) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 20)));
    $status = sanitizeInput($_GET['status'] ?? '');
    $domain = sanitizeInput($_GET['domain'] ?? '');
    
    try {
        $db = getDatabase();
        
        $sql = "
            SELECT dj.*, t.name as template_name
            FROM deploy_jobs dj
            LEFT JOIN templates t ON dj.template_id = t.id
            WHERE 1=1
        ";
        $params = [];
        
        if ($status) {
            $sql .= " AND dj.status = ?";
            $params[] = $status;
        }
        
        if ($domain) {
            $sql .= " AND dj.domain LIKE ?";
            $params[] = "%$domain%";
        }
        
        // 获取总数
        $countSql = str_replace('SELECT dj.*, t.name as template_name', 'SELECT COUNT(*)', $sql);
        $countStmt = $db->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // 获取分页数据
        $sql .= " ORDER BY dj.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = ($page - 1) * $limit;
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 处理配置数据
        foreach ($jobs as &$job) {
            $job['config_data'] = json_decode($job['config_data'], true);
        }
        
        successResponse([
            'jobs' => $jobs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取部署统计
 */
function handleGetDeployStats($deployManager) {
    $days = min(365, max(1, intval($_GET['days'] ?? 30)));
    
    try {
        $db = getDatabase();
        
        // 获取状态统计
        $stmt = $db->prepare("
            SELECT status, COUNT(*) as count
            FROM deploy_jobs 
            WHERE created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY status
        ");
        $stmt->execute([$days]);
        $statusStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // 获取每日统计
        $stmt = $db->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM deploy_jobs 
            WHERE created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        ");
        $stmt->execute([$days]);
        $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取模板使用统计
        $stmt = $db->prepare("
            SELECT 
                t.name as template_name,
                COUNT(dj.id) as usage_count
            FROM templates t
            LEFT JOIN deploy_jobs dj ON t.id = dj.template_id 
                AND dj.created_at >= datetime('now', '-' || ? || ' days')
            WHERE t.status = 'active'
            GROUP BY t.id, t.name
            ORDER BY usage_count DESC
            LIMIT 10
        ");
        $stmt->execute([$days]);
        $templateStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        successResponse([
            'status_stats' => $statusStats,
            'daily_stats' => $dailyStats,
            'template_stats' => $templateStats,
            'period_days' => $days
        ]);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}
