<?php
/**
 * API路由入口文件
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$endpoint = str_replace('/api/', '', $path);

// 简单的路由处理
if (empty($endpoint) || $endpoint === 'index.php') {
    // 默认API信息
    echo json_encode([
        'success' => true,
        'message' => 'WordPress Deploy Manager API',
        'version' => '1.0.0',
        'endpoints' => [
            'status' => '/api/status.php',
            'deploy' => '/api/deploy.php',
            'templates' => '/api/templates.php',
            'logs' => '/api/logs.php',
            'settings' => '/api/settings.php'
        ],
        'timestamp' => date('Y-m-d H:i:s'),
        'server_info' => [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'
        ]
    ], JSON_PRETTY_PRINT);
} elseif ($endpoint === 'status.php') {
    // 系统状态API
    $action = $_GET['action'] ?? 'system';
    
    if ($action === 'system') {
        echo json_encode([
            'success' => true,
            'message' => 'System status retrieved successfully',
            'data' => [
                'server_time' => date('Y-m-d H:i:s'),
                'php_version' => PHP_VERSION,
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'memory_limit' => ini_get('memory_limit'),
                'cpu_usage' => '15%', // 模拟数据
                'disk_usage' => '45%', // 模拟数据
                'database_status' => 'connected', // 模拟数据
                'service_status' => 'running'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action for status endpoint',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
} elseif ($endpoint === 'templates.php') {
    // 模板管理API
    $action = $_GET['action'] ?? 'list';
    
    if ($action === 'list') {
        echo json_encode([
            'success' => true,
            'message' => 'Templates retrieved successfully',
            'data' => [
                [
                    'id' => 1,
                    'uuid' => 'test-template-1',
                    'name' => '测试模板1',
                    'description' => '这是一个测试模板',
                    'filename' => 'test1.tar.gz',
                    'file_size' => 1024000,
                    'status' => 'active',
                    'created_at' => '2024-07-26 10:00:00'
                ],
                [
                    'id' => 2,
                    'uuid' => 'test-template-2',
                    'name' => '测试模板2',
                    'description' => '这是另一个测试模板',
                    'filename' => 'test2.tar.gz',
                    'file_size' => 2048000,
                    'status' => 'active',
                    'created_at' => '2024-07-26 09:30:00'
                ]
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action for templates endpoint',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
} elseif ($endpoint === 'deploy.php') {
    // 部署管理API
    $action = $_GET['action'] ?? 'get_deploy_stats';
    
    if ($action === 'get_deploy_stats') {
        echo json_encode([
            'success' => true,
            'message' => 'Deploy stats retrieved successfully',
            'data' => [
                'total_sites' => 5,
                'successful_deploys' => 12,
                'pending_tasks' => 2,
                'active_templates' => 2,
                'failed_deploys' => 1,
                'total_deploys' => 15
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } elseif ($action === 'get_deploy_history') {
        echo json_encode([
            'success' => true,
            'message' => 'Deploy history retrieved successfully',
            'data' => [
                [
                    'id' => 1,
                    'uuid' => 'deploy-001',
                    'type' => 'single',
                    'domains' => ['example.com'],
                    'status' => 'completed',
                    'progress' => 100,
                    'created_at' => '2024-07-26 10:30:00'
                ],
                [
                    'id' => 2,
                    'uuid' => 'deploy-002',
                    'type' => 'single',
                    'domains' => ['test.com'],
                    'status' => 'pending',
                    'progress' => 0,
                    'created_at' => '2024-07-26 10:25:00'
                ]
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action for deploy endpoint',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
} elseif ($endpoint === 'logs.php') {
    // 日志管理API
    $action = $_GET['action'] ?? 'get_logs';
    
    if ($action === 'get_logs') {
        echo json_encode([
            'success' => true,
            'message' => 'Logs retrieved successfully',
            'data' => [
                [
                    'id' => 1,
                    'level' => 'INFO',
                    'message' => '系统启动完成',
                    'created_at' => '2024-07-26 10:30:15'
                ],
                [
                    'id' => 2,
                    'level' => 'INFO',
                    'message' => '数据库连接成功',
                    'created_at' => '2024-07-26 10:30:10'
                ],
                [
                    'id' => 3,
                    'level' => 'DEBUG',
                    'message' => '加载配置文件',
                    'created_at' => '2024-07-26 10:30:05'
                ]
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action for logs endpoint',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
} elseif ($endpoint === 'settings.php') {
    // 设置管理API
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get';
    
    if ($action === 'get') {
        echo json_encode([
            'success' => true,
            'message' => 'Settings retrieved successfully',
            'data' => [
                'max_concurrent_jobs' => 3,
                'default_timeout' => 1800,
                'enable_notifications' => false,
                'notification_email' => '',
                'backup_before_deploy' => true,
                'auto_ssl_setup' => false
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } elseif ($action === 'save') {
        // 模拟保存设置
        echo json_encode([
            'success' => true,
            'message' => 'Settings saved successfully',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action for settings endpoint',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
} else {
    // 未知端点
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'error' => 'API endpoint not found: ' . $endpoint,
        'available_endpoints' => [
            'status.php',
            'templates.php',
            'deploy.php',
            'logs.php',
            'settings.php'
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
