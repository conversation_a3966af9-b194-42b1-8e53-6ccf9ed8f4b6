# 🚀 WordPress Deploy Manager 本地部署完整指南

## 📋 目录
1. [环境要求](#环境要求)
2. [本地环境搭建](#本地环境搭建)
3. [项目部署](#项目部署)
4. [问题修复](#问题修复)
5. [测试验证](#测试验证)
6. [生产部署](#生产部署)

## 🔧 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 4GB，推荐 8GB+
- **磁盘空间**: 最低 10GB 可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP**: 7.4+ (推荐 8.1+)
- **MySQL**: 5.7+ 或 8.0+
- **Node.js**: 16+ (用于前端工具，可选)
- **Git**: 最新版本

### PHP扩展要求
```bash
# 必需扩展
php-mysql
php-pdo
php-json
php-curl
php-mbstring
php-zip
php-xml
php-gd

# 推荐扩展
php-opcache
php-redis (如果使用Redis)
php-imagick
```

## 🏗️ 本地环境搭建

### 方案一：使用XAMPP (推荐新手)

#### 1. 下载安装XAMPP
```bash
# Windows
https://www.apachefriends.org/download.html

# macOS
brew install --cask xampp

# Linux
wget https://www.apachefriends.org/xampp-files/8.2.12/xampp-linux-x64-8.2.12-0-installer.run
chmod +x xampp-linux-x64-8.2.12-0-installer.run
sudo ./xampp-linux-x64-8.2.12-0-installer.run
```

#### 2. 配置虚拟主机
编辑 `httpd-vhosts.conf`:
```apache
# Windows: C:\xampp\apache\conf\extra\httpd-vhosts.conf
# macOS/Linux: /Applications/XAMPP/etc/extra/httpd-vhosts.conf

<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/wp-deploy-manager/public"
    ServerName wp-deploy.local
    ServerAlias www.wp-deploy.local
    
    <Directory "C:/xampp/htdocs/wp-deploy-manager/public">
        AllowOverride All
        Require all granted
        
        # 启用重写模块
        RewriteEngine On
        
        # API路由重写
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog "logs/wp-deploy-error.log"
    CustomLog "logs/wp-deploy-access.log" common
</VirtualHost>
```

#### 3. 配置hosts文件
```bash
# Windows: C:\Windows\System32\drivers\etc\hosts
# macOS/Linux: /etc/hosts

127.0.0.1 wp-deploy.local
127.0.0.1 www.wp-deploy.local
```

### 方案二：使用Docker (推荐开发者)

#### 1. 创建Docker Compose文件
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    image: php:8.1-apache
    container_name: wp-deploy-web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/apache/vhost.conf:/etc/apache2/sites-available/000-default.conf
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    depends_on:
      - mysql
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    networks:
      - wp-deploy-network

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_mysql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - wp-deploy-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
    depends_on:
      - mysql
    networks:
      - wp-deploy-network

volumes:
  mysql_data:

networks:
  wp-deploy-network:
    driver: bridge
```

#### 2. 创建Apache配置
```apache
# docker/apache/vhost.conf
<VirtualHost *:80>
    DocumentRoot /var/www/html/public
    ServerName localhost
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
```

#### 3. 启动Docker环境
```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f web
```

### 方案三：原生环境 (Linux/macOS)

#### 1. 安装必需软件
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install apache2 mysql-server php8.1 php8.1-mysql php8.1-curl php8.1-json php8.1-mbstring php8.1-zip php8.1-xml php8.1-gd

# CentOS/RHEL
sudo yum install httpd mysql-server php php-mysql php-curl php-json php-mbstring php-zip php-xml php-gd

# macOS (使用Homebrew)
brew install apache2 mysql php@8.1
brew services start apache2
brew services start mysql
```

#### 2. 配置Apache
```apache
# /etc/apache2/sites-available/wp-deploy.conf
<VirtualHost *:80>
    DocumentRoot /var/www/wp-deploy-manager/public
    ServerName wp-deploy.local
    
    <Directory /var/www/wp-deploy-manager/public>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/wp-deploy-error.log
    CustomLog ${APACHE_LOG_DIR}/wp-deploy-access.log combined
</VirtualHost>
```

```bash
# 启用站点和模块
sudo a2ensite wp-deploy.conf
sudo a2enmod rewrite
sudo systemctl restart apache2
```

## 📦 项目部署

### 1. 获取项目代码
```bash
# 方法一：从线上服务器下载
scp -r root@your-server:/www/wwwroot/wpd.cloudcheckout.shop /path/to/local/wp-deploy-manager

# 方法二：从Git仓库克隆（如果有）
git clone https://github.com/your-repo/wp-deploy-manager.git
cd wp-deploy-manager

# 方法三：手动创建项目结构
mkdir -p wp-deploy-manager/{public,api,core,config,database,logs,uploads,scripts,templates}
```

### 2. 设置目录权限
```bash
# Linux/macOS
sudo chown -R www-data:www-data /var/www/wp-deploy-manager
sudo chmod -R 755 /var/www/wp-deploy-manager
sudo chmod -R 777 /var/www/wp-deploy-manager/logs
sudo chmod -R 777 /var/www/wp-deploy-manager/uploads

# 或者使用当前用户
sudo chown -R $USER:$USER /var/www/wp-deploy-manager
chmod -R 755 /var/www/wp-deploy-manager
chmod -R 777 logs uploads
```

### 3. 配置数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE wp_deploy_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'wp_deploy'@'localhost' IDENTIFIED BY 'wp_deploy_pass_2024';
GRANT ALL PRIVILEGES ON wp_deploy_manager.* TO 'wp_deploy'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据库结构
mysql -u wp_deploy -p wp_deploy_manager < database/init_mysql.sql
```

### 4. 配置应用
```bash
# 复制配置文件模板
cp config/database.example.php config/database.php
cp config/app.example.json config/app.json

# 编辑数据库配置
nano config/database.php
```

## 🔧 配置文件设置

### 1. 数据库配置 (config/database.php)
```php
<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);
?>
```

### 2. 应用配置 (config/app.json)
```json
{
    "mysql_root_password": "wp_deploy_2024",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "<EMAIL>",
    "backup_before_deploy": true,
    "auto_ssl_setup": false,
    "debug_mode": true,
    "log_level": "DEBUG"
}
```

### 3. 主配置文件 (config/config.php)
```php
<?php
// 开发环境配置
define('ENVIRONMENT', 'development');
define('DEBUG_MODE', true);

// 基础路径配置
define('ROOT_PATH', dirname(__DIR__));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('API_PATH', ROOT_PATH . '/api');
define('CORE_PATH', ROOT_PATH . '/core');
define('DATABASE_PATH', ROOT_PATH . '/database');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// 本地开发配置
define('MAX_UPLOAD_SIZE', 100 * 1024 * 1024); // 100MB
define('ALLOWED_EXTENSIONS', ['tar.gz', 'tgz', 'zip']);
define('MAX_CONCURRENT_DEPLOYMENTS', 2);
define('DEPLOYMENT_TIMEOUT', 900); // 15分钟

// 禁用原始部署脚本（本地开发）
define('ORIGINAL_DEPLOY_SCRIPT', '');
define('ENABLE_ACTUAL_DEPLOYMENT', false);

// 安全配置
define('API_SECRET_KEY', 'local_dev_' . md5(__DIR__));
define('SESSION_TIMEOUT', 7200); // 2小时

// 日志配置
define('LOG_LEVEL', 'DEBUG');
define('LOG_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('LOG_MAX_FILES', 5);

// 时区配置
define('TIMEZONE', 'Asia/Shanghai');
date_default_timezone_set(TIMEZONE);

// 加载数据库配置
require_once ROOT_PATH . '/config/database.php';
?>
```

## 🛠️ 问题修复和完善

### 1. 创建修复脚本
```bash
# 创建修复脚本
cat > fix_local_issues.php << 'EOF'
<?php
/**
 * 本地环境问题修复脚本
 */

echo "🔧 开始修复本地环境问题...\n";

// 1. 修复JavaScript baseUrl问题
echo "1. 修复JavaScript API baseUrl...\n";
$apiJsFile = __DIR__ . '/public/assets/js/api.js';
if (file_exists($apiJsFile)) {
    $content = file_get_contents($apiJsFile);

    // 确保baseUrl正确
    $content = preg_replace(
        '/this\.baseUrl\s*=\s*[\'"][^\'"]*[\'"]/',
        "this.baseUrl = '/api'",
        $content
    );

    // 移除多余的版本注释
    $content = preg_replace('/^\/\* Version:.*\*\/\n/m', '', $content);

    // 添加单个版本注释
    $version = date('YmdHis');
    $content = "/* Version: $version - Local Fixed */\n" . $content;

    file_put_contents($apiJsFile, $content);
    echo "   ✅ API.js 修复完成\n";
} else {
    echo "   ❌ API.js 文件不存在\n";
}

// 2. 修复HTML中的JavaScript引用
echo "2. 修复HTML中的JavaScript引用...\n";
$indexFile = __DIR__ . '/public/index.html';
if (file_exists($indexFile)) {
    $content = file_get_contents($indexFile);

    // 移除可能的错误版本参数
    $content = preg_replace('/\?v=[^"]*%[^"]*/', '', $content);

    // 添加正确的版本参数
    $version = date('YmdHis');
    $content = preg_replace(
        '/src="assets\/js\/([^"]+\.js)"/',
        'src="assets/js/$1?v=' . $version . '"',
        $content
    );

    file_put_contents($indexFile, $content);
    echo "   ✅ index.html 修复完成\n";
} else {
    echo "   ❌ index.html 文件不存在\n";
}

// 3. 创建必要的目录
echo "3. 创建必要的目录...\n";
$dirs = ['logs', 'uploads', 'uploads/temp', 'uploads/templates'];
foreach ($dirs as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    if (!is_dir($fullPath)) {
        mkdir($fullPath, 0777, true);
        echo "   ✅ 创建目录: $dir\n";
    }
}

// 4. 创建测试数据
echo "4. 创建测试数据...\n";
try {
    require_once __DIR__ . '/config/config.php';
    $db = getDatabase();

    // 检查是否有测试数据
    $stmt = $db->query("SELECT COUNT(*) FROM templates");
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        // 插入测试模板
        $stmt = $db->prepare("
            INSERT INTO templates (uuid, name, description, filename, file_path, file_size, file_hash, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $testData = [
            [
                'uuid' => 'test-' . uniqid(),
                'name' => '测试模板1',
                'description' => '这是一个测试模板',
                'filename' => 'test-template-1.tar.gz',
                'file_path' => '/uploads/templates/test-template-1.tar.gz',
                'file_size' => 1024000,
                'file_hash' => md5('test-template-1'),
                'status' => 'active'
            ],
            [
                'uuid' => 'test-' . uniqid(),
                'name' => '测试模板2',
                'description' => '这是另一个测试模板',
                'filename' => 'test-template-2.tar.gz',
                'file_path' => '/uploads/templates/test-template-2.tar.gz',
                'file_size' => 2048000,
                'file_hash' => md5('test-template-2'),
                'status' => 'active'
            ]
        ];

        foreach ($testData as $data) {
            $stmt->execute(array_values($data));
        }

        echo "   ✅ 测试数据创建完成\n";
    } else {
        echo "   ℹ️  测试数据已存在 ($count 条记录)\n";
    }

} catch (Exception $e) {
    echo "   ❌ 数据库操作失败: " . $e->getMessage() . "\n";
}

// 5. 设置文件权限
echo "5. 设置文件权限...\n";
if (PHP_OS_FAMILY !== 'Windows') {
    chmod(__DIR__ . '/logs', 0777);
    chmod(__DIR__ . '/uploads', 0777);
    echo "   ✅ 权限设置完成\n";
} else {
    echo "   ℹ️  Windows系统，跳过权限设置\n";
}

echo "\n🎉 修复完成！请访问 http://wp-deploy.local 测试\n";
?>
EOF

# 运行修复脚本
php fix_local_issues.php
```

### 2. 创建开发工具脚本
```bash
# 创建开发服务器启动脚本
cat > start_dev_server.sh << 'EOF'
#!/bin/bash

echo "🚀 启动WordPress Deploy Manager开发服务器..."

# 检查PHP版本
PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
echo "PHP版本: $PHP_VERSION"

# 检查必需扩展
REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "json" "curl" "mbstring")
for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "$ext"; then
        echo "✅ $ext 扩展已安装"
    else
        echo "❌ $ext 扩展未安装"
        exit 1
    fi
done

# 检查数据库连接
echo "检查数据库连接..."
php -r "
require_once 'config/config.php';
try {
    \$db = getDatabase();
    echo '✅ 数据库连接成功\n';
} catch (Exception \$e) {
    echo '❌ 数据库连接失败: ' . \$e->getMessage() . '\n';
    exit(1);
}
"

# 启动内置服务器（如果没有Apache/Nginx）
if ! command -v apache2 &> /dev/null && ! command -v nginx &> /dev/null; then
    echo "启动PHP内置服务器..."
    echo "访问地址: http://localhost:8080"
    cd public && php -S localhost:8080
else
    echo "✅ 开发环境准备就绪"
    echo "访问地址: http://wp-deploy.local"
fi
EOF

chmod +x start_dev_server.sh
```

### 3. 创建API测试脚本
```bash
# 创建API测试脚本
cat > test_api.php << 'EOF'
<?php
/**
 * API功能测试脚本
 */

require_once 'config/config.php';

echo "🧪 开始API功能测试...\n\n";

// 测试数据库连接
echo "1. 测试数据库连接...\n";
try {
    $db = getDatabase();
    echo "   ✅ 数据库连接成功\n";
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试API端点
echo "\n2. 测试API端点...\n";
$apiEndpoints = [
    '/api/' => '基础API信息',
    '/api/templates.php?action=list' => '模板列表',
    '/api/status.php?action=system' => '系统状态',
    '/api/deploy.php?action=get_deploy_history' => '部署历史'
];

foreach ($apiEndpoints as $endpoint => $description) {
    echo "   测试: $description ($endpoint)\n";

    // 模拟HTTP请求
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = $endpoint;

    // 解析查询参数
    $parts = parse_url($endpoint);
    if (isset($parts['query'])) {
        parse_str($parts['query'], $_GET);
    }

    ob_start();
    try {
        if (strpos($endpoint, '/api/templates.php') !== false) {
            include 'api/templates.php';
        } elseif (strpos($endpoint, '/api/status.php') !== false) {
            include 'api/status.php';
        } elseif (strpos($endpoint, '/api/deploy.php') !== false) {
            include 'api/deploy.php';
        } else {
            include 'public/api/index.php';
        }

        $output = ob_get_clean();
        $data = json_decode($output, true);

        if ($data && isset($data['success'])) {
            echo "      ✅ 响应正常\n";
        } else {
            echo "      ⚠️  响应异常: " . substr($output, 0, 100) . "\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "      ❌ 错误: " . $e->getMessage() . "\n";
    }

    // 清理全局变量
    $_GET = [];
}

// 测试前端资源
echo "\n3. 测试前端资源...\n";
$frontendFiles = [
    'public/index.html' => 'HTML主页',
    'public/assets/css/main.css' => '主样式文件',
    'public/assets/js/api.js' => 'API脚本',
    'public/assets/js/main.js' => '主脚本'
];

foreach ($frontendFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ $description 存在\n";

        // 检查JavaScript文件的baseUrl
        if (strpos($file, 'api.js') !== false) {
            $content = file_get_contents($file);
            if (strpos($content, "baseUrl = '/api'") !== false) {
                echo "      ✅ API baseUrl 配置正确\n";
            } else {
                echo "      ❌ API baseUrl 配置错误\n";
            }
        }
    } else {
        echo "   ❌ $description 不存在\n";
    }
}

echo "\n🎉 API测试完成！\n";
?>
EOF
```

### 4. 创建数据库管理脚本
```bash
# 创建数据库管理脚本
cat > manage_database.php << 'EOF'
<?php
/**
 * 数据库管理脚本
 */

require_once 'config/config.php';

function showUsage() {
    echo "用法: php manage_database.php [命令]\n";
    echo "命令:\n";
    echo "  init     - 初始化数据库表\n";
    echo "  reset    - 重置数据库（清空所有数据）\n";
    echo "  seed     - 填充测试数据\n";
    echo "  status   - 显示数据库状态\n";
    echo "  backup   - 备份数据库\n";
    echo "  restore  - 恢复数据库\n";
}

function initDatabase() {
    echo "初始化数据库表...\n";
    try {
        $db = getDatabase();
        $sql = file_get_contents('database/init_mysql.sql');
        $db->exec($sql);
        echo "✅ 数据库表初始化成功\n";
    } catch (Exception $e) {
        echo "❌ 初始化失败: " . $e->getMessage() . "\n";
    }
}

function resetDatabase() {
    echo "重置数据库...\n";
    try {
        $db = getDatabase();

        // 获取所有表
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        // 删除所有表
        $db->exec("SET FOREIGN_KEY_CHECKS = 0");
        foreach ($tables as $table) {
            $db->exec("DROP TABLE IF EXISTS `$table`");
        }
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");

        echo "✅ 数据库重置成功\n";

        // 重新初始化
        initDatabase();
    } catch (Exception $e) {
        echo "❌ 重置失败: " . $e->getMessage() . "\n";
    }
}

function seedDatabase() {
    echo "填充测试数据...\n";
    try {
        $db = getDatabase();

        // 插入测试模板
        $stmt = $db->prepare("
            INSERT INTO templates (uuid, name, description, filename, file_path, file_size, file_hash, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $testTemplates = [
            [
                'uuid' => 'template-' . uniqid(),
                'name' => 'WordPress基础模板',
                'description' => '包含WordPress核心文件的基础模板',
                'filename' => 'wordpress-basic.tar.gz',
                'file_path' => '/uploads/templates/wordpress-basic.tar.gz',
                'file_size' => 15728640, // 15MB
                'file_hash' => md5('wordpress-basic-template'),
                'status' => 'active'
            ],
            [
                'uuid' => 'template-' . uniqid(),
                'name' => 'WooCommerce商城模板',
                'description' => '包含WooCommerce插件的电商模板',
                'filename' => 'woocommerce-shop.tar.gz',
                'file_path' => '/uploads/templates/woocommerce-shop.tar.gz',
                'file_size' => 31457280, // 30MB
                'file_hash' => md5('woocommerce-shop-template'),
                'status' => 'active'
            ]
        ];

        foreach ($testTemplates as $template) {
            $stmt->execute(array_values($template));
        }

        echo "✅ 测试数据填充成功\n";
    } catch (Exception $e) {
        echo "❌ 填充失败: " . $e->getMessage() . "\n";
    }
}

function showStatus() {
    echo "数据库状态:\n";
    try {
        $db = getDatabase();

        // 显示表信息
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "表数量: " . count($tables) . "\n";

        foreach ($tables as $table) {
            $stmt = $db->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "  $table: $count 条记录\n";
        }

    } catch (Exception $e) {
        echo "❌ 获取状态失败: " . $e->getMessage() . "\n";
    }
}

// 主程序
$command = $argv[1] ?? '';

switch ($command) {
    case 'init':
        initDatabase();
        break;
    case 'reset':
        resetDatabase();
        break;
    case 'seed':
        seedDatabase();
        break;
    case 'status':
        showStatus();
        break;
    default:
        showUsage();
        break;
}
?>
EOF
```

## 🧪 测试验证

### 1. 基础功能测试
```bash
# 运行修复脚本
php fix_local_issues.php

# 测试数据库
php manage_database.php status

# 测试API
php test_api.php

# 启动开发服务器
./start_dev_server.sh
```

### 2. 前端功能测试
访问 `http://wp-deploy.local` 或 `http://localhost:8080`，测试以下功能：

#### 基础功能检查
- [ ] 页面正常加载，无JavaScript错误
- [ ] 导航菜单可以正常切换
- [ ] API调用返回正确响应
- [ ] 仪表板显示系统信息

#### 模块功能测试
- [ ] **仪表板**: 显示统计信息、系统状态
- [ ] **部署管理**: 单域名部署、批量部署界面
- [ ] **模板管理**: 模板列表、上传功能
- [ ] **监控中心**: 系统资源、健康检查
- [ ] **日志管理**: 日志查看、筛选功能
- [ ] **系统设置**: 配置保存、重置功能

### 3. API端点测试
```bash
# 测试基础API
curl http://wp-deploy.local/api/

# 测试模板API
curl http://wp-deploy.local/api/templates.php?action=list

# 测试状态API
curl http://wp-deploy.local/api/status.php?action=system

# 测试部署API
curl http://wp-deploy.local/api/deploy.php?action=get_deploy_history
```

### 4. 错误日志检查
```bash
# 检查Apache错误日志
tail -f /var/log/apache2/error.log

# 检查应用日志
tail -f logs/system_*.log

# 检查PHP错误日志
tail -f /var/log/php_errors.log
```

## 🚀 生产部署准备

### 1. 创建部署包
```bash
# 创建部署脚本
cat > create_deployment_package.sh << 'EOF'
#!/bin/bash

echo "📦 创建生产部署包..."

# 创建部署目录
DEPLOY_DIR="wp-deploy-manager-production-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$DEPLOY_DIR"

# 复制必要文件
cp -r api core config database public scripts templates "$DEPLOY_DIR/"
cp *.md *.php *.sh "$DEPLOY_DIR/"

# 创建生产配置
cp config/config.php "$DEPLOY_DIR/config/config.production.php"
cp config/database.php "$DEPLOY_DIR/config/database.production.php"

# 修改生产配置
sed -i "s/define('ENVIRONMENT', 'development')/define('ENVIRONMENT', 'production')/" "$DEPLOY_DIR/config/config.production.php"
sed -i "s/define('DEBUG_MODE', true)/define('DEBUG_MODE', false)/" "$DEPLOY_DIR/config/config.production.php"

# 创建部署说明
cat > "$DEPLOY_DIR/PRODUCTION_DEPLOY.md" << 'DEPLOY_EOF'
# 生产环境部署说明

## 1. 上传文件到服务器
```bash
scp -r wp-deploy-manager-production-* root@your-server:/www/wwwroot/
```

## 2. 配置数据库
```bash
# 编辑数据库配置
nano config/database.production.php

# 初始化数据库
mysql -u root -p your_database < database/init_mysql.sql
```

## 3. 配置Web服务器
```bash
# 复制Nginx配置
cp nginx.conf /etc/nginx/sites-available/wp-deploy-manager
ln -s /etc/nginx/sites-available/wp-deploy-manager /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

## 4. 设置权限
```bash
chown -R www:www /www/wwwroot/wp-deploy-manager
chmod -R 755 /www/wwwroot/wp-deploy-manager
chmod -R 777 logs uploads
```

## 5. 测试部署
访问您的域名，确认所有功能正常工作。
DEPLOY_EOF

# 创建压缩包
tar -czf "$DEPLOY_DIR.tar.gz" "$DEPLOY_DIR"

echo "✅ 部署包创建完成: $DEPLOY_DIR.tar.gz"
echo "📁 部署目录: $DEPLOY_DIR"
EOF

chmod +x create_deployment_package.sh
```

### 2. 生产环境配置模板
```nginx
# nginx.conf - 生产环境Nginx配置
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /www/wwwroot/wp-deploy-manager/public;
    index index.html index.php;

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # API路由
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全限制
    location ~ /\. {
        deny all;
    }

    location ~ /(config|database|logs|uploads/temp)/ {
        deny all;
    }
}
```

### 3. 同步到线上服务器
```bash
# 创建同步脚本
cat > sync_to_production.sh << 'EOF'
#!/bin/bash

SERVER="root@your-server-ip"
REMOTE_PATH="/www/wwwroot/wp-deploy-manager"

echo "🚀 同步到生产服务器..."

# 创建备份
ssh $SERVER "cd $REMOTE_PATH && tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz ."

# 同步文件（排除敏感文件）
rsync -avz --exclude='config/database.php' --exclude='logs/*' --exclude='uploads/*' \
    ./ $SERVER:$REMOTE_PATH/

# 重启服务
ssh $SERVER "systemctl reload nginx && systemctl reload php8.1-fpm"

echo "✅ 同步完成！"
EOF

chmod +x sync_to_production.sh
```

## 📚 总结

本指南提供了完整的本地部署方案，包括：

1. **多种环境搭建方式**: XAMPP、Docker、原生环境
2. **完整的配置说明**: 数据库、Web服务器、应用配置
3. **问题修复脚本**: 自动修复常见问题
4. **开发工具**: 测试脚本、数据库管理工具
5. **生产部署准备**: 部署包创建、配置模板

按照本指南操作，您可以：
- ✅ 在本地完整复现线上环境
- ✅ 修复所有已知问题
- ✅ 进行全面的功能测试
- ✅ 准备生产环境部署

如需帮助，请参考各个脚本的输出信息或查看日志文件。
```
