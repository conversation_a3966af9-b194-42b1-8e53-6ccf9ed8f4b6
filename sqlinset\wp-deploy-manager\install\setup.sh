#!/bin/bash

# WordPress 部署管理系统环境设置脚本
# 用于在宝塔面板环境下快速部署系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="wp-deploy-manager"
WEB_ROOT="/www/wwwroot"
NGINX_CONF_DIR="/www/server/panel/vhost/nginx"
PHP_VERSION="80" # 默认PHP 8.0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  WordPress 部署管理系统 - 环境设置脚本"
    echo "=================================================="
    echo -e "${NC}"
    echo "本脚本将为您自动配置WordPress部署管理系统的运行环境"
    echo ""
}

# 检查运行环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查宝塔面板
    if [ ! -d "/www/server/panel" ]; then
        log_warning "未检测到宝塔面板，某些功能可能无法正常工作"
    else
        log_success "检测到宝塔面板环境"
    fi
    
    # 检查PHP
    if ! command -v php &> /dev/null; then
        log_error "PHP未安装，请先安装PHP 8.0或更高版本"
        exit 1
    fi
    
    local php_version=$(php -r "echo PHP_VERSION;")
    if ! php -r "exit(version_compare(PHP_VERSION, '8.0.0', '>=') ? 0 : 1);"; then
        log_error "PHP版本过低: $php_version，需要8.0或更高版本"
        exit 1
    fi
    
    log_success "PHP版本检查通过: $php_version"
    
    # 检查必需的PHP扩展
    local required_extensions=("pdo_sqlite" "json" "curl" "zip")
    for ext in "${required_extensions[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            log_error "PHP扩展 $ext 未安装"
            exit 1
        fi
    done
    
    log_success "PHP扩展检查通过"
    
    # 检查原始部署脚本
    local original_script="/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh"
    if [ ! -f "$original_script" ]; then
        log_warning "原始部署脚本不存在: $original_script"
        log_warning "请确保原始部署脚本已正确安装"
    elif [ ! -x "$original_script" ]; then
        log_warning "原始部署脚本不可执行，正在设置执行权限..."
        chmod +x "$original_script"
    else
        log_success "原始部署脚本检查通过"
    fi
}

# 创建项目目录结构
create_directory_structure() {
    log_info "创建项目目录结构..."
    
    local target_dir="$WEB_ROOT/$PROJECT_NAME"
    
    # 如果目标目录已存在，询问是否覆盖
    if [ -d "$target_dir" ]; then
        echo -n "目标目录已存在，是否覆盖? (y/N): "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "安装已取消"
            exit 0
        fi
        
        log_info "备份现有目录..."
        mv "$target_dir" "${target_dir}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 复制项目文件
    log_info "复制项目文件到 $target_dir"
    cp -r "$PROJECT_ROOT" "$target_dir"
    
    # 创建必要的目录
    local directories=(
        "$target_dir/logs"
        "$target_dir/uploads"
        "$target_dir/uploads/temp"
        "$target_dir/templates/uploads"
        "$target_dir/templates/configs"
        "$target_dir/database"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录结构创建完成"
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    local target_dir="$WEB_ROOT/$PROJECT_NAME"
    
    # 设置基础权限
    chown -R www:www "$target_dir"
    find "$target_dir" -type d -exec chmod 755 {} \;
    find "$target_dir" -type f -exec chmod 644 {} \;
    
    # 设置可执行权限
    chmod +x "$target_dir/scripts/"*.sh
    chmod +x "$target_dir/install/setup.sh"
    
    # 设置写入权限
    local writable_dirs=(
        "$target_dir/logs"
        "$target_dir/uploads"
        "$target_dir/templates"
        "$target_dir/database"
        "$target_dir/config"
    )
    
    for dir in "${writable_dirs[@]}"; do
        chmod 755 "$dir"
        chown www:www "$dir"
    done
    
    log_success "文件权限设置完成"
}

# 配置Nginx虚拟主机
configure_nginx() {
    log_info "配置Nginx虚拟主机..."
    
    local domain="$1"
    local target_dir="$WEB_ROOT/$PROJECT_NAME"
    local conf_file="$NGINX_CONF_DIR/${domain}.conf"
    
    # 创建Nginx配置文件
    cat > "$conf_file" << EOF
server {
    listen 80;
    server_name $domain;
    index index.html index.htm index.php;
    root $target_dir/public;
    
    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ ^/(config|database|logs|scripts|install)/ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/tmp/php-cgi-$PHP_VERSION.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        fastcgi_param PATH_INFO \$fastcgi_path_info;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API路由
    location /api/ {
        try_files \$uri \$uri/ /api/index.php?\$query_string;
    }
    
    # 默认路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 日志
    access_log $target_dir/logs/nginx_access.log;
    error_log $target_dir/logs/nginx_error.log;
}
EOF
    
    # 测试Nginx配置
    if nginx -t; then
        # 重载Nginx配置
        nginx -s reload
        log_success "Nginx配置已更新"
    else
        log_error "Nginx配置测试失败"
        rm -f "$conf_file"
        return 1
    fi
}

# 安装依赖工具
install_dependencies() {
    log_info "安装依赖工具..."
    
    # 检查并安装jq
    if ! command -v jq &> /dev/null; then
        log_info "安装jq..."
        if command -v yum &> /dev/null; then
            yum install -y jq
        elif command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y jq
        else
            log_warning "无法自动安装jq，请手动安装"
        fi
    else
        log_success "jq已安装"
    fi
    
    # 检查并安装curl
    if ! command -v curl &> /dev/null; then
        log_info "安装curl..."
        if command -v yum &> /dev/null; then
            yum install -y curl
        elif command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y curl
        else
            log_warning "无法自动安装curl，请手动安装"
        fi
    else
        log_success "curl已安装"
    fi
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    local target_dir="$WEB_ROOT/$PROJECT_NAME"
    local service_file="/etc/systemd/system/wp-deploy-queue.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=WordPress Deploy Manager Queue Processor
After=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=$target_dir
ExecStart=/usr/bin/php $target_dir/scripts/queue_processor.php
Restart=always
RestartSec=5
StandardOutput=append:$target_dir/logs/queue_processor.log
StandardError=append:$target_dir/logs/queue_processor_error.log

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd配置
    systemctl daemon-reload
    
    # 启用服务（但不立即启动）
    systemctl enable wp-deploy-queue
    
    log_success "系统服务已创建"
}

# 主安装函数
main() {
    show_welcome
    
    # 获取域名配置
    echo -n "请输入访问域名 (例如: deploy.example.com): "
    read -r domain
    
    if [ -z "$domain" ]; then
        log_error "域名不能为空"
        exit 1
    fi
    
    log_info "开始安装WordPress部署管理系统..."
    log_info "目标域名: $domain"
    
    # 执行安装步骤
    check_environment
    install_dependencies
    create_directory_structure
    set_permissions
    configure_nginx "$domain"
    create_systemd_service
    
    log_success "环境设置完成！"
    echo ""
    echo "下一步操作："
    echo "1. 访问 http://$domain/install/ 完成系统配置"
    echo "2. 启动队列处理服务: systemctl start wp-deploy-queue"
    echo "3. 查看服务状态: systemctl status wp-deploy-queue"
    echo ""
    echo "重要文件位置："
    echo "- 项目目录: $WEB_ROOT/$PROJECT_NAME"
    echo "- 配置文件: $WEB_ROOT/$PROJECT_NAME/config/"
    echo "- 日志目录: $WEB_ROOT/$PROJECT_NAME/logs/"
    echo "- 数据库文件: $WEB_ROOT/$PROJECT_NAME/database/deploy.db"
    echo ""
    log_success "安装完成！"
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
