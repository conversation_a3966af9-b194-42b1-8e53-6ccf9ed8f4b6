<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/images/logo.png" alt="Logo" class="nav-logo">
                <span class="nav-title">WordPress 部署管理系统</span>
            </div>
            
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                    <i class="icon-dashboard"></i> 仪表板
                </a>
                <a href="#deploy" class="nav-link" data-tab="deploy">
                    <i class="icon-deploy"></i> 部署管理
                </a>
                <a href="#templates" class="nav-link" data-tab="templates">
                    <i class="icon-templates"></i> 模板管理
                </a>
                <a href="#monitoring" class="nav-link" data-tab="monitoring">
                    <i class="icon-monitoring"></i> 监控中心
                </a>
                <a href="#logs" class="nav-link" data-tab="logs">
                    <i class="icon-logs"></i> 日志管理
                </a>
                <a href="#settings" class="nav-link" data-tab="settings">
                    <i class="icon-settings"></i> 系统设置
                </a>
            </div>
            
            <div class="nav-actions">
                <button class="btn btn-icon" id="refresh-btn" title="刷新">
                    <i class="icon-refresh"></i>
                </button>
                <button class="btn btn-icon" id="notifications-btn" title="通知">
                    <i class="icon-bell"></i>
                    <span class="notification-badge" id="notification-count">0</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 仪表板 -->
        <section id="dashboard-tab" class="tab-content active">
            <div class="page-header">
                <h1>系统仪表板</h1>
                <p>WordPress部署管理系统概览</p>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon stat-primary">
                        <i class="icon-sites"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-sites">0</h3>
                        <p>总站点数</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon stat-success">
                        <i class="icon-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="successful-deployments">0</h3>
                        <p>成功部署</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon stat-warning">
                        <i class="icon-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="pending-jobs">0</h3>
                        <p>待处理任务</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon stat-info">
                        <i class="icon-template"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="active-templates">0</h3>
                        <p>活跃模板</p>
                    </div>
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>部署趋势</h3>
                        <div class="card-actions">
                            <select id="chart-period">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <canvas id="deployment-chart"></canvas>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>系统状态</h3>
                    </div>
                    <div class="card-content">
                        <div class="system-status">
                            <div class="status-item">
                                <span class="status-label">队列处理器</span>
                                <span class="status-indicator status-running" id="queue-status">运行中</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">并发任务</span>
                                <span class="status-value" id="concurrent-jobs">0/3</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">系统负载</span>
                                <span class="status-value" id="system-load">0.0</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">磁盘使用</span>
                                <span class="status-value" id="disk-usage">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 最近活动 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>最近活动</h3>
                    <button class="btn btn-sm" onclick="refreshRecentActivity()">刷新</button>
                </div>
                <div class="card-content">
                    <div class="activity-list" id="recent-activity">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 部署管理 -->
        <section id="deploy-tab" class="tab-content">
            <div class="page-header">
                <h1>部署管理</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showSingleDeployModal()">
                        <i class="icon-plus"></i> 单域名部署
                    </button>
                    <button class="btn btn-secondary" onclick="showBatchDeployModal()">
                        <i class="icon-batch"></i> 批量部署
                    </button>
                </div>
            </div>
            
            <!-- 部署队列 -->
            <div class="deploy-queue">
                <div class="queue-header">
                    <h3>部署队列</h3>
                    <div class="queue-controls">
                        <button class="btn btn-sm" id="pause-queue-btn" onclick="toggleQueue()">
                            <i class="icon-pause"></i> 暂停队列
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="clearQueue()">
                            <i class="icon-clear"></i> 清空队列
                        </button>
                    </div>
                </div>
                <div class="queue-content" id="deploy-queue-list">
                    <!-- 动态加载 -->
                </div>
            </div>
            
            <!-- 部署历史 -->
            <div class="deploy-history">
                <div class="history-header">
                    <h3>部署历史</h3>
                    <div class="history-filters">
                        <select id="status-filter">
                            <option value="">所有状态</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                            <option value="running">运行中</option>
                            <option value="pending">待处理</option>
                        </select>
                        <input type="text" id="domain-filter" placeholder="搜索域名...">
                        <button class="btn btn-sm" onclick="filterDeployHistory()">筛选</button>
                    </div>
                </div>
                <div class="history-content">
                    <table class="data-table" id="deploy-history-table">
                        <thead>
                            <tr>
                                <th>域名</th>
                                <th>模板</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>开始时间</th>
                                <th>耗时</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                    
                    <!-- 分页 -->
                    <div class="pagination" id="deploy-pagination">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 模板管理 -->
        <section id="templates-tab" class="tab-content">
            <div class="page-header">
                <h1>模板管理</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showUploadTemplateModal()">
                        <i class="icon-upload"></i> 上传模板
                    </button>
                    <button class="btn btn-secondary" onclick="testAllTemplates()">
                        <i class="icon-test"></i> 批量测试
                    </button>
                </div>
            </div>
            
            <!-- 模板列表 -->
            <div class="templates-grid" id="templates-grid">
                <!-- 动态加载 -->
            </div>
        </section>

        <!-- 监控中心 -->
        <section id="monitoring-tab" class="tab-content">
            <div class="page-header">
                <h1>监控中心</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="runHealthCheck()">
                        <i class="icon-health"></i> 健康检查
                    </button>
                    <button class="btn btn-secondary" onclick="exportMonitoringReport()">
                        <i class="icon-export"></i> 导出报告
                    </button>
                </div>
            </div>
            
            <!-- 监控内容 -->
            <div class="monitoring-content">
                <!-- 系统资源监控 -->
                <div class="monitoring-card">
                    <h3>系统资源</h3>
                    <div class="resource-metrics" id="system-resources">
                        <!-- 动态加载 -->
                    </div>
                </div>
                
                <!-- 站点健康状态 -->
                <div class="monitoring-card">
                    <h3>站点健康状态</h3>
                    <div class="health-overview" id="sites-health">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 日志管理 -->
        <section id="logs-tab" class="tab-content">
            <div class="page-header">
                <h1>日志管理</h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="exportLogs()">
                        <i class="icon-export"></i> 导出日志
                    </button>
                    <button class="btn btn-danger" onclick="clearLogs()">
                        <i class="icon-clear"></i> 清理日志
                    </button>
                </div>
            </div>
            
            <!-- 日志过滤器 -->
            <div class="logs-filters">
                <select id="log-level-filter">
                    <option value="">所有级别</option>
                    <option value="ERROR">错误</option>
                    <option value="WARNING">警告</option>
                    <option value="INFO">信息</option>
                    <option value="DEBUG">调试</option>
                </select>
                <input type="date" id="log-start-date">
                <input type="date" id="log-end-date">
                <input type="text" id="log-search" placeholder="搜索日志内容...">
                <button class="btn btn-sm" onclick="filterLogs()">筛选</button>
            </div>
            
            <!-- 日志内容 -->
            <div class="logs-content">
                <div class="log-viewer" id="log-viewer">
                    <!-- 动态加载 -->
                </div>
            </div>
        </section>

        <!-- 系统设置 -->
        <section id="settings-tab" class="tab-content">
            <div class="page-header">
                <h1>系统设置</h1>
            </div>
            
            <!-- 设置表单 -->
            <div class="settings-content">
                <form id="settings-form" class="settings-form">
                    <!-- 基础设置 -->
                    <div class="settings-section">
                        <h3>基础设置</h3>
                        <div class="form-group">
                            <label for="max-concurrent-jobs">最大并发任务数</label>
                            <input type="number" id="max-concurrent-jobs" min="1" max="10" value="3">
                        </div>
                        <div class="form-group">
                            <label for="default-timeout">默认超时时间（秒）</label>
                            <input type="number" id="default-timeout" min="300" max="7200" value="1800">
                        </div>
                    </div>
                    
                    <!-- 通知设置 -->
                    <div class="settings-section">
                        <h3>通知设置</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable-notifications"> 启用通知
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="notification-email">通知邮箱</label>
                            <input type="email" id="notification-email">
                        </div>
                    </div>
                    
                    <!-- 安全设置 -->
                    <div class="settings-section">
                        <h3>安全设置</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="backup-before-deploy" checked> 部署前自动备份
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="auto-ssl-setup" checked> 自动SSL设置
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">保存设置</button>
                        <button type="button" class="btn btn-secondary" onclick="resetSettings()">重置</button>
                    </div>
                </form>
            </div>
        </section>
    </main>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>
    
    <!-- 通知容器 -->
    <div id="notification-container"></div>
    
    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/components.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/deploy.js"></script>
    <script src="assets/js/templates.js"></script>
    <script src="assets/js/monitoring.js"></script>
    <script src="assets/js/logs.js"></script>
    <script src="assets/js/settings.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
