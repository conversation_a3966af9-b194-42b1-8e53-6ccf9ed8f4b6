#!/bin/bash

# 创建API文件脚本

echo "🔌 创建API文件..."

# 创建API路由文件
cat > public/api/index.php << 'EOF'
<?php
/**
 * API路由入口文件
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 加载配置
require_once dirname(__DIR__, 2) . '/config/config.php';

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$endpoint = str_replace('/api/', '', $path);

// 路由映射
$routes = [
    'deploy.php' => '../../api/deploy.php',
    'templates.php' => '../../api/templates.php',
    'status.php' => '../../api/status.php',
    'logs.php' => '../../api/logs.php',
    'settings.php' => '../../api/settings.php'
];

// 检查路由
if (isset($routes[$endpoint])) {
    $targetFile = __DIR__ . '/' . $routes[$endpoint];
    
    if (file_exists($targetFile)) {
        // 设置环境变量
        $_SERVER['ROUTED_REQUEST'] = true;
        
        // 包含目标文件
        require $targetFile;
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint file not found: ' . $endpoint
        ]);
    }
} else {
    // 默认API信息
    if (empty($endpoint) || $endpoint === 'index.php') {
        echo json_encode([
            'success' => true,
            'message' => 'WordPress Deploy Manager API',
            'version' => '1.0.0',
            'endpoints' => [
                'deploy' => '/api/deploy.php',
                'templates' => '/api/templates.php',
                'status' => '/api/status.php',
                'logs' => '/api/logs.php',
                'settings' => '/api/settings.php'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint not found: ' . $endpoint,
            'available_endpoints' => array_keys($routes)
        ]);
    }
}
?>
EOF

echo "✅ API路由文件创建完成"

# 创建模板API
cat > api/templates.php << 'EOF'
<?php
/**
 * 模板管理API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list':
            handleGetTemplates();
            break;
            
        case 'upload':
            handleUploadTemplate();
            break;
            
        case 'delete':
            handleDeleteTemplate();
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Templates API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 获取模板列表
 */
function handleGetTemplates() {
    try {
        $db = getDatabase();
        
        $stmt = $db->prepare("
            SELECT id, uuid, name, description, filename, file_size, status, created_at 
            FROM templates 
            WHERE status != 'deleted' 
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        
        $templates = $stmt->fetchAll();
        
        successResponse($templates, 'Templates retrieved successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to retrieve templates: ' . $e->getMessage());
    }
}

/**
 * 上传模板
 */
function handleUploadTemplate() {
    try {
        // 检查文件上传
        if (!isset($_FILES['template']) || $_FILES['template']['error'] !== UPLOAD_ERR_OK) {
            errorResponse('No file uploaded or upload error');
        }
        
        $file = $_FILES['template'];
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        
        if (empty($name)) {
            errorResponse('Template name is required');
        }
        
        // 验证文件类型
        $allowedTypes = ['application/gzip', 'application/x-gzip', 'application/zip'];
        if (!in_array($file['type'], $allowedTypes)) {
            errorResponse('Invalid file type. Only .tar.gz and .zip files are allowed');
        }
        
        // 生成文件路径
        $uuid = uniqid('template_');
        $filename = $uuid . '_' . $file['name'];
        $uploadPath = UPLOADS_PATH . '/templates/' . $filename;
        
        // 确保上传目录存在
        if (!is_dir(dirname($uploadPath))) {
            mkdir(dirname($uploadPath), 0777, true);
        }
        
        // 移动文件
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            errorResponse('Failed to save uploaded file');
        }
        
        // 保存到数据库
        $db = getDatabase();
        $stmt = $db->prepare("
            INSERT INTO templates (uuid, name, description, filename, file_path, file_size, file_hash) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $uuid,
            $name,
            $description,
            $filename,
            $uploadPath,
            $file['size'],
            md5_file($uploadPath)
        ]);
        
        successResponse([
            'id' => $db->lastInsertId(),
            'uuid' => $uuid,
            'name' => $name
        ], 'Template uploaded successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to upload template: ' . $e->getMessage());
    }
}

/**
 * 删除模板
 */
function handleDeleteTemplate() {
    try {
        $id = intval($_GET['id'] ?? 0);
        
        if ($id <= 0) {
            errorResponse('Invalid template ID');
        }
        
        $db = getDatabase();
        
        // 获取模板信息
        $stmt = $db->prepare("SELECT file_path FROM templates WHERE id = ?");
        $stmt->execute([$id]);
        $template = $stmt->fetch();
        
        if (!$template) {
            errorResponse('Template not found');
        }
        
        // 标记为删除
        $stmt = $db->prepare("UPDATE templates SET status = 'deleted' WHERE id = ?");
        $stmt->execute([$id]);
        
        // 删除文件
        if (file_exists($template['file_path'])) {
            unlink($template['file_path']);
        }
        
        successResponse(null, 'Template deleted successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to delete template: ' . $e->getMessage());
    }
}
?>
EOF

echo "✅ 模板API创建完成"

# 创建部署API
cat > api/deploy.php << 'EOF'
<?php
/**
 * 部署管理API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'single_deploy':
            handleSingleDeploy();
            break;
            
        case 'batch_deploy':
            handleBatchDeploy();
            break;
            
        case 'get_deploy_history':
            handleGetDeployHistory();
            break;
            
        case 'get_deploy_stats':
            handleGetDeployStats();
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Deploy API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 单域名部署
 */
function handleSingleDeploy() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $domain = $input['domain'] ?? '';
        $templateId = intval($input['template_id'] ?? 0);
        
        if (empty($domain) || $templateId <= 0) {
            errorResponse('Domain and template ID are required');
        }
        
        // 创建部署任务
        $db = getDatabase();
        $uuid = uniqid('deploy_');
        
        $stmt = $db->prepare("
            INSERT INTO deploy_jobs (uuid, type, template_id, domains, status) 
            VALUES (?, 'single', ?, ?, 'pending')
        ");
        
        $stmt->execute([$uuid, $templateId, json_encode([$domain])]);
        
        successResponse([
            'job_id' => $db->lastInsertId(),
            'job_uuid' => $uuid,
            'status' => 'pending'
        ], 'Deploy job created successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to create deploy job: ' . $e->getMessage());
    }
}

/**
 * 批量部署
 */
function handleBatchDeploy() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $domains = $input['domains'] ?? [];
        $templateId = intval($input['template_id'] ?? 0);
        
        if (empty($domains) || $templateId <= 0) {
            errorResponse('Domains and template ID are required');
        }
        
        // 创建批量部署任务
        $db = getDatabase();
        $uuid = uniqid('batch_');
        
        $stmt = $db->prepare("
            INSERT INTO deploy_jobs (uuid, type, template_id, domains, status) 
            VALUES (?, 'batch', ?, ?, 'pending')
        ");
        
        $stmt->execute([$uuid, $templateId, json_encode($domains)]);
        
        successResponse([
            'job_id' => $db->lastInsertId(),
            'job_uuid' => $uuid,
            'status' => 'pending',
            'domain_count' => count($domains)
        ], 'Batch deploy job created successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to create batch deploy job: ' . $e->getMessage());
    }
}

/**
 * 获取部署历史
 */
function handleGetDeployHistory() {
    try {
        $limit = intval($_GET['limit'] ?? 20);
        $page = intval($_GET['page'] ?? 1);
        $offset = ($page - 1) * $limit;
        
        $db = getDatabase();
        
        $stmt = $db->prepare("
            SELECT dj.*, t.name as template_name 
            FROM deploy_jobs dj 
            LEFT JOIN templates t ON dj.template_id = t.id 
            ORDER BY dj.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        
        $jobs = $stmt->fetchAll();
        
        // 解析domains JSON
        foreach ($jobs as &$job) {
            $job['domains'] = json_decode($job['domains'], true);
        }
        
        successResponse($jobs, 'Deploy history retrieved successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to retrieve deploy history: ' . $e->getMessage());
    }
}

/**
 * 获取部署统计
 */
function handleGetDeployStats() {
    try {
        $days = intval($_GET['days'] ?? 30);
        
        $db = getDatabase();
        
        // 获取统计数据
        $stats = [];
        
        // 总站点数（模拟数据）
        $stmt = $db->query("SELECT COUNT(*) FROM templates WHERE status = 'active'");
        $stats['total_sites'] = $stmt->fetchColumn();
        
        // 成功部署数
        $stmt = $db->prepare("
            SELECT COUNT(*) FROM deploy_jobs 
            WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $stats['successful_deploys'] = $stmt->fetchColumn();
        
        // 待处理任务
        $stmt = $db->query("SELECT COUNT(*) FROM deploy_jobs WHERE status IN ('pending', 'running')");
        $stats['pending_tasks'] = $stmt->fetchColumn();
        
        // 活跃模板数
        $stmt = $db->query("SELECT COUNT(*) FROM templates WHERE status = 'active'");
        $stats['active_templates'] = $stmt->fetchColumn();
        
        successResponse($stats, 'Deploy stats retrieved successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to retrieve deploy stats: ' . $e->getMessage());
    }
}
?>
EOF

echo "✅ 部署API创建完成"

# 创建状态API
cat > api/status.php << 'EOF'
<?php
/**
 * 系统状态API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'system':
            handleSystemStatus();
            break;

        case 'queue':
            handleQueueStatus();
            break;

        default:
            errorResponse('Invalid action', 400);
    }

} catch (Exception $e) {
    logMessage('ERROR', 'Status API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 获取系统状态
 */
function handleSystemStatus() {
    try {
        $status = [
            'server_time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'memory_limit' => ini_get('memory_limit'),
            'cpu_usage' => '15%', // 模拟数据
            'disk_usage' => '45%', // 模拟数据
            'database_status' => 'connected'
        ];

        // 检查数据库连接
        try {
            $db = getDatabase();
            $db->query('SELECT 1');
            $status['database_status'] = 'connected';
        } catch (Exception $e) {
            $status['database_status'] = 'disconnected';
        }

        successResponse($status, 'System status retrieved successfully');

    } catch (Exception $e) {
        errorResponse('Failed to retrieve system status: ' . $e->getMessage());
    }
}

/**
 * 获取队列状态
 */
function handleQueueStatus() {
    try {
        $db = getDatabase();

        $stmt = $db->query("
            SELECT status, COUNT(*) as count
            FROM deploy_jobs
            GROUP BY status
        ");

        $queueStatus = [];
        while ($row = $stmt->fetch()) {
            $queueStatus[$row['status']] = $row['count'];
        }

        successResponse($queueStatus, 'Queue status retrieved successfully');

    } catch (Exception $e) {
        errorResponse('Failed to retrieve queue status: ' . $e->getMessage());
    }
}
?>
EOF

# 创建日志API
cat > api/logs.php << 'EOF'
<?php
/**
 * 日志管理API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'get_logs':
            handleGetLogs();
            break;

        default:
            errorResponse('Invalid action', 400);
    }

} catch (Exception $e) {
    logMessage('ERROR', 'Logs API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 获取日志
 */
function handleGetLogs() {
    try {
        $limit = intval($_GET['limit'] ?? 50);
        $level = $_GET['level'] ?? '';

        $logs = [];

        // 读取日志文件
        $logFiles = glob(LOGS_PATH . '/system_*.log');
        rsort($logFiles); // 最新的文件在前

        $count = 0;
        foreach ($logFiles as $logFile) {
            if ($count >= $limit) break;

            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $lines = array_reverse($lines); // 最新的日志在前

            foreach ($lines as $line) {
                if ($count >= $limit) break;

                // 解析日志行
                if (preg_match('/\[(.*?)\] \[(.*?)\] (.*)/', $line, $matches)) {
                    $logEntry = [
                        'created_at' => $matches[1],
                        'level' => $matches[2],
                        'message' => $matches[3]
                    ];

                    // 过滤日志级别
                    if (empty($level) || $logEntry['level'] === $level) {
                        $logs[] = $logEntry;
                        $count++;
                    }
                }
            }
        }

        successResponse($logs, 'Logs retrieved successfully');

    } catch (Exception $e) {
        errorResponse('Failed to retrieve logs: ' . $e->getMessage());
    }
}
?>
EOF

# 创建设置API
cat > api/settings.php << 'EOF'
<?php
/**
 * 系统设置API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'get':
            handleGetSettings();
            break;

        case 'save':
            handleSaveSettings();
            break;

        default:
            errorResponse('Invalid action', 400);
    }

} catch (Exception $e) {
    logMessage('ERROR', 'Settings API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 获取系统设置
 */
function handleGetSettings() {
    try {
        $db = getDatabase();

        $stmt = $db->query("SELECT setting_key, setting_value FROM system_settings");
        $settings = [];

        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        successResponse($settings, 'Settings retrieved successfully');

    } catch (Exception $e) {
        errorResponse('Failed to retrieve settings: ' . $e->getMessage());
    }
}

/**
 * 保存系统设置
 */
function handleSaveSettings() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (empty($input)) {
            errorResponse('No settings data provided');
        }

        $db = getDatabase();

        foreach ($input as $key => $value) {
            $stmt = $db->prepare("
                INSERT INTO system_settings (setting_key, setting_value)
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
            ");
            $stmt->execute([$key, $value]);
        }

        successResponse(null, 'Settings saved successfully');

    } catch (Exception $e) {
        errorResponse('Failed to save settings: ' . $e->getMessage());
    }
}
?>
EOF

echo "🔌 API文件创建完成！"
