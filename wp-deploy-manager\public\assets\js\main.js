/**
 * WordPress Deploy Manager - 主应用脚本
 */

class WPDeployManager {
    constructor() {
        this.currentTab = 'dashboard';
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        console.log('WordPress Deploy Manager initialized');
        
        // 绑定事件
        this.bindEvents();
        
        // 加载初始数据
        this.loadInitialData();
        
        // 设置定时刷新
        this.setupAutoRefresh();
        
        // 测试API连接
        this.testAPIConnection();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentTab();
            });
        }

        // 设置保存按钮
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // 设置重置按钮
        const resetSettingsBtn = document.getElementById('reset-settings-btn');
        if (resetSettingsBtn) {
            resetSettingsBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // 部署按钮
        const singleDeployBtn = document.getElementById('single-deploy-btn');
        if (singleDeployBtn) {
            singleDeployBtn.addEventListener('click', () => {
                this.showSingleDeployDialog();
            });
        }

        const batchDeployBtn = document.getElementById('batch-deploy-btn');
        if (batchDeployBtn) {
            batchDeployBtn.addEventListener('click', () => {
                this.showBatchDeployDialog();
            });
        }

        // 模板上传按钮
        const uploadTemplateBtn = document.getElementById('upload-template-btn');
        if (uploadTemplateBtn) {
            uploadTemplateBtn.addEventListener('click', () => {
                this.showUploadTemplateDialog();
            });
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // 加载对应数据
        this.loadTabData(tabName);
    }

    /**
     * 加载标签页数据
     */
    async loadTabData(tabName) {
        try {
            switch (tabName) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'deploy':
                    await this.loadDeployData();
                    break;
                case 'templates':
                    await this.loadTemplatesData();
                    break;
                case 'monitoring':
                    await this.loadMonitoringData();
                    break;
                case 'logs':
                    await this.loadLogsData();
                    break;
                case 'settings':
                    await this.loadSettingsData();
                    break;
            }
        } catch (error) {
            console.error(`Failed to load ${tabName} data:`, error);
            this.showNotification(`加载${tabName}数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 加载仪表板数据
     */
    async loadDashboardData() {
        try {
            // 模拟加载统计数据
            this.updateDashboardStats({
                total_sites: 5,
                successful_deploys: 12,
                pending_tasks: 2,
                active_templates: 2
            });

            // 模拟系统状态
            this.updateSystemStatus({
                service_status: 'running',
                database_status: 'connected',
                api_status: 'normal'
            });

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            // 显示默认数据
            this.updateDashboardStats({});
            this.updateSystemStatus({});
        }
    }

    /**
     * 更新仪表板统计
     */
    updateDashboardStats(stats) {
        document.getElementById('total-sites').textContent = stats.total_sites || 0;
        document.getElementById('successful-deploys').textContent = stats.successful_deploys || 0;
        document.getElementById('pending-tasks').textContent = stats.pending_tasks || 0;
        document.getElementById('active-templates').textContent = stats.active_templates || 0;
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(status) {
        console.log('System status updated:', status);
        // 这里可以根据实际状态更新UI
    }

    /**
     * 加载部署数据
     */
    async loadDeployData() {
        try {
            // 模拟部署历史数据
            const history = [
                {
                    domain: 'example.com',
                    status: 'completed',
                    created_at: '2024-07-26 10:30:00'
                },
                {
                    domain: 'test.com',
                    status: 'pending',
                    created_at: '2024-07-26 10:25:00'
                }
            ];
            this.updateDeployHistory(history);
        } catch (error) {
            console.error('Failed to load deploy data:', error);
        }
    }

    /**
     * 更新部署历史
     */
    updateDeployHistory(history) {
        const container = document.getElementById('deploy-history-list');
        if (history.length === 0) {
            container.innerHTML = '<p>暂无部署记录</p>';
            return;
        }

        const html = history.map(item => `
            <div class="deploy-item">
                <div class="deploy-info">
                    <strong>${item.domain}</strong>
                    <span class="deploy-status status-${item.status}">${this.getStatusText(item.status)}</span>
                </div>
                <div class="deploy-time">${item.created_at}</div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'running': '运行中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    /**
     * 加载模板数据
     */
    async loadTemplatesData() {
        try {
            // 模拟模板数据
            const templates = [
                {
                    id: 1,
                    name: '测试模板1',
                    description: '这是一个测试模板',
                    file_size: 1024000
                },
                {
                    id: 2,
                    name: '测试模板2',
                    description: '这是另一个测试模板',
                    file_size: 2048000
                }
            ];
            this.updateTemplatesList(templates);
        } catch (error) {
            console.error('Failed to load templates:', error);
        }
    }

    /**
     * 更新模板列表
     */
    updateTemplatesList(templates) {
        const container = document.getElementById('templates-container');
        if (templates.length === 0) {
            container.innerHTML = '<p>暂无模板</p>';
            return;
        }

        const html = templates.map(template => `
            <div class="template-item">
                <div class="template-info">
                    <h4>${template.name}</h4>
                    <p>${template.description || '无描述'}</p>
                    <small>文件大小: ${this.formatFileSize(template.file_size)}</small>
                </div>
                <div class="template-actions">
                    <button class="btn btn-secondary" onclick="app.editTemplate('${template.id}')">编辑</button>
                    <button class="btn btn-danger" onclick="app.deleteTemplate('${template.id}')">删除</button>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 加载监控数据
     */
    async loadMonitoringData() {
        try {
            // 模拟监控数据
            this.updateMonitoringData({
                cpu_usage: '15%',
                memory_usage: '45%',
                disk_usage: '60%'
            });
        } catch (error) {
            console.error('Failed to load monitoring data:', error);
        }
    }

    /**
     * 更新监控数据
     */
    updateMonitoringData(data) {
        document.getElementById('cpu-usage').textContent = data.cpu_usage || '0%';
        document.getElementById('memory-usage').textContent = data.memory_usage || '0%';
        if (document.getElementById('disk-usage')) {
            document.getElementById('disk-usage').textContent = data.disk_usage || '0%';
        }
    }

    /**
     * 加载日志数据
     */
    async loadLogsData() {
        try {
            // 模拟日志数据
            const logs = [
                {
                    created_at: '2024-07-26 10:30:15',
                    level: 'INFO',
                    message: '系统启动完成'
                },
                {
                    created_at: '2024-07-26 10:30:10',
                    level: 'INFO',
                    message: '数据库连接成功'
                },
                {
                    created_at: '2024-07-26 10:30:05',
                    level: 'DEBUG',
                    message: '加载配置文件'
                }
            ];
            this.updateLogsDisplay(logs);
        } catch (error) {
            console.error('Failed to load logs:', error);
        }
    }

    /**
     * 更新日志显示
     */
    updateLogsDisplay(logs) {
        const container = document.getElementById('logs-content');
        if (logs.length === 0) {
            container.innerHTML = '<p>暂无日志</p>';
            return;
        }

        const html = logs.map(log => `
            <div class="log-item log-${log.level.toLowerCase()}">
                <span class="log-time">${log.created_at}</span>
                <span class="log-level">[${log.level}]</span>
                <span class="log-message">${log.message}</span>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 加载设置数据
     */
    async loadSettingsData() {
        try {
            // 模拟设置数据
            const settings = {
                max_concurrent_jobs: 3,
                default_timeout: 1800,
                enable_notifications: false
            };
            this.updateSettingsForm(settings);
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    /**
     * 更新设置表单
     */
    updateSettingsForm(settings) {
        const maxJobsInput = document.getElementById('max-concurrent-jobs');
        const timeoutInput = document.getElementById('default-timeout');
        const notificationsSelect = document.getElementById('enable-notifications');

        if (maxJobsInput) maxJobsInput.value = settings.max_concurrent_jobs || 3;
        if (timeoutInput) timeoutInput.value = settings.default_timeout || 1800;
        if (notificationsSelect) notificationsSelect.value = settings.enable_notifications ? 'true' : 'false';
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            const settings = {
                max_concurrent_jobs: document.getElementById('max-concurrent-jobs').value,
                default_timeout: document.getElementById('default-timeout').value,
                enable_notifications: document.getElementById('enable-notifications').value === 'true'
            };

            // 这里应该调用API保存设置
            console.log('Saving settings:', settings);
            this.showNotification('设置保存成功', 'success');
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('设置保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 重置设置
     */
    resetSettings() {
        if (confirm('确定要重置所有设置吗？')) {
            this.updateSettingsForm({
                max_concurrent_jobs: 3,
                default_timeout: 1800,
                enable_notifications: false
            });
            this.showNotification('设置已重置', 'info');
        }
    }

    /**
     * 测试API连接
     */
    async testAPIConnection() {
        try {
            const result = await window.api.testConnection();
            if (result.success) {
                console.log('API连接测试成功');
            } else {
                console.warn('API连接测试失败:', result.message);
            }
        } catch (error) {
            console.warn('API连接测试异常:', error.message);
        }
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        await this.loadDashboardData();
    }

    /**
     * 刷新当前标签页
     */
    refreshCurrentTab() {
        this.loadTabData(this.currentTab);
        this.showNotification('数据已刷新', 'info');
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒刷新一次仪表板数据
        setInterval(() => {
            if (this.currentTab === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 创建简单的通知
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 显示单域名部署对话框
     */
    showSingleDeployDialog() {
        const domain = prompt('请输入域名:');
        if (domain) {
            this.showNotification(`单域名部署功能开发中: ${domain}`, 'info');
        }
    }

    /**
     * 显示批量部署对话框
     */
    showBatchDeployDialog() {
        this.showNotification('批量部署功能开发中', 'info');
    }

    /**
     * 显示模板上传对话框
     */
    showUploadTemplateDialog() {
        this.showNotification('模板上传功能开发中', 'info');
    }

    /**
     * 编辑模板
     */
    editTemplate(templateId) {
        this.showNotification(`编辑模板功能开发中: ${templateId}`, 'info');
    }

    /**
     * 删除模板
     */
    deleteTemplate(templateId) {
        if (confirm('确定要删除这个模板吗？')) {
            this.showNotification(`模板删除功能开发中: ${templateId}`, 'info');
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new WPDeployManager();
});

// 导出应用类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WPDeployManager;
}
