#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer_incremental.py - 增量保存版本
支持中途终止时保存已处理结果，避免数据丢失
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
import signal
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import OrderedDict

# DeepSeek API配置
DEEPSEEK_API_KEY = "***********************************"  # 修复API密钥
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"  # 修复API URL

# 路径配置 - 线上版本
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_incremental/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_incremental/output"

# 并发配置 - 高性能优化
MAX_WORKERS = 8       # 增加到8线程 (33%提升)
BATCH_SIZE = 25       # 增加到25产品/批次 (25%提升)
REQUEST_DELAY = 0.08  # 减少到0.08秒 (20%提升)
TIMEOUT_SECONDS = 90  # 增加超时时间适应更大批次
MAX_RETRIES = 3

# 性能模式配置
PERFORMANCE_MODES = {
    "conservative": {  # 保守模式 (当前性能)
        "workers": 6,
        "batch_size": 20,
        "delay": 0.15,
        "description": "稳定优先，适合长时间运行"
    },
    "balanced": {      # 平衡模式 (1.5倍性能)
        "workers": 8,
        "batch_size": 25,
        "delay": 0.2,
        "description": "平衡性能与稳定性"
    },
    "aggressive": {    # 激进模式 (2倍性能)
        "workers": 10,
        "batch_size": 30,
        "delay": 0.2,
        "description": "最大性能，需要监控稳定性"
    }
}

# 日志系统配置
def setup_logging():
    """设置日志系统"""
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(OUTPUT_DIR), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # 日志文件名包含时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"product_optimizer_{timestamp}.log")

    # 配置日志格式
    log_format = '%(asctime)s | %(levelname)-8s | %(threadName)-10s | %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'

    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 创建专用日志器
    logger = logging.getLogger('ProductOptimizer')
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")

    return logger

# 初始化日志系统
logger = setup_logging()

# 全局变量
current_output_file = None
processed_results = []
results_lock = threading.Lock()
graceful_shutdown = False
processed_files = set()  # 已处理文件集合
file_monitor_active = False  # 文件监控状态

# 顺序保持器类
class OrderPreserver:
    """保证输出顺序的类"""

    def __init__(self):
        self.completed_batches = OrderedDict()
        self.next_expected_batch = 1
        self.lock = threading.Lock()
        self.written_batches = set()

    def add_completed_batch(self, batch_num, results):
        """添加完成的批次，返回可以按顺序写入的批次"""
        with self.lock:
            self.completed_batches[batch_num] = results
            return self._get_ready_batches()

    def _get_ready_batches(self):
        """获取可以按顺序写入的批次"""
        ready_to_write = []

        # 找出可以连续写入的批次
        current_batch = self.next_expected_batch
        while (current_batch in self.completed_batches and
               current_batch not in self.written_batches):
            ready_to_write.append((current_batch, self.completed_batches[current_batch]))
            self.written_batches.add(current_batch)
            current_batch += 1

        if ready_to_write:
            self.next_expected_batch = current_batch
            # 清理已写入的批次以节省内存
            for batch_num, _ in ready_to_write:
                if batch_num in self.completed_batches:
                    del self.completed_batches[batch_num]

        return ready_to_write

    def get_status(self):
        """获取当前状态"""
        with self.lock:
            return {
                "next_expected": self.next_expected_batch,
                "pending_batches": list(self.completed_batches.keys()),
                "written_count": len(self.written_batches)
            }

order_preserver = None  # 顺序保持器实例

def signal_handler(signum, frame):
    """信号处理器 - 优雅关闭"""
    global graceful_shutdown
    logger.warning(f"接收到终止信号 ({signum})，开始优雅关闭流程")
    print(f"\n🛑 接收到终止信号 ({signum})，正在保存已处理结果...")
    graceful_shutdown = True
    save_partial_results()
    logger.info("优雅关闭完成，程序退出")
    print("✅ 已处理结果已保存，程序退出")
    sys.exit(0)

def save_partial_results():
    """保存部分结果"""
    global current_output_file, processed_results

    if not current_output_file or not processed_results:
        logger.debug("无需保存部分结果：无输出文件或无处理结果")
        return

    try:
        with results_lock:
            # 创建临时文件名
            base_name = os.path.splitext(current_output_file)[0]
            partial_file = f"{base_name}_partial.txt"

            logger.info(f"开始保存部分结果到: {partial_file}")

            with open(partial_file, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")
                f.write(f"# 部分处理结果 - 保存时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 已处理产品数量: {len(processed_results)}\n")
                f.write("="*120 + "\n")

                for result in processed_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

            logger.info(f"部分结果保存成功: {len(processed_results)} 个产品 → {partial_file}")
            print(f"💾 部分结果已保存到: {partial_file}")
            print(f"📊 已保存 {len(processed_results)} 个产品的处理结果")

    except Exception as e:
        logger.error(f"保存部分结果失败: {e}", exc_info=True)
        print(f"❌ 保存部分结果失败: {e}")

def add_processed_result(batch_results):
    """添加已处理结果到全局列表"""
    global processed_results

    with results_lock:
        batch_count = len(batch_results)
        for result in batch_results:
            processed_results.append(result)

        logger.debug(f"添加批次结果: {batch_count} 个产品，总计: {len(processed_results)} 个产品")

        # 每处理200个产品保存一次
        if len(processed_results) % 200 == 0:
            save_incremental_backup()

def save_incremental_backup():
    """增量备份"""
    global current_output_file, processed_results

    if not current_output_file:
        logger.debug("跳过增量备份：无当前输出文件")
        return

    try:
        base_name = os.path.splitext(current_output_file)[0]
        backup_file = f"{base_name}_backup_{int(time.time())}.txt"

        logger.info(f"开始增量备份: {len(processed_results)} 个产品")

        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write("SEO Name | Category | Tags\n")
            f.write("="*120 + "\n")
            f.write(f"# 增量备份 - 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 已处理产品数量: {len(processed_results)}\n")
            f.write("="*120 + "\n")

            for result in processed_results:
                seo_name = result.get("seo_name", "").replace("|", "-")
                category = result.get("category", "")
                tags = result.get("tags", "")
                f.write(f"{seo_name} | {category} | {tags}\n")

        logger.info(f"增量备份完成: {os.path.basename(backup_file)}")
        print(f"💾 增量备份: {len(processed_results)} 个产品 → {os.path.basename(backup_file)}")

    except Exception as e:
        logger.error(f"增量备份失败: {e}", exc_info=True)
        print(f"⚠️ 增量备份失败: {e}")

def ai_optimize_products(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """AI产品优化 - 带结果保存"""
    global graceful_shutdown, order_preserver

    if graceful_shutdown:
        logger.debug("检测到优雅关闭信号，跳过批次处理")
        return None, []

    batch_num, products_batch = batch_data
    logger.info(f"开始处理批次 {batch_num}: {len(products_batch)} 个产品")

    # 构建提示词
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: 自动检测
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""

    for attempt in range(max_retries):
        if graceful_shutdown:
            logger.debug(f"批次 {batch_num} 检测到优雅关闭信号")
            return None, []

        try:
            # 随机延迟避免并发冲突（与原版一致）
            delay = random.uniform(0, REQUEST_DELAY * 2)
            logger.debug(f"批次 {batch_num} 尝试 {attempt + 1}: 延迟 {delay:.3f}s")
            time.sleep(delay)

            data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.8
            }

            logger.debug(f"批次 {batch_num} 发送API请求")
            response = requests.post(
                DEEPSEEK_API_URL,
                json=data,
                headers={
                    'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
                    'Content-Type': 'application/json'
                },
                timeout=TIMEOUT_SECONDS
            )

            response.raise_for_status()
            result = response.json()
            logger.debug(f"批次 {batch_num} API响应成功")

            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()

                try:
                    # 清理JSON内容
                    clean_content = content.strip()

                    if "```json" in clean_content:
                        start_idx = clean_content.find("```json") + 7
                        end_idx = clean_content.find("```", start_idx)
                        if end_idx > start_idx:
                            clean_content = clean_content[start_idx:end_idx].strip()

                    parsed_result = json.loads(clean_content)

                    if isinstance(parsed_result, dict) and "results" in parsed_result:
                        results_array = parsed_result["results"]
                    elif isinstance(parsed_result, list):
                        results_array = parsed_result
                    else:
                        results_array = [parsed_result] if isinstance(parsed_result, dict) else []

                    # 清理结果
                    cleaned_results = []
                    for result in results_array:
                        seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                        category = result.get("category", "")
                        tags = result.get("tags", "")

                        # 优化关键词（使用专门的函数）
                        tags = optimize_tags(tags)

                        cleaned_result = {
                            "seo_name": seo_name,
                            "category": category,
                            "tags": tags
                        }
                        cleaned_results.append(cleaned_result)

                    # 使用顺序保持器处理结果
                    if order_preserver:
                        ready_batches = order_preserver.add_completed_batch(batch_num, cleaned_results)
                        # 按顺序写入准备好的批次
                        for ready_batch_num, ready_results in ready_batches:
                            add_processed_result(ready_results)
                            logger.debug(f"按顺序写入批次 {ready_batch_num}: {len(ready_results)} 个产品")
                            print(f"📝 按顺序写入批次 {ready_batch_num}: {len(ready_results)} 个产品")
                    else:
                        # 如果没有顺序保持器，直接写入（兼容模式）
                        add_processed_result(cleaned_results)

                    logger.info(f"批次 {batch_num} 处理成功: {len(cleaned_results)} 个产品")
                    print(f"✅ 批次 {batch_num} 完成 | 已处理: {len(processed_results)} 个产品")

                    return batch_num, cleaned_results

                except json.JSONDecodeError as e:
                    logger.warning(f"批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        logger.info(f"批次 {batch_num} 使用备用方案")
                        backup_results = generate_basic_results(products_batch, fail_mode="basic")
                        # 使用顺序保持器处理备用结果
                        if order_preserver:
                            ready_batches = order_preserver.add_completed_batch(batch_num, backup_results)
                            for ready_batch_num, ready_results in ready_batches:
                                add_processed_result(ready_results)
                                logger.debug(f"按顺序写入备用批次 {ready_batch_num}: {len(ready_results)} 个产品")
                        else:
                            add_processed_result(backup_results)
                        return batch_num, backup_results

        except requests.exceptions.RequestException as e:
            logger.warning(f"批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                # 智能延迟：超时错误延迟更长（与原版一致）
                delay = 3 ** attempt if "timeout" in str(e).lower() else 2 ** attempt
                logger.debug(f"批次 {batch_num} 重试延迟: {delay}s")
                time.sleep(delay)
        except Exception as e:
            logger.error(f"批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}", exc_info=True)
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)

    logger.warning(f"批次 {batch_num} 所有重试失败，使用备用方案")
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    backup_results = generate_basic_results(products_batch, fail_mode="mark")  # 标记失败

    # 使用顺序保持器处理失败结果
    if order_preserver:
        ready_batches = order_preserver.add_completed_batch(batch_num, backup_results)
        for ready_batch_num, ready_results in ready_batches:
            add_processed_result(ready_results)
            logger.debug(f"按顺序写入失败批次 {ready_batch_num}: {len(ready_results)} 个产品")
    else:
        add_processed_result(backup_results)

    return batch_num, backup_results

def optimize_tags(tags):
    """优化关键词质量（与原版一致）"""
    if not tags:
        return ""

    # 分割并清理关键词
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

    # 限制数量和长度
    optimized_tags = []
    for tag in tag_list[:2]:  # 最多2个
        # 限制单个关键词长度
        if len(tag) <= 25:
            optimized_tags.append(tag)

    result = ", ".join(optimized_tags)

    # 总长度控制
    if len(result) > 30:
        result = optimized_tags[0] if optimized_tags else ""

    return result

def generate_basic_results(products_batch, fail_mode="basic"):
    """生成基础结果（与原版一致的备用方案）"""
    results = []

    for product in products_batch:
        # 简单的产品分析
        words = product.split()
        brand = words[0] if words else "Unknown"

        if fail_mode == "mark":
            # 标记为处理失败的产品
            seo_name = f"[FAILED] {product.replace('|', '-')}"
            category = "FAILED > AI Processing Failed"
            tags = f"[FAILED] {brand}"
        elif fail_mode == "basic":
            # 改进的智能备用处理
            product_lower = product.lower()

            # 改进的分类映射
            category_mapping = {
                "heater": "Home & Garden > Heating & Cooling > Space Heaters",
                "fan": "Home & Garden > Heating & Cooling > Fans",
                "damper": "Home Improvement > HVAC > Ductwork",
                "grille": "Home Improvement > HVAC > Vents & Grilles",
                "filter": "Home & Garden > Air Quality > Filters",
                "adapter": "Tools & Hardware > Plumbing > Fittings",
                "humidifier": "Home & Garden > Air Quality > Humidifiers",
                "starter": "Home & Garden > Outdoor Living > Fire Starters",
                "tape": "Tools & Hardware > Adhesives & Tapes",
                "register": "Home Improvement > HVAC > Vents & Grilles"
            }

            # 智能分类
            category = "General > Products"  # 默认分类
            for keyword, cat in category_mapping.items():
                if keyword in product_lower:
                    category = cat
                    break

            # 生成更好的SEO标题
            seo_name = product.replace("|", "-").replace(",", " -").replace("  ", " ").strip()

            # 生成更好的标签
            tags = []
            if "heater" in product_lower:
                tags = ["space heater", "heating"]
            elif "fan" in product_lower:
                tags = ["cooling fan", "ventilation"]
            elif "filter" in product_lower:
                tags = ["air filter", "replacement"]
            elif "adapter" in product_lower:
                tags = ["connector", "fitting"]
            elif "humidifier" in product_lower:
                tags = ["humidifier", "air quality"]
            else:
                tags = [brand.lower(), "hardware"]

            tags = ", ".join(tags[:2])
        elif fail_mode == "empty":
            # 留空处理
            seo_name = product.replace("|", "-")
            category = ""
            tags = ""
        else:
            # 默认标记模式
            seo_name = f"[FAILED] {product.replace('|', '-')}"
            category = "FAILED > AI Processing Failed"
            tags = f"[FAILED] {brand}"

        results.append({
            "seo_name": seo_name,
            "category": category,
            "tags": tags
        })

    return results

def monitor_performance():
    """性能监控"""
    global processed_results

    logger.info("启动性能监控线程")
    start_time = time.time()
    last_count = 0

    while not graceful_shutdown:
        time.sleep(60)  # 每分钟检查一次

        current_count = len(processed_results)
        elapsed = time.time() - start_time

        if elapsed > 0:
            # 计算速度
            total_speed = current_count / (elapsed / 60)  # 产品/分钟
            recent_speed = (current_count - last_count)   # 最近1分钟速度

            logger.info(f"性能监控: 总速度 {total_speed:.1f} 产品/分钟, 最近 {recent_speed} 产品/分钟")
            print(f"📊 性能监控: 总速度 {total_speed:.1f} 产品/分钟, 最近 {recent_speed} 产品/分钟")

            # 性能警告
            if recent_speed < 30:  # 低于30产品/分钟
                logger.warning("性能警告: 处理速度较慢，可能遇到API限制")
                print(f"⚠️ 性能警告: 处理速度较慢，可能遇到API限制")
            elif recent_speed > 150:  # 高于150产品/分钟
                logger.info("性能优秀: 处理速度很快")
                print(f"🚀 性能优秀: 处理速度很快")

        last_count = current_count

    logger.info("性能监控线程结束")

def get_supported_files():
    """获取支持的文件列表"""
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    # 查找无扩展名文件（但排除系统文件）
    try:
        excluded_names = {'home', 'root', 'tmp', 'var', 'usr', 'etc', 'bin', 'lib', 'opt', 'sys', 'proc'}
        for item in os.listdir(INPUT_DIR):
            item_path = os.path.join(INPUT_DIR, item)
            if (os.path.isfile(item_path) and
                not item.startswith('.') and
                '.' not in item and
                item.lower() not in excluded_names and
                len(item) > 2):  # 文件名至少3个字符
                all_files.append(item_path)
    except FileNotFoundError:
        pass

    return sorted(list(set(all_files)))

def is_file_stable(file_path, wait_time=3):
    """检查文件是否写入完成"""
    try:
        size1 = os.path.getsize(file_path)
        time.sleep(wait_time)
        size2 = os.path.getsize(file_path)
        return size1 == size2
    except:
        return False

def file_monitor_thread():
    """文件监控线程"""
    global file_monitor_active, processed_files, graceful_shutdown

    logger.info("启动文件监控线程")
    print("🔍 启动文件监控线程...")
    print("💡 现在可以随时添加新文件到input目录")

    last_files = set()

    while file_monitor_active and not graceful_shutdown:
        try:
            # 获取当前文件列表
            current_files = set(get_supported_files())

            # 检查新增文件
            new_files = current_files - last_files - processed_files

            if new_files:
                logger.info(f"发现 {len(new_files)} 个新文件: {[os.path.basename(f) for f in new_files]}")
                print(f"\n🆕 发现 {len(new_files)} 个新文件:")
                for file_path in sorted(new_files):
                    filename = os.path.basename(file_path)
                    print(f"   - {filename}")

                # 等待文件写入完成并处理
                for file_path in sorted(new_files):
                    if graceful_shutdown:
                        logger.debug("检测到优雅关闭信号，停止文件处理")
                        break

                    filename = os.path.basename(file_path)
                    logger.info(f"开始检查新文件: {filename}")
                    print(f"\n📝 检查新文件: {filename}")

                    # 等待文件稳定
                    if is_file_stable(file_path):
                        logger.info(f"文件稳定，开始处理: {filename}")
                        print(f"✅ 文件稳定，开始处理: {filename}")

                        # 处理新文件
                        base_name = os.path.splitext(filename)[0]
                        output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

                        success = process_file_with_incremental_save(file_path, output_file)

                        if success:
                            processed_files.add(file_path)
                            logger.info(f"新文件处理完成: {filename}")
                            print(f"✅ 新文件处理完成: {filename}")
                        else:
                            logger.warning(f"新文件处理失败: {filename}")
                            print(f"❌ 新文件处理失败: {filename}")
                    else:
                        logger.debug(f"文件可能还在写入，稍后重试: {filename}")
                        print(f"⏳ 文件可能还在写入，稍后重试: {filename}")

            last_files = current_files.copy()

            # 每10秒检查一次
            time.sleep(10)

        except Exception as e:
            logger.error(f"文件监控异常: {e}", exc_info=True)
            print(f"⚠️ 文件监控异常: {e}")
            time.sleep(5)

    logger.info("文件监控线程结束")
    print("🔍 文件监控线程结束")

def process_file_with_incremental_save(input_path, output_path):
    """带增量保存的文件处理"""
    global current_output_file, processed_results, graceful_shutdown, order_preserver

    # 设置当前输出文件
    current_output_file = output_path
    processed_results = []

    # 初始化顺序保持器
    order_preserver = OrderPreserver()
    logger.info("初始化顺序保持器，确保输出顺序正确")

    logger.info(f"开始处理文件: {os.path.basename(input_path)}")
    print(f"📁 输入文件: {input_path}")
    print(f"📄 输出文件: {output_path}")
    print(f"💾 增量保存: 每200个产品自动备份")
    print(f"🛑 支持优雅终止: Ctrl+C 或 kill 会保存已处理结果")

    # 读取文件
    try:
        logger.debug(f"读取输入文件: {input_path}")
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
        logger.info(f"文件读取成功: {len(products)} 个产品")
    except Exception as e:
        logger.error(f"读取文件错误: {e}", exc_info=True)
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 产品数量: {len(products):,} 个")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), BATCH_SIZE):
        batch_num = (i // BATCH_SIZE) + 1
        batch = products[i:i+BATCH_SIZE]
        batch_data.append((batch_num, batch))

    logger.info(f"准备处理: {len(batch_data)} 个批次，{MAX_WORKERS} 个线程")
    print(f"🔄 开始处理 ({len(batch_data)} 个批次，{MAX_WORKERS} 个线程)...")

    start_time = time.time()

    # 并发处理
    try:
        logger.info("开始并发处理批次")
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_batch = {
                executor.submit(ai_optimize_products, batch, "auto"): batch[0]
                for batch in batch_data
            }

            completed_batches = 0

            for future in as_completed(future_to_batch):
                if graceful_shutdown:
                    logger.info("检测到优雅关闭信号，停止处理")
                    break

                try:
                    batch_num, batch_results = future.result()
                    if batch_results:
                        completed_batches += 1

                        # 显示进度
                        progress = (completed_batches / len(batch_data)) * 100
                        elapsed = time.time() - start_time
                        eta = (elapsed / completed_batches) * (len(batch_data) - completed_batches) if completed_batches > 0 else 0

                        logger.info(f"进度更新: {progress:.1f}% ({completed_batches}/{len(batch_data)}) | ETA: {eta/60:.1f}分钟")
                        print(f"📊 进度: {progress:.1f}% ({completed_batches}/{len(batch_data)}) | ETA: {eta/60:.1f}分钟")

                except Exception as e:
                    logger.error(f"批次处理异常: {e}", exc_info=True)
                    print(f"❌ 批次处理异常: {e}")

    except KeyboardInterrupt:
        logger.warning("用户中断处理")
        print(f"\n🛑 用户中断处理")
        graceful_shutdown = True
        save_partial_results()
        return False

    # 处理剩余的未写入批次（如果有的话）
    if order_preserver:
        status = order_preserver.get_status()
        if status["pending_batches"]:
            logger.warning(f"发现未按顺序写入的批次: {status['pending_batches']}")
            print(f"⚠️ 发现未按顺序写入的批次: {status['pending_batches']}")
            # 强制写入剩余批次（虽然可能顺序不对，但不丢失数据）
            for batch_num in status["pending_batches"]:
                if batch_num in order_preserver.completed_batches:
                    remaining_results = order_preserver.completed_batches[batch_num]
                    add_processed_result(remaining_results)
                    logger.info(f"强制写入剩余批次 {batch_num}: {len(remaining_results)} 个产品")

    # 保存最终结果
    if not graceful_shutdown and processed_results:
        try:
            logger.info(f"开始保存最终结果: {len(processed_results)} 个产品")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in processed_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

            elapsed_time = time.time() - start_time
            speed = len(processed_results)/(elapsed_time/60) if elapsed_time > 0 else 0

            logger.info(f"文件处理完成: {len(processed_results)} 个产品, 耗时 {elapsed_time/60:.1f} 分钟, 速度 {speed:.0f} 产品/分钟")

            # 顺序验证日志
            if order_preserver:
                status = order_preserver.get_status()
                logger.info(f"顺序保持器状态: 已写入 {status['written_count']} 个批次，无剩余批次")
                print(f"📋 顺序验证: 已按正确顺序写入 {status['written_count']} 个批次")

            print(f"\n✅ 处理完成!")
            print(f"📊 成功处理: {len(processed_results)} 个产品")
            print(f"⏱️ 总时间: {elapsed_time/60:.1f} 分钟")
            print(f"🚀 处理速度: {speed:.0f} 产品/分钟")
            print(f"💾 结果保存到: {output_path}")

            return True

        except Exception as e:
            logger.error(f"保存最终结果错误: {e}", exc_info=True)
            print(f"❌ 保存最终结果错误: {e}")
            save_partial_results()
            return False

    logger.warning("文件处理未完成或被中断")
    return False

def is_background_mode():
    """检测是否为后台运行模式"""
    import sys
    return not sys.stdin.isatty()

def select_performance_mode():
    """选择性能模式"""
    global MAX_WORKERS, BATCH_SIZE, REQUEST_DELAY

    background = is_background_mode()

    if background:
        # 后台模式默认使用平衡模式
        mode = "balanced"
        print(f"🤖 后台模式: 自动选择平衡性能模式")
    else:
        # 交互模式提供选择
        print(f"\n⚡ 性能模式选择:")
        print(f"1. 保守模式 - 6线程, 20产品/批次 (稳定优先)")
        print(f"2. 平衡模式 - 8线程, 25产品/批次 (推荐, 1.5倍性能)")
        print(f"3. 激进模式 - 10线程, 30产品/批次 (2倍性能)")

        try:
            choice = input("请选择模式 (1/2/3, 默认2): ").strip()
            if choice == "1":
                mode = "conservative"
            elif choice == "3":
                mode = "aggressive"
            else:
                mode = "balanced"  # 默认
        except (EOFError, KeyboardInterrupt):
            mode = "balanced"  # 默认

    # 应用配置
    config = PERFORMANCE_MODES[mode]
    MAX_WORKERS = config["workers"]
    BATCH_SIZE = config["batch_size"]
    REQUEST_DELAY = config["delay"]

    print(f"⚡ 性能配置: {mode.upper()} 模式")
    print(f"   线程数: {MAX_WORKERS}")
    print(f"   批次大小: {BATCH_SIZE} 产品/批次")
    print(f"   请求延迟: {REQUEST_DELAY} 秒")
    print(f"   说明: {config['description']}")

    # 计算预期性能提升
    baseline_speed = 6 * 20 / 0.1  # 保守模式基准
    current_speed = MAX_WORKERS * BATCH_SIZE / REQUEST_DELAY
    speedup = current_speed / baseline_speed

    print(f"   预期提速: {speedup:.1f}x")

    return mode

def main():
    """主函数"""
    global file_monitor_active, processed_files, graceful_shutdown

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # kill命令

    logger.info("程序启动")
    print("🚀 高性能增量保存产品优化器 + 动态文件监控")
    print("="*60)
    print(f"💾 特性: 支持中途终止时保存已处理结果")
    print(f"🛑 优雅终止: Ctrl+C 或 kill 会自动保存")
    print(f"🔍 动态监控: 可在处理过程中添加新文件")
    print(f"⚡ 性能优化: 支持1-2倍速度提升")
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"🔄 API类型: DeepSeek Chat (高性能)")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")

    # 选择性能模式
    performance_mode = select_performance_mode()
    logger.info(f"选择性能模式: {performance_mode}")

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logger.info("目录检查完成")

    # 查找初始文件
    txt_files = get_supported_files()
    logger.info(f"发现初始文件: {len(txt_files)} 个")

    if not txt_files:
        logger.info("input目录为空，启动文件监控等待新文件")
        print(f"📋 input目录为空，启动文件监控等待新文件...")
    else:
        logger.info(f"开始处理 {len(txt_files)} 个初始文件")
        print(f"📋 发现 {len(txt_files)} 个初始文件，开始处理...")

    # 启动文件监控线程
    file_monitor_active = True
    monitor_thread = threading.Thread(target=file_monitor_thread, daemon=True)
    monitor_thread.start()
    logger.info("文件监控线程已启动")

    # 启动性能监控线程
    perf_thread = threading.Thread(target=monitor_performance, daemon=True)
    perf_thread.start()
    logger.info("性能监控线程已启动")

    success_count = 0
    total_start_time = time.time()

    # 处理初始文件
    for i, input_file in enumerate(txt_files, 1):
        if graceful_shutdown:
            logger.info("检测到优雅关闭信号，停止初始文件处理")
            break

        # 标记文件为正在处理，避免重复处理
        processed_files.add(input_file)

        logger.info(f"开始处理初始文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"\n{'='*60}")
        print(f"🔄 处理初始文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

        success = process_file_with_incremental_save(input_file, output_file)

        if success:
            success_count += 1
            logger.info(f"初始文件 {i} 处理完成: {os.path.basename(input_file)}")
            print(f"✅ 初始文件 {i} 处理完成")
        else:
            logger.warning(f"初始文件 {i} 处理失败或中断: {os.path.basename(input_file)}")
            print(f"❌ 初始文件 {i} 处理失败或中断")

    # 如果没有被中断，继续监控新文件
    if not graceful_shutdown:
        logger.info(f"初始文件处理完成: {success_count}/{len(txt_files)} 个文件成功")
        print(f"\n{'='*60}")
        print(f"🎉 初始文件处理完成!")
        print(f"📊 成功处理: {success_count}/{len(txt_files)} 个初始文件")
        print(f"⏱️ 初始处理耗时: {(time.time() - total_start_time)/60:.1f} 分钟")
        print(f"🔍 继续监控新文件... (添加文件到 {INPUT_DIR})")
        print(f"💡 按 Ctrl+C 停止监控并退出")
        print(f"{'='*60}")

        # 保持监控状态
        logger.info("进入文件监控模式")
        try:
            while file_monitor_active and not graceful_shutdown:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用户停止监控")
            print(f"\n🛑 用户停止监控")
            graceful_shutdown = True

    # 停止文件监控
    file_monitor_active = False
    logger.info("停止文件监控")

    # 最终总结
    total_elapsed = time.time() - total_start_time
    logger.info(f"程序结束: 处理了 {len(processed_files)} 个文件，总耗时 {total_elapsed/60:.1f} 分钟")
    print(f"\n{'='*60}")
    if graceful_shutdown:
        logger.info("程序被优雅中断")
        print(f"🛑 处理被中断")
    else:
        logger.info("程序正常结束")
        print(f"🎉 监控结束!")
    print(f"📊 总共处理: {len(processed_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
