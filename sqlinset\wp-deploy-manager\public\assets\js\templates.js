/**
 * 模板管理模块
 */

class TemplateManager {
    constructor() {
        this.templates = [];
        this.init();
    }

    init() {
        this.loadTemplates();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 上传模板按钮
        const uploadBtn = document.querySelector('[onclick="showUploadTemplateModal()"]');
        if (uploadBtn) {
            uploadBtn.onclick = () => this.showUploadModal();
        }

        // 批量测试按钮
        const testBtn = document.querySelector('[onclick="testAllTemplates()"]');
        if (testBtn) {
            testBtn.onclick = () => this.testAllTemplates();
        }
    }

    async loadTemplates() {
        try {
            const response = await api.getTemplates();
            this.templates = response.data || [];
            this.renderTemplates();
        } catch (error) {
            console.error('Failed to load templates:', error);
            app.showNotification('加载模板失败', 'error');
        }
    }

    renderTemplates() {
        const container = document.getElementById('templates-grid');
        if (!container) return;

        if (this.templates.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📦</div>
                    <h3>暂无模板</h3>
                    <p>请上传第一个WordPress模板开始使用</p>
                    <button class="btn btn-primary" onclick="templateManager.showUploadModal()">
                        <i class="icon-upload"></i> 上传模板
                    </button>
                </div>
            `;
            return;
        }

        const html = this.templates.map(template => this.renderTemplateCard(template)).join('');
        container.innerHTML = html;
    }

    renderTemplateCard(template) {
        const sizeFormatted = app.formatFileSize(template.file_size);
        const lastUsed = template.last_used_at ? app.formatRelativeTime(template.last_used_at) : '从未使用';
        
        return `
            <div class="template-card" data-template-id="${template.id}">
                <div class="template-header">
                    <div class="template-icon">
                        <i class="icon-template"></i>
                    </div>
                    <div class="template-status status-${template.status}">
                        ${this.getStatusText(template.status)}
                    </div>
                </div>
                
                <div class="template-content">
                    <h3 class="template-name">${template.name}</h3>
                    <p class="template-description">${template.description || '无描述'}</p>
                    
                    <div class="template-meta">
                        <div class="meta-item">
                            <span class="meta-label">文件大小:</span>
                            <span class="meta-value">${sizeFormatted}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">母站域名:</span>
                            <span class="meta-value">${template.mother_domain || '未设置'}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">使用次数:</span>
                            <span class="meta-value">${template.usage_count || 0}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">最后使用:</span>
                            <span class="meta-value">${lastUsed}</span>
                        </div>
                    </div>
                </div>
                
                <div class="template-actions">
                    <button class="btn btn-sm btn-primary" onclick="templateManager.useTemplate(${template.id})">
                        <i class="icon-deploy"></i> 使用
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="templateManager.editTemplate(${template.id})">
                        <i class="icon-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-info" onclick="templateManager.testTemplate(${template.id})">
                        <i class="icon-test"></i> 测试
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="templateManager.deleteTemplate(${template.id})">
                        <i class="icon-delete"></i> 删除
                    </button>
                </div>
            </div>
        `;
    }

    getStatusText(status) {
        const texts = {
            'active': '活跃',
            'inactive': '停用',
            'testing': '测试中',
            'error': '错误'
        };
        return texts[status] || status;
    }

    showUploadModal() {
        const modal = new Modal({
            title: '上传WordPress模板',
            size: 'large',
            content: `
                <form id="upload-template-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="template-file">模板文件 *</label>
                        <input type="file" id="template-file" name="template_file" accept=".tar.gz,.tgz,.zip" required>
                        <small class="form-help">支持 .tar.gz, .tgz, .zip 格式，最大500MB</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="template-name">模板名称 *</label>
                        <input type="text" id="template-name" name="template_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="template-description">模板描述</label>
                        <textarea id="template-description" name="template_description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="mother-domain">母站域名 *</label>
                        <input type="text" id="mother-domain" name="mother_domain" placeholder="example.com" required>
                        <small class="form-help">原始WordPress站点的域名</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="mother-db">母站数据库名 *</label>
                        <input type="text" id="mother-db" name="mother_db" required>
                        <small class="form-help">原始WordPress站点的数据库名</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="mysql-root-pass">MySQL Root密码 *</label>
                        <input type="password" id="mysql-root-pass" name="mysql_root_pass" required>
                        <small class="form-help">用于创建新数据库</small>
                    </div>
                    
                    <div class="upload-progress" id="upload-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="upload-progress-fill"></div>
                        </div>
                        <span class="progress-text" id="upload-progress-text">0%</span>
                    </div>
                </form>
            `,
            onConfirm: () => this.uploadTemplate()
        });

        modal.open();
    }

    async uploadTemplate() {
        const form = document.getElementById('upload-template-form');
        const formData = new FormData(form);
        
        const progressContainer = document.getElementById('upload-progress');
        const progressFill = document.getElementById('upload-progress-fill');
        const progressText = document.getElementById('upload-progress-text');
        
        try {
            progressContainer.style.display = 'block';
            
            const response = await api.uploadTemplate(formData, (progress) => {
                const percentage = Math.round(progress * 100);
                progressFill.style.width = percentage + '%';
                progressText.textContent = percentage + '%';
            });
            
            app.showNotification('模板上传成功', 'success');
            this.loadTemplates();
            
        } catch (error) {
            console.error('Template upload failed:', error);
            app.showNotification('模板上传失败: ' + error.message, 'error');
        } finally {
            progressContainer.style.display = 'none';
        }
    }

    useTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;

        // 显示部署选择模态框
        const modal = new Modal({
            title: '选择部署方式',
            content: `
                <div class="deploy-options">
                    <div class="deploy-option" onclick="templateManager.singleDeploy(${templateId})">
                        <div class="option-icon">🎯</div>
                        <h4>单域名部署</h4>
                        <p>部署到单个域名</p>
                    </div>
                    
                    <div class="deploy-option" onclick="templateManager.batchDeploy(${templateId})">
                        <div class="option-icon">📦</div>
                        <h4>批量部署</h4>
                        <p>部署到多个域名</p>
                    </div>
                </div>
            `
        });

        modal.open();
    }

    singleDeploy(templateId) {
        app.showNotification('单域名部署功能开发中', 'info');
    }

    batchDeploy(templateId) {
        app.showNotification('批量部署功能开发中', 'info');
    }

    editTemplate(templateId) {
        app.showNotification('编辑模板功能开发中', 'info');
    }

    async testTemplate(templateId) {
        try {
            app.showNotification('开始测试模板...', 'info');
            
            const response = await api.testTemplate(templateId);
            
            if (response.success) {
                app.showNotification('模板测试通过', 'success');
            } else {
                app.showNotification('模板测试失败: ' + response.error, 'error');
            }
            
        } catch (error) {
            console.error('Template test failed:', error);
            app.showNotification('模板测试失败', 'error');
        }
    }

    async deleteTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;

        if (!confirm(`确定要删除模板 "${template.name}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await api.deleteTemplate(templateId);
            app.showNotification('模板已删除', 'success');
            this.loadTemplates();
            
        } catch (error) {
            console.error('Template deletion failed:', error);
            app.showNotification('删除模板失败', 'error');
        }
    }

    async testAllTemplates() {
        if (this.templates.length === 0) {
            app.showNotification('没有可测试的模板', 'warning');
            return;
        }

        if (!confirm('确定要测试所有模板吗？这可能需要一些时间。')) {
            return;
        }

        app.showNotification('开始批量测试模板...', 'info');

        for (const template of this.templates) {
            try {
                await this.testTemplate(template.id);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
            } catch (error) {
                console.error(`Template ${template.id} test failed:`, error);
            }
        }

        app.showNotification('批量测试完成', 'success');
    }
}

// 全局函数
function showUploadTemplateModal() {
    if (window.templateManager) {
        window.templateManager.showUploadModal();
    }
}

function testAllTemplates() {
    if (window.templateManager) {
        window.templateManager.testAllTemplates();
    }
}

// 创建全局实例
window.templateManager = new TemplateManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TemplateManager;
}
