<?php
/**
 * JavaScript问题修复脚本
 * 修复API baseUrl和其他前端问题
 */

echo "🔧 开始修复JavaScript问题...\n\n";

// 1. 修复API.js中的baseUrl问题
echo "1. 修复API.js baseUrl配置...\n";
$apiJsFile = __DIR__ . '/public/assets/js/api.js';

if (file_exists($apiJsFile)) {
    $content = file_get_contents($apiJsFile);
    $originalContent = $content;
    
    // 修复各种可能的错误baseUrl
    $patterns = [
        '/this\.baseUrl\s*=\s*[\'"]\/wp-deploy-manager\/api[\'"]/',
        '/this\.baseUrl\s*=\s*[\'"]\w+\/wp-deploy-manager\/api[\'"]/',
        '/this\.baseUrl\s*=\s*[\'"][^\'\"]*wp-deploy-manager[^\'\"]*[\'"]/'
    ];
    
    foreach ($patterns as $pattern) {
        $content = preg_replace($pattern, "this.baseUrl = '/api'", $content);
    }
    
    // 移除多余的版本注释
    $content = preg_replace('/^\/\* Version:.*\*\/\n/m', '', $content);
    
    // 添加新的版本注释
    $version = date('YmdHis');
    $content = "/* Version: $version - Fixed baseUrl */\n" . $content;
    
    if ($content !== $originalContent) {
        file_put_contents($apiJsFile, $content);
        echo "   ✅ API.js baseUrl 已修复\n";
    } else {
        echo "   ℹ️  API.js baseUrl 无需修复\n";
    }
    
    // 验证修复结果
    if (strpos($content, "this.baseUrl = '/api'") !== false) {
        echo "   ✅ baseUrl 验证通过\n";
    } else {
        echo "   ❌ baseUrl 验证失败\n";
    }
} else {
    echo "   ❌ API.js 文件不存在: $apiJsFile\n";
}

// 2. 修复HTML中的JavaScript引用
echo "\n2. 修复HTML中的JavaScript引用...\n";
$indexFile = __DIR__ . '/public/index.html';

if (file_exists($indexFile)) {
    $content = file_get_contents($indexFile);
    $originalContent = $content;
    
    // 移除可能的错误版本参数
    $content = preg_replace('/\?v=[^"]*%[^"]*/', '', $content);
    $content = preg_replace('/\?v=>[^"]*/', '', $content);
    
    // 添加正确的版本参数
    $version = date('YmdHis');
    $content = preg_replace(
        '/src="assets\/js\/([^"]+\.js)"(?!\?v=)/',
        'src="assets/js/$1?v=' . $version . '"',
        $content
    );
    
    if ($content !== $originalContent) {
        file_put_contents($indexFile, $content);
        echo "   ✅ HTML JavaScript引用已修复\n";
    } else {
        echo "   ℹ️  HTML JavaScript引用无需修复\n";
    }
    
    // 验证修复结果
    $jsReferences = preg_match_all('/src="assets\/js\/[^"]+\.js\?v=\d+"/', $content);
    echo "   ℹ️  找到 $jsReferences 个JavaScript引用\n";
} else {
    echo "   ❌ index.html 文件不存在: $indexFile\n";
}

// 3. 检查和修复其他JavaScript文件
echo "\n3. 检查其他JavaScript文件...\n";
$jsDir = __DIR__ . '/public/assets/js';
$jsFiles = glob($jsDir . '/*.js');

foreach ($jsFiles as $jsFile) {
    $filename = basename($jsFile);
    if ($filename === 'api.js') continue; // 已经处理过
    
    $content = file_get_contents($jsFile);
    $hasIssues = false;
    
    // 检查是否有错误的API路径引用
    if (preg_match('/wp-deploy-manager\/api/', $content)) {
        echo "   ⚠️  $filename 包含错误的API路径引用\n";
        $content = preg_replace('/wp-deploy-manager\/api/', 'api', $content);
        $hasIssues = true;
    }
    
    // 检查是否有语法错误（简单检查）
    if (preg_match('/\}\s*\{/', $content)) {
        echo "   ⚠️  $filename 可能有语法问题\n";
    }
    
    if ($hasIssues) {
        file_put_contents($jsFile, $content);
        echo "   ✅ $filename 已修复\n";
    } else {
        echo "   ✅ $filename 检查通过\n";
    }
}

// 4. 创建JavaScript测试页面
echo "\n4. 创建JavaScript测试页面...\n";
$testPageContent = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .result { margin: 10px 0; padding: 8px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🧪 JavaScript功能测试</h1>
    
    <div class="test-section">
        <h3>1. API对象测试</h3>
        <button onclick="testAPIObject()">测试API对象</button>
        <div id="api-test-results"></div>
    </div>
    
    <div class="test-section">
        <h3>2. API连接测试</h3>
        <button onclick="testAPIConnection()">测试API连接</button>
        <div id="connection-test-results"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 页面功能测试</h3>
        <button onclick="testPageFunctions()">测试页面功能</button>
        <div id="function-test-results"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 返回主页面</h3>
        <button onclick="goToMainPage()">返回主页面</button>
        <button onclick="clearCacheAndGo()">清除缓存并返回</button>
    </div>

    <script src="assets/js/api.js"></script>
    <script>
        function addResult(containerId, message, type = "info") {
            const container = document.getElementById(containerId);
            const div = document.createElement("div");
            div.className = `result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = "";
        }
        
        function testAPIObject() {
            clearResults("api-test-results");
            
            // 检查API类是否存在
            if (typeof API !== "undefined") {
                addResult("api-test-results", "✅ API类已加载", "success");
                
                try {
                    const api = new API();
                    addResult("api-test-results", `✅ API实例创建成功`, "success");
                    addResult("api-test-results", `📍 baseUrl: ${api.baseUrl}`, "info");
                    
                    if (api.baseUrl === "/api") {
                        addResult("api-test-results", "✅ baseUrl配置正确", "success");
                    } else {
                        addResult("api-test-results", "❌ baseUrl配置错误", "error");
                    }
                } catch (error) {
                    addResult("api-test-results", `❌ API实例创建失败: ${error.message}`, "error");
                }
            } else {
                addResult("api-test-results", "❌ API类未加载", "error");
            }
            
            // 检查全局API实例
            if (typeof window.api !== "undefined") {
                addResult("api-test-results", "✅ 全局API实例存在", "success");
            } else {
                addResult("api-test-results", "❌ 全局API实例不存在", "error");
            }
        }
        
        async function testAPIConnection() {
            clearResults("connection-test-results");
            addResult("connection-test-results", "🔄 测试中...", "info");
            
            const endpoints = ["/", "/deploy.php", "/templates.php", "/status.php"];
            
            for (const endpoint of endpoints) {
                try {
                    const url = `/api${endpoint}`;
                    const response = await fetch(url);
                    const status = response.ok ? "success" : "error";
                    const icon = response.ok ? "✅" : "❌";
                    
                    addResult("connection-test-results", 
                        `${icon} ${url} - HTTP ${response.status}`, status);
                    
                    if (endpoint === "/" && response.ok) {
                        const data = await response.json();
                        addResult("connection-test-results", 
                            `<pre>${JSON.stringify(data, null, 2)}</pre>`, "info");
                    }
                } catch (error) {
                    addResult("connection-test-results", 
                        `❌ /api${endpoint} - 错误: ${error.message}`, "error");
                }
            }
        }
        
        function testPageFunctions() {
            clearResults("function-test-results");
            
            // 检查导航元素
            const navItems = document.querySelectorAll(".nav-item");
            addResult("function-test-results", 
                `📊 导航项数量: ${navItems.length}`, "info");
            
            // 检查其他关键元素
            const elements = [
                { selector: ".main-content", name: "主内容区" },
                { selector: ".navbar", name: "导航栏" },
                { selector: ".tab-content", name: "标签内容" }
            ];
            
            elements.forEach(el => {
                const found = document.querySelector(el.selector);
                const status = found ? "success" : "error";
                const icon = found ? "✅" : "❌";
                addResult("function-test-results", 
                    `${icon} ${el.name}`, status);
            });
        }
        
        function goToMainPage() {
            window.location.href = "/?test=" + Date.now();
        }
        
        function clearCacheAndGo() {
            if ("caches" in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            window.location.href = "/?nocache=" + Date.now();
        }
        
        // 页面加载时自动运行测试
        window.addEventListener("load", () => {
            setTimeout(() => {
                testAPIObject();
                testAPIConnection();
            }, 500);
        });
    </script>
</body>
</html>';

$testPageFile = __DIR__ . '/public/js-test.html';
file_put_contents($testPageFile, $testPageContent);
echo "   ✅ JavaScript测试页面已创建: /js-test.html\n";

// 5. 创建修复验证脚本
echo "\n5. 创建修复验证脚本...\n";
$verifyScript = '#!/bin/bash

echo "🔍 验证JavaScript修复结果..."

# 检查API.js文件
echo "1. 检查API.js文件:"
if grep -q "this.baseUrl = \'/api\'" public/assets/js/api.js; then
    echo "   ✅ API baseUrl 配置正确"
else
    echo "   ❌ API baseUrl 配置错误"
fi

# 检查HTML文件
echo "2. 检查HTML文件:"
if grep -q "assets/js/.*\.js?v=" public/index.html; then
    echo "   ✅ JavaScript文件包含版本参数"
else
    echo "   ❌ JavaScript文件缺少版本参数"
fi

# 检查是否有错误的路径
echo "3. 检查错误路径:"
if grep -r "wp-deploy-manager/api" public/assets/js/ 2>/dev/null; then
    echo "   ❌ 发现错误的API路径"
else
    echo "   ✅ 未发现错误的API路径"
fi

echo ""
echo "🧪 测试建议:"
echo "1. 访问 http://localhost/js-test.html 进行功能测试"
echo "2. 清除浏览器缓存后访问主页面"
echo "3. 检查浏览器控制台是否有错误"
';

$verifyScriptFile = __DIR__ . '/verify_js_fix.sh';
file_put_contents($verifyScriptFile, $verifyScript);
chmod($verifyScriptFile, 0755);
echo "   ✅ 验证脚本已创建: verify_js_fix.sh\n";

echo "\n🎉 JavaScript问题修复完成！\n";
echo "📋 修复总结:\n";
echo "   ✅ API.js baseUrl 配置\n";
echo "   ✅ HTML JavaScript引用\n";
echo "   ✅ 其他JavaScript文件检查\n";
echo "   ✅ 测试页面创建\n";
echo "   ✅ 验证脚本创建\n";
echo "\n🧪 下一步:\n";
echo "1. 运行验证脚本: ./verify_js_fix.sh\n";
echo "2. 访问测试页面: http://localhost/js-test.html\n";
echo "3. 清除浏览器缓存并测试主页面\n";
?>
