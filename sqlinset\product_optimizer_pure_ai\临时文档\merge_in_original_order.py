#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按原始输入文件顺序完美合并成功结果和重新处理的失败产品
"""

import os
import sys

def merge_in_original_order(input_file, original_output, retry_output, final_output):
    """按原始输入文件顺序合并结果"""
    
    print("🔄 按原始顺序合并结果")
    print("="*60)
    
    # 读取原始输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            input_products = [line.strip() for line in f if line.strip()]
        print(f"📁 输入文件: {len(input_products):,} 个产品")
    except Exception as e:
        print(f"❌ 读取输入文件失败: {e}")
        return False
    
    # 读取原始输出文件（成功的结果）
    original_results = {}
    try:
        with open(original_output, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 跳过标题行，提取成功的结果
        for line in lines[2:]:
            if line.strip() and not line.startswith('=') and '[FAILED]' not in line:
                # 提取SEO名称，尝试匹配原始产品
                seo_name = line.split('|')[0].strip()
                original_results[len(original_results)] = line.strip()
        
        print(f"📄 原始输出: {len(original_results):,} 个成功产品")
    except Exception as e:
        print(f"❌ 读取原始输出失败: {e}")
        return False
    
    # 读取重新处理的结果
    retry_results = {}
    try:
        with open(retry_output, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 跳过标题行，提取重新处理的结果
        for line in lines[2:]:
            if line.strip() and not line.startswith('='):
                retry_results[len(retry_results)] = line.strip()
        
        print(f"🔄 重新处理: {len(retry_results):,} 个产品")
    except Exception as e:
        print(f"❌ 读取重新处理结果失败: {e}")
        return False
    
    # 读取失败产品列表，建立映射
    failed_products = []
    failed_report_file = os.path.join(os.path.dirname(original_output), "failed_products_report.txt")
    
    if os.path.exists(failed_report_file):
        try:
            with open(failed_report_file, 'r', encoding='utf-8') as f:
                failed_products = [line.strip() for line in f if line.strip()]
            print(f"📋 失败产品列表: {len(failed_products):,} 个")
        except Exception as e:
            print(f"⚠️ 读取失败产品列表失败: {e}")
    
    # 按原始顺序合并结果
    final_results = []
    success_index = 0
    retry_index = 0
    
    print(f"\n🔄 开始按原始顺序合并...")
    
    for i, input_product in enumerate(input_products):
        # 检查这个产品是否是失败产品
        is_failed = input_product in failed_products
        
        if is_failed and retry_index < len(retry_results):
            # 使用重新处理的结果
            final_results.append(retry_results[retry_index])
            retry_index += 1
            if (i + 1) % 1000 == 0:
                print(f"   处理进度: {i+1:,}/{len(input_products):,} (使用重新处理结果)")
        elif not is_failed and success_index < len(original_results):
            # 使用原始成功结果
            final_results.append(original_results[success_index])
            success_index += 1
            if (i + 1) % 1000 == 0:
                print(f"   处理进度: {i+1:,}/{len(input_products):,} (使用原始结果)")
        else:
            # 异常情况：生成占位符
            final_results.append(f"[ERROR] {input_product} | ERROR > Processing Error | error")
            print(f"⚠️ 第 {i+1} 行产品无对应结果: {input_product}")
    
    # 保存最终结果
    try:
        with open(final_output, 'w', encoding='utf-8') as f:
            f.write("SEO Name | Category | Tags\n")
            f.write("="*120 + "\n")
            
            for result in final_results:
                f.write(f"{result}\n")
        
        print(f"\n✅ 合并完成!")
        print(f"📊 最终结果: {len(final_results):,} 个产品")
        print(f"📊 使用原始结果: {success_index:,} 个")
        print(f"📊 使用重新处理: {retry_index:,} 个")
        print(f"💾 保存到: {final_output}")
        
        # 验证数量
        expected_total = len(input_products)
        if len(final_results) == expected_total:
            print(f"✅ 数量验证通过: {len(final_results):,} = {expected_total:,}")
        else:
            print(f"⚠️ 数量不匹配: {len(final_results):,} ≠ {expected_total:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存最终结果失败: {e}")
        return False

def show_usage():
    """显示使用说明"""
    
    print("📋 按原始顺序合并工具")
    print("="*60)
    
    print("🎯 功能:")
    print("   完美保持输入文件的原始顺序")
    print("   将成功结果和重新处理的失败产品按原位置合并")
    print()
    
    print("📊 排序保证:")
    print("   ✅ 原版脚本: 按批次号排序，保持输入顺序")
    print("   ✅ 重新处理: 同样按批次号排序，保持失败产品的相对顺序")
    print("   ✅ 最终合并: 按输入文件的绝对位置合并")
    print()
    
    print("💡 使用方法:")
    print("   python3 merge_in_original_order.py \\")
    print("     <输入文件> \\")
    print("     <原始输出文件> \\")
    print("     <重新处理输出文件> \\")
    print("     <最终输出文件>")
    print()
    
    print("📝 示例:")
    print("   python3 merge_in_original_order.py \\")
    print("     /path/to/input/original.txt \\")
    print("     /path/to/output/original_backup.txt \\")
    print("     /path/to/output/retry_optimized.txt \\")
    print("     /path/to/output/final_perfect_order.txt")

def main():
    """主函数"""
    if len(sys.argv) == 5:
        # 命令行模式
        input_file = sys.argv[1]
        original_output = sys.argv[2]
        retry_output = sys.argv[3]
        final_output = sys.argv[4]
        
        success = merge_in_original_order(input_file, original_output, retry_output, final_output)
        sys.exit(0 if success else 1)
    else:
        # 帮助模式
        show_usage()

if __name__ == "__main__":
    main()
