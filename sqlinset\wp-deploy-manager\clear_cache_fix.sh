#!/bin/bash

# 清除缓存并修复URL问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  清除缓存并修复URL问题"
echo "==================================================${NC}"

# 1. 添加版本号到JavaScript文件，强制浏览器重新加载
echo -e "${BLUE}[1/5]${NC} 添加版本号到JavaScript文件..."

VERSION=$(date +%Y%m%d%H%M%S)
echo "版本号: $VERSION"

# 在API.js开头添加版本注释
sed -i "1i/* Version: $VERSION */" public/assets/js/api.js

# 2. 修复可能的路径问题
echo -e "${BLUE}[2/5]${NC} 检查并修复API路径..."

# 确保API.js中的baseUrl正确
if grep -q "this.baseUrl = '/api'" public/assets/js/api.js; then
    echo "✓ API baseUrl 正确"
else
    echo "⚠️  修复API baseUrl"
    sed -i "s|this.baseUrl = '.*'|this.baseUrl = '/api'|g" public/assets/js/api.js
fi

# 3. 检查HTML中的JavaScript加载顺序
echo -e "${BLUE}[3/5]${NC} 检查JavaScript加载顺序..."

echo "HTML中的JavaScript文件加载顺序:"
grep -n "script src" public/index.html

# 4. 创建API测试页面
echo -e "${BLUE}[4/5]${NC} 创建API测试页面..."

cat > public/api-test.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 API路径测试</h1>
    
    <div id="results"></div>
    
    <button onclick="testAPI()">测试API</button>
    <button onclick="clearResults()">清除结果</button>
    
    <script>
        // 直接定义API类，避免依赖外部文件
        class TestAPI {
            constructor() {
                this.baseUrl = '/api';
            }
            
            async request(endpoint, options = {}) {
                const url = `${this.baseUrl}${endpoint}`;
                console.log('请求URL:', url);
                
                try {
                    const response = await fetch(url, {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        ...options
                    });
                    
                    return {
                        ok: response.ok,
                        status: response.status,
                        url: response.url,
                        data: response.ok ? await response.json() : null
                    };
                } catch (error) {
                    return {
                        ok: false,
                        status: 0,
                        url: url,
                        error: error.message
                    };
                }
            }
        }
        
        const testApi = new TestAPI();
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function testAPI() {
            clearResults();
            addResult('🔍 开始API测试...', 'info');
            
            const endpoints = [
                '/',
                '/deploy.php',
                '/templates.php',
                '/status.php',
                '/logs.php',
                '/settings.php'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const result = await testApi.request(endpoint);
                    
                    const message = `
                        <strong>${endpoint}</strong><br>
                        状态: ${result.status}<br>
                        URL: ${result.url}<br>
                        ${result.error ? '错误: ' + result.error : ''}
                    `;
                    
                    const type = result.ok ? 'success' : 'error';
                    addResult(message, type);
                    
                } catch (error) {
                    addResult(`<strong>${endpoint}</strong><br>异常: ${error.message}`, 'error');
                }
            }
            
            addResult('✅ API测试完成', 'info');
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
EOF

echo "✓ API测试页面已创建: public/api-test.html"

# 5. 设置正确的文件权限和所有者
echo -e "${BLUE}[5/5]${NC} 设置文件权限..."

chown -R www:www public/
find public/ -type f -name "*.js" -exec chmod 644 {} \;
find public/ -type f -name "*.html" -exec chmod 644 {} \;

echo -e "${GREEN}=================================================="
echo "  ✅ 修复完成！"
echo "=================================================="
echo "下一步操作:"
echo "1. 访问测试页面: https://wpd.cloudcheckout.shop/api-test.html"
echo "2. 清除浏览器缓存 (Ctrl+Shift+Delete)"
echo "3. 强制刷新主页面 (Ctrl+F5)"
echo "4. 检查Console错误是否减少"
echo ""
echo "如果仍有问题:"
echo "- 检查浏览器开发者工具 Network 标签"
echo "- 查看实际请求的URL是否正确"
echo "==================================================${NC}"

# 显示当前API文件状态
echo -e "${YELLOW}当前API文件状态:${NC}"
echo "public/api/ 目录:"
ls -la public/api/*.php | head -5

echo ""
echo "测试API基础路径:"
curl -s "https://wpd.cloudcheckout.shop/api/" | head -3

exit 0
