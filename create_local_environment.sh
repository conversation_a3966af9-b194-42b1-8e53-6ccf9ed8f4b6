#!/bin/bash

# WordPress Deploy Manager 本地环境创建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 创建WordPress Deploy Manager本地环境${NC}"
echo "=================================================="

# 检查系统要求
echo -e "${BLUE}[1/8]${NC} 检查系统要求..."

# 检查Docker
if command -v docker &> /dev/null; then
    echo -e "   ✅ Docker 已安装: $(docker --version | cut -d' ' -f3)"
else
    echo -e "   ❌ Docker 未安装，请先安装Docker"
    echo -e "   下载地址: https://docs.docker.com/get-docker/"
    exit 1
fi

# 检查Docker Compose
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
    echo -e "   ✅ Docker Compose 已安装"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
    echo -e "   ✅ Docker Compose (Plugin) 已安装"
else
    echo -e "   ❌ Docker Compose 未安装"
    exit 1
fi

# 创建项目目录结构
echo -e "${BLUE}[2/8]${NC} 创建项目目录结构..."

# 主目录
mkdir -p {api,config,core,database,logs,public,scripts,templates,uploads}
mkdir -p {uploads/temp,uploads/templates}
mkdir -p {public/assets/{css,js,images},public/api}
mkdir -p {docker/{apache,php,mysql,redis,supervisor,scripts}}

echo -e "   ✅ 目录结构创建完成"

# 创建Docker配置文件
echo -e "${BLUE}[3/8]${NC} 创建Docker配置文件..."

# docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: wp-deploy-web
    ports:
      - "80:80"
    volumes:
      - ./:/var/www/html
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/uploads
    depends_on:
      - mysql
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    networks:
      - wp-deploy-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_mysql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - wp-deploy-network
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
    depends_on:
      - mysql
    networks:
      - wp-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  wp-deploy-network:
    driver: bridge
EOF

echo -e "   ✅ docker-compose.yml 创建完成"

# Dockerfile
cat > docker/Dockerfile << 'EOF'
FROM php:8.1-apache

WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev libzip-dev \
    zip unzip nano wget && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip xml curl

# 启用Apache模块
RUN a2enmod rewrite

# 复制Apache配置
COPY docker/apache/vhost.conf /etc/apache2/sites-available/000-default.conf

# 设置权限
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80
EOF

echo -e "   ✅ Dockerfile 创建完成"

# Apache配置
cat > docker/apache/vhost.conf << 'EOF'
<VirtualHost *:80>
    DocumentRoot /var/www/html/public
    ServerName localhost
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF

echo -e "   ✅ Apache配置创建完成"

# 创建配置文件
echo -e "${BLUE}[4/8]${NC} 创建配置文件..."

# 数据库配置
cat > config/database.php << 'EOF'
<?php
// 数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);

// 数据库连接函数
function getDatabase() {
    static $db = null;
    if ($db === null) {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        $db = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
    }
    return $db;
}
?>
EOF

# 主配置文件
cat > config/config.php << 'EOF'
<?php
// 环境配置
define('ENVIRONMENT', 'development');
define('DEBUG_MODE', true);

// 基础路径配置
define('ROOT_PATH', dirname(__DIR__));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('API_PATH', ROOT_PATH . '/api');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// 应用配置
define('MAX_UPLOAD_SIZE', 100 * 1024 * 1024); // 100MB
define('MAX_CONCURRENT_DEPLOYMENTS', 2);
define('DEPLOYMENT_TIMEOUT', 900); // 15分钟

// 时区配置
define('TIMEZONE', 'Asia/Shanghai');
date_default_timezone_set(TIMEZONE);

// 加载数据库配置
require_once ROOT_PATH . '/config/database.php';

// 日志函数
function logMessage($level, $message) {
    $logFile = LOGS_PATH . '/system_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 响应函数
function successResponse($data = null, $message = 'Success') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

function errorResponse($message = 'Error', $code = 500) {
    http_response_code($code);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}
?>
EOF

echo -e "   ✅ 配置文件创建完成"

# 创建数据库初始化文件
echo -e "${BLUE}[5/8]${NC} 创建数据库初始化文件..."

cat > database/init_mysql.sql << 'EOF'
-- WordPress 部署管理系统数据库初始化脚本

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署任务表
CREATE TABLE IF NOT EXISTS deploy_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    type ENUM('single', 'batch') NOT NULL,
    template_id INT,
    domains JSON NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认设置
INSERT IGNORE INTO system_settings (setting_key, setting_value) VALUES
('max_concurrent_jobs', '3'),
('default_timeout', '1800'),
('enable_notifications', 'false');

-- 插入测试模板
INSERT IGNORE INTO templates (uuid, name, description, filename, file_path, file_size, file_hash) VALUES
('test-template-1', '测试模板1', '这是一个测试模板', 'test1.tar.gz', '/uploads/templates/test1.tar.gz', 1024000, 'hash1'),
('test-template-2', '测试模板2', '这是另一个测试模板', 'test2.tar.gz', '/uploads/templates/test2.tar.gz', 2048000, 'hash2');
EOF

echo -e "   ✅ 数据库初始化文件创建完成"

echo -e "${GREEN}✅ 本地环境文件创建完成！${NC}"
echo ""
echo -e "${YELLOW}下一步操作:${NC}"
echo -e "1. 运行: ${BLUE}$DOCKER_COMPOSE_CMD up -d${NC}"
echo -e "2. 等待服务启动完成"
echo -e "3. 访问: ${BLUE}http://localhost${NC}"
echo -e "4. phpMyAdmin: ${BLUE}http://localhost:8080${NC}"
echo ""
echo -e "${GREEN}🎉 准备就绪！${NC}"
