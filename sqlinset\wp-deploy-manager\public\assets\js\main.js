/**
 * 主应用JavaScript
 * 应用程序的入口点和全局功能
 */

class WPDeployManager {
    constructor() {
        this.currentTab = 'dashboard';
        this.eventSources = new Map();
        this.refreshIntervals = new Map();
        this.notifications = [];
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.setupEventListeners();
        this.setupTabNavigation();
        this.setupGlobalRefresh();
        this.setupNotifications();
        this.loadInitialData();
        
        // 启动实时更新
        this.startRealTimeUpdates();
        
        console.log('WordPress Deploy Manager initialized');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showNotification('系统错误：' + event.error.message, 'error');
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showNotification('系统错误：' + event.reason, 'error');
            event.preventDefault();
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseRealTimeUpdates();
            } else {
                this.resumeRealTimeUpdates();
            }
        });

        // 网络状态变化
        window.addEventListener('online', () => {
            this.showNotification('网络连接已恢复', 'success');
            this.resumeRealTimeUpdates();
        });

        window.addEventListener('offline', () => {
            this.showNotification('网络连接已断开', 'warning');
            this.pauseRealTimeUpdates();
        });
    }

    /**
     * 设置标签页导航
     */
    setupTabNavigation() {
        const navLinks = document.querySelectorAll('.nav-link[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = link.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 处理浏览器后退/前进
        window.addEventListener('popstate', (e) => {
            const tabId = e.state?.tab || 'dashboard';
            this.switchTab(tabId, false);
        });

        // 初始化URL状态
        const urlHash = window.location.hash.slice(1);
        if (urlHash && document.querySelector(`[data-tab="${urlHash}"]`)) {
            this.switchTab(urlHash, false);
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tabId, updateHistory = true) {
        if (this.currentTab === tabId) return;

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`)?.classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabId}-tab`)?.classList.add('active');

        // 更新URL
        if (updateHistory) {
            const url = new URL(window.location);
            url.hash = tabId;
            history.pushState({ tab: tabId }, '', url);
        }

        // 清理当前标签页的资源
        this.cleanupTab(this.currentTab);

        // 初始化新标签页
        this.initTab(tabId);

        this.currentTab = tabId;
    }

    /**
     * 初始化标签页
     */
    initTab(tabId) {
        switch (tabId) {
            case 'dashboard':
                if (window.dashboard) {
                    window.dashboard.init();
                }
                break;
            case 'deploy':
                if (window.deployManager) {
                    window.deployManager.init();
                }
                break;
            case 'templates':
                if (window.templateManager) {
                    window.templateManager.init();
                }
                break;
            case 'monitoring':
                if (window.monitoringManager) {
                    window.monitoringManager.init();
                }
                break;
            case 'logs':
                if (window.logManager) {
                    window.logManager.init();
                }
                break;
            case 'settings':
                if (window.settingsManager) {
                    window.settingsManager.init();
                }
                break;
        }
    }

    /**
     * 清理标签页资源
     */
    cleanupTab(tabId) {
        // 清理定时器
        const intervalKey = `${tabId}_refresh`;
        if (this.refreshIntervals.has(intervalKey)) {
            clearInterval(this.refreshIntervals.get(intervalKey));
            this.refreshIntervals.delete(intervalKey);
        }

        // 清理事件源
        const eventSourceKey = `${tabId}_events`;
        if (this.eventSources.has(eventSourceKey)) {
            this.eventSources.get(eventSourceKey).close();
            this.eventSources.delete(eventSourceKey);
        }
    }

    /**
     * 设置全局刷新
     */
    setupGlobalRefresh() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentTab();
            });
        }

        // 键盘快捷键 F5 或 Ctrl+R
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.refreshCurrentTab();
            }
        });
    }

    /**
     * 刷新当前标签页
     */
    refreshCurrentTab() {
        this.showLoading('刷新中...');
        
        try {
            this.initTab(this.currentTab);
            this.showNotification('页面已刷新', 'success');
        } catch (error) {
            console.error('Refresh failed:', error);
            this.showNotification('刷新失败：' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 设置通知系统
     */
    setupNotifications() {
        // 创建通知容器
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // 通知按钮点击
        const notificationBtn = document.getElementById('notifications-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.showNotificationPanel();
            });
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };

        this.notifications.unshift(notification);
        this.updateNotificationBadge();

        // 创建通知元素
        const notificationEl = this.createNotificationElement(notification);
        const container = document.getElementById('notification-container');
        container.appendChild(notificationEl);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, duration);
        }

        return notification.id;
    }

    /**
     * 创建通知元素
     */
    createNotificationElement(notification) {
        const el = document.createElement('div');
        el.className = `notification notification-${notification.type}`;
        el.dataset.id = notification.id;
        
        el.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${this.formatTime(notification.timestamp)}</div>
            </div>
            <button class="notification-close" onclick="app.removeNotification(${notification.id})">
                <i class="icon-close"></i>
            </button>
        `;

        // 添加动画
        setTimeout(() => el.classList.add('show'), 10);

        return el;
    }

    /**
     * 移除通知
     */
    removeNotification(id) {
        const el = document.querySelector(`[data-id="${id}"]`);
        if (el) {
            el.classList.add('hide');
            setTimeout(() => el.remove(), 300);
        }

        this.notifications = this.notifications.filter(n => n.id !== id);
        this.updateNotificationBadge();
    }

    /**
     * 更新通知徽章
     */
    updateNotificationBadge() {
        const badge = document.getElementById('notification-count');
        if (badge) {
            const unreadCount = this.notifications.length;
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }

    /**
     * 显示通知面板
     */
    showNotificationPanel() {
        // 实现通知面板显示逻辑
        console.log('Show notification panel');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            this.showLoading('加载系统数据...');

            // 并行加载基础数据
            const [systemStatus, queueStatus] = await Promise.all([
                api.getSystemStatus().catch(e => ({ data: null, error: e.message })),
                api.getQueueStatus().catch(e => ({ data: null, error: e.message }))
            ]);

            // 更新系统状态显示
            this.updateSystemStatus(systemStatus.data);
            this.updateQueueStatus(queueStatus.data);

        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.showNotification('加载系统数据失败：' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(status) {
        if (!status) return;

        // 更新系统负载
        const loadEl = document.getElementById('system-load');
        if (loadEl && status.load_average) {
            loadEl.textContent = status.load_average[0]?.toFixed(2) || '0.0';
        }

        // 更新磁盘使用
        const diskEl = document.getElementById('disk-usage');
        if (diskEl && status.disk_usage) {
            diskEl.textContent = Math.round(status.disk_usage) + '%';
        }
    }

    /**
     * 更新队列状态
     */
    updateQueueStatus(status) {
        if (!status) return;

        // 更新并发任务数
        const concurrentEl = document.getElementById('concurrent-jobs');
        if (concurrentEl) {
            concurrentEl.textContent = `${status.running_jobs || 0}/${status.max_concurrent || 3}`;
        }

        // 更新队列状态指示器
        const queueStatusEl = document.getElementById('queue-status');
        if (queueStatusEl) {
            const isRunning = !status.is_paused;
            queueStatusEl.textContent = isRunning ? '运行中' : '已暂停';
            queueStatusEl.className = `status-indicator ${isRunning ? 'status-running' : 'status-paused'}`;
        }
    }

    /**
     * 启动实时更新
     */
    startRealTimeUpdates() {
        // 监听系统状态
        const systemEventSource = api.watchSystemStatus(
            (data) => this.updateSystemStatus(data),
            (error) => console.error('System status update error:', error)
        );
        this.eventSources.set('system_status', systemEventSource);

        // 定期刷新数据
        const refreshInterval = setInterval(() => {
            if (!document.hidden) {
                this.refreshSystemData();
            }
        }, 30000); // 30秒刷新一次
        this.refreshIntervals.set('global_refresh', refreshInterval);
    }

    /**
     * 暂停实时更新
     */
    pauseRealTimeUpdates() {
        this.eventSources.forEach(source => source.close());
        this.refreshIntervals.forEach(interval => clearInterval(interval));
    }

    /**
     * 恢复实时更新
     */
    resumeRealTimeUpdates() {
        this.startRealTimeUpdates();
    }

    /**
     * 刷新系统数据
     */
    async refreshSystemData() {
        try {
            const [systemStatus, queueStatus] = await Promise.all([
                api.getSystemStatus(),
                api.getQueueStatus()
            ]);

            this.updateSystemStatus(systemStatus.data);
            this.updateQueueStatus(queueStatus.data);
        } catch (error) {
            console.error('Failed to refresh system data:', error);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.remove('hidden');
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    /**
     * 格式化时间
     */
    formatTime(date) {
        return new Intl.DateTimeFormat('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).format(date);
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).format(date);
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    /**
     * 格式化持续时间
     */
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new WPDeployManager();
});

// 导出应用类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WPDeployManager;
}
