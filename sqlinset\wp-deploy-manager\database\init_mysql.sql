-- WordPress 部署管理系统数据库初始化脚本 (MySQL版本)

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    mother_domain VARCHAR(255),
    mother_db VARCHAR(255),
    mysql_root_pass VARCHAR(255),
    config_data JSON,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署任务表
CREATE TABLE IF NOT EXISTS deploy_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    type ENUM('single', 'batch') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 5,
    template_id INT,
    domain VARCHAR(255),
    config_data JSON,
    progress INT DEFAULT 0,
    current_step VARCHAR(255),
    total_steps INT DEFAULT 0,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    log_file VARCHAR(500),
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 批量部署子任务表
CREATE TABLE IF NOT EXISTS deploy_sub_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_job_id INT NOT NULL,
    domain VARCHAR(255) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending',
    template_id INT,
    config_data JSON,
    progress INT DEFAULT 0,
    current_step VARCHAR(255),
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    log_file VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_job_id) REFERENCES deploy_jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署历史表
CREATE TABLE IF NOT EXISTS deploy_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    domain VARCHAR(255) NOT NULL,
    template_id INT,
    status VARCHAR(50) NOT NULL,
    duration INT,
    file_size BIGINT,
    db_size BIGINT,
    success_rate DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES deploy_jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    source VARCHAR(255),
    user_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 站点健康检查表
CREATE TABLE IF NOT EXISTS site_health (
    id INT AUTO_INCREMENT PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    status ENUM('healthy', 'warning', 'critical', 'unknown') NOT NULL,
    http_status INT,
    https_status INT,
    response_time DECIMAL(8,3),
    ssl_expiry_date TIMESTAMP NULL,
    disk_usage DECIMAL(5,2),
    db_status VARCHAR(50),
    wp_version VARCHAR(20),
    last_check_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    health_score INT DEFAULT 0,
    issues JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) UNIQUE NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_deploy_jobs_status ON deploy_jobs(status);
CREATE INDEX idx_deploy_jobs_type ON deploy_jobs(type);
CREATE INDEX idx_deploy_jobs_created_at ON deploy_jobs(created_at);
CREATE INDEX idx_deploy_sub_jobs_parent ON deploy_sub_jobs(parent_job_id);
CREATE INDEX idx_deploy_sub_jobs_status ON deploy_sub_jobs(status);
CREATE INDEX idx_deploy_history_domain ON deploy_history(domain);
CREATE INDEX idx_deploy_history_created_at ON deploy_history(created_at);
CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_site_health_domain ON site_health(domain);
CREATE INDEX idx_site_health_status ON site_health(status);
CREATE INDEX idx_templates_status ON templates(status);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 插入默认系统配置
INSERT IGNORE INTO system_config (config_key, config_value, description, type) VALUES
('system_name', 'WordPress部署管理系统', '系统名称', 'string'),
('max_concurrent_jobs', '3', '最大并发部署任务数', 'integer'),
('default_timeout', '1800', '默认超时时间（秒）', 'integer'),
('auto_cleanup_logs', 'true', '自动清理日志', 'boolean'),
('log_retention_days', '30', '日志保留天数', 'integer'),
('health_check_interval', '300', '健康检查间隔（秒）', 'integer'),
('enable_notifications', 'false', '启用通知', 'boolean'),
('notification_email', '', '通知邮箱', 'string'),
('backup_before_deploy', 'true', '部署前备份', 'boolean'),
('auto_ssl_setup', 'true', '自动SSL设置', 'boolean');

-- 创建视图：部署统计
CREATE OR REPLACE VIEW deploy_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_jobs,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_jobs,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_jobs,
    ROUND(AVG(progress), 2) as avg_progress
FROM deploy_jobs 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建视图：模板使用统计
CREATE OR REPLACE VIEW template_usage_stats AS
SELECT 
    t.id,
    t.name,
    t.uuid,
    COUNT(dj.id) as usage_count,
    SUM(CASE WHEN dj.status = 'completed' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN dj.status = 'failed' THEN 1 ELSE 0 END) as failure_count,
    ROUND(
        CASE 
            WHEN COUNT(dj.id) > 0 
            THEN (SUM(CASE WHEN dj.status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(dj.id))
            ELSE 0 
        END, 2
    ) as success_rate,
    MAX(dj.created_at) as last_used_at
FROM templates t
LEFT JOIN deploy_jobs dj ON t.id = dj.template_id
WHERE t.status = 'active'
GROUP BY t.id, t.name, t.uuid
ORDER BY usage_count DESC;

-- 创建视图：系统健康概览
CREATE OR REPLACE VIEW system_health_overview AS
SELECT 
    COUNT(*) as total_sites,
    SUM(CASE WHEN status = 'healthy' THEN 1 ELSE 0 END) as healthy_sites,
    SUM(CASE WHEN status = 'warning' THEN 1 ELSE 0 END) as warning_sites,
    SUM(CASE WHEN status = 'critical' THEN 1 ELSE 0 END) as critical_sites,
    SUM(CASE WHEN status = 'unknown' THEN 1 ELSE 0 END) as unknown_sites,
    ROUND(AVG(health_score), 2) as avg_health_score,
    ROUND(AVG(response_time), 3) as avg_response_time,
    MAX(last_check_at) as last_check_time
FROM site_health
WHERE last_check_at > DATE_SUB(NOW(), INTERVAL 1 DAY);
