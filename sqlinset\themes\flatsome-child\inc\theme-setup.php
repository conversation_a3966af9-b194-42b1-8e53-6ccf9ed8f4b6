<?php
/**
 * Theme setup, enqueue scripts/styles, custom shortcodes, and general hooks
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; 
}

function flatsome_child_setup() {

    do_action('flatsome_child_after_setup');
}
add_action('after_setup_theme', 'flatsome_child_setup');

function flatsome_child_load_textdomains() {
    load_theme_textdomain('flatsome', get_template_directory() . '/languages');
    load_child_theme_textdomain('flatsome-child', get_stylesheet_directory() . '/languages');
}
add_action('init', 'flatsome_child_load_textdomains');

function flatsome_child_enqueue_parent_style() {
    wp_enqueue_style('flatsome-parent', get_template_directory_uri() .'/style.css');
}
add_action('wp_enqueue_scripts', 'flatsome_child_enqueue_parent_style');

function flatsome_child_custom_scripts() {
    wp_enqueue_style('flatsome-child-style', 
        get_stylesheet_uri(),
        array('flatsome-parent'), // Depends on parent style
        wp_get_theme()->get('Version') // Cache busting
    );

    $custom_css = flatsome_child_get_inline_css();
    if (!empty($custom_css)) {
        wp_add_inline_style('flatsome-child-style', $custom_css);
    }
    
    do_action('flatsome_child_after_enqueue_scripts');
}
add_action('wp_enqueue_scripts', 'flatsome_child_custom_scripts', 20);

function flatsome_child_get_inline_css() {
    $css = apply_filters('flatsome_child_inline_css', "
        /* Product title two lines */
        .product-title,
        .name.product-title,
        .name.product-title a,
        .product-info .name.product-title a {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 2.8em; /* Adjust line-height * lines */
            line-height: 1.4em;
        }

        /* Button font size adjustments */
        .button,
        .button.is-outline,
        button,
        input[type='submit'],
        input[type='reset'],
        input[type='button'],
        .add-to-cart-button a,
        .single_add_to_cart_button,
        .checkout-button,
        .woocommerce-mini-cart__buttons a {
            font-size: 14px !important; /* Use !important sparingly */
            min-height: 40px;
            line-height: 40px;
            padding: 0 15px;
        }

        /* Specific button size adjustments */
        .checkout-button,
        .woocommerce-mini-cart__buttons .checkout {
            font-size: 16px !important;
        }

        /* Align product grid titles */
        .product-small .box-text {
            padding-bottom: 15px;
            height: auto; /* Allow dynamic height */
            display: flex;
            flex-direction: column;
        }
        
        .product-small .title-wrapper {
            flex-grow: 1; /* Allow title to take available space */
            min-height: 2.8em; /* Ensure space for two lines */
            margin-bottom: 5px;
        }
    ");
    
    $css = preg_replace('/\s+/', ' ', $css);
    $css = preg_replace('/\s*([{}|:;,])\s*/', '\1', $css);
    $css = str_replace(';}', '}', $css);

    return $css;
}

function flatsome_child_custom_shortcodes() {
    add_shortcode('custom_button', function($atts, $content = null) {
        $atts = shortcode_atts(array(
            'text'  => 'Button Text',
            'link'  => '#',
            'style' => 'primary', // e.g., primary, secondary, alert, success
            'class' => ''       // Allow adding custom classes
        ), $atts, 'custom_button');
        
        $link = esc_url($atts['link']);
        $text = esc_html($atts['text']);
        $style = sanitize_html_class($atts['style']);
        $class = sanitize_html_class($atts['class']);

        $button_html = sprintf(
            '<a href="%s" class="button %s %s">%s</a>',
            $link,
            esc_attr($style), // Use esc_attr for class attributes
            esc_attr($class),
            $text
        );
        
        return apply_filters('flatsome_child_custom_button_output', $button_html, $atts, $content);
    });
    
    do_action('flatsome_child_after_register_shortcodes');
}
add_action('init', 'flatsome_child_custom_shortcodes');

/**
 * Enqueue scripts for the Customizer controls panel.
 */
function flatsome_child_enqueue_customizer_controls_scripts() {
    $preset_styles = function_exists('flatsome_child_get_preset_styles') ? flatsome_child_get_preset_styles() : array();
    wp_enqueue_script(
        'flatsome-child-customizer-apply-preset',
        FLATSOME_CHILD_THEME_URI . '/assets/js/customizer-apply-preset.js',
        array( 'jquery', 'customize-controls' ),
        wp_get_theme()->get('Version'),
        true
    );
    wp_localize_script(
        'flatsome-child-customizer-apply-preset',
        'flatsomeChildPresets',
        $preset_styles
    );
    wp_localize_script(
        'flatsome-child-customizer-apply-preset',
        'flatsomeChildI18n',
        [
            'selectPreset' => __('Please select a preset from the dropdown first.', 'flatsome-child'),
            'errorPreset' => __('Error: Could not find data for the selected preset: ', 'flatsome-child'),
            'notFound' => __('Preset button placeholder control not found.', 'flatsome-child'),
            'notFoundKey' => __('Customizer setting not found for key: ', 'flatsome-child'),
        ]
    );
}
add_action( 'customize_controls_enqueue_scripts', 'flatsome_child_enqueue_customizer_controls_scripts' );

