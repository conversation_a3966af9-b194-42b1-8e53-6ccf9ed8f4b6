#!/bin/bash
# 全自动部署脚本（含数据库配置）
DOMAIN=$1
DB_NAME="wp_$(echo $DOMAIN | md5sum | cut -c1-8)"  # 生成唯一数据库名
DB_USER="user_$(openssl rand -hex 3)"
DB_PASS=$(openssl rand -base64 12)

# 创建数据库及用户
mysql -u root -p"$MYSQL_ROOT_PASSWORD" <<EOF
CREATE DATABASE $DB_NAME;
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

# 复制模板
TEMPLATE_DIR="/path/to/template"
TARGET_DIR="/var/www/$DOMAIN"
rsync -av --delete $TEMPLATE_DIR/ $TARGET_DIR/

# 生成动态CSS
cd $TARGET_DIR/wp-content/themes/your-theme/
php css-generator.php

# 配置wp-config.php
cd $TARGET_DIR
wp config create \
  --dbname="$DB_NAME" \
  --dbuser="$DB_USER" \
  --dbpass="$DB_PASS" \
  --dbhost="localhost" \
  --skip-check \
  --force

wp config set WP_HOME "https://$DOMAIN" 
wp config set WP_SITEURL "https://$DOMAIN"
wp core install \
  --url="https://$DOMAIN" \
  --title="$DOMAIN" \
  --admin_user="admin_$(openssl rand -hex 2)" \
  --admin_password="$(openssl rand -base64 8)" \
  --admin_email="admin@$DOMAIN"

# 清理缓存
wp cache flush