# WordPress 部署管理系统 - 部署指南

## 🚀 快速部署

### 环境要求

- **操作系统**: Linux (推荐 CentOS 7+ 或 Ubuntu 18+)
- **Web服务器**: Nginx
- **PHP版本**: 8.0+
- **数据库**: SQLite 3 (内置)
- **面板**: 宝塔面板 (推荐)

### 必需的PHP扩展

- pdo_sqlite
- json
- curl
- zip

### 一键安装

```bash
# 1. 下载项目到服务器
cd /www/wwwroot/
git clone <repository-url> wp-deploy-manager

# 2. 运行环境设置脚本
cd wp-deploy-manager/install/
chmod +x setup.sh
./setup.sh

# 3. 访问安装向导
# http://your-domain/install/
```

## 📝 详细安装步骤

### 步骤1: 环境准备

#### 1.1 检查PHP版本和扩展

```bash
# 检查PHP版本
php -v

# 检查必需扩展
php -m | grep -E "(pdo_sqlite|json|curl|zip)"
```

#### 1.2 安装缺失的扩展 (如果需要)

**CentOS/RHEL:**
```bash
yum install php-pdo php-json php-curl php-zip
```

**Ubuntu/Debian:**
```bash
apt-get install php-sqlite3 php-json php-curl php-zip
```

### 步骤2: 项目部署

#### 2.1 下载项目文件

```bash
# 方式1: Git克隆
cd /www/wwwroot/
git clone <repository-url> wp-deploy-manager

# 方式2: 手动上传
# 将项目文件上传到 /www/wwwroot/wp-deploy-manager/
```

#### 2.2 设置文件权限

```bash
cd /www/wwwroot/wp-deploy-manager/

# 设置基础权限
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 设置可执行权限
chmod +x scripts/*.sh
chmod +x install/setup.sh

# 设置写入权限
chmod 755 logs/ uploads/ templates/ database/ config/
```

#### 2.3 创建必要目录

```bash
mkdir -p logs uploads/temp templates/uploads templates/configs database
```

### 步骤3: Web服务器配置

#### 3.1 Nginx配置

创建虚拟主机配置文件 `/www/server/panel/vhost/nginx/deploy.example.com.conf`:

```nginx
server {
    listen 80;
    server_name deploy.example.com;
    index index.html index.htm index.php;
    root /www/wwwroot/wp-deploy-manager/public;
    
    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 禁止访问敏感目录
    location ~ ^/(config|database|logs|scripts|install)/ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/tmp/php-cgi-80.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
    }
    
    # 静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 默认路由
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

#### 3.2 重载Nginx配置

```bash
nginx -t && nginx -s reload
```

### 步骤4: 系统配置

#### 4.1 访问安装向导

打开浏览器访问: `http://your-domain/install/`

按照向导完成以下配置:

1. **环境检查**: 确认所有要求都满足
2. **系统配置**: 设置MySQL密码、并发数等
3. **数据库初始化**: 自动创建数据库表
4. **安装完成**: 获取访问信息

#### 4.2 手动配置 (可选)

如果无法使用安装向导，可以手动配置:

```bash
# 1. 创建配置文件
cp config/config.example.php config/config.php

# 2. 编辑配置
vim config/config.php

# 3. 初始化数据库
php -r "
require_once 'config/config.php';
\$db = new PDO('sqlite:' . DB_FILE);
\$sql = file_get_contents('database/init.sql');
\$db->exec(\$sql);
echo 'Database initialized successfully';
"
```

### 步骤5: 启动服务

#### 5.1 创建队列处理服务

```bash
# 创建systemd服务文件
cat > /etc/systemd/system/wp-deploy-queue.service << 'EOF'
[Unit]
Description=WordPress Deploy Manager Queue Processor
After=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/wp-deploy-manager
ExecStart=/usr/bin/php scripts/queue_processor.php
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 重载systemd配置
systemctl daemon-reload

# 启用并启动服务
systemctl enable wp-deploy-queue
systemctl start wp-deploy-queue

# 检查服务状态
systemctl status wp-deploy-queue
```

#### 5.2 设置定时任务 (可选)

```bash
# 添加crontab任务
crontab -e

# 添加以下行
# 每5分钟检查队列状态
*/5 * * * * /usr/bin/php /www/wwwroot/wp-deploy-manager/scripts/queue_monitor.php

# 每天凌晨清理日志
0 2 * * * /usr/bin/php /www/wwwroot/wp-deploy-manager/scripts/cleanup_logs.php
```

## 🔧 配置说明

### 主要配置文件

#### config/config.php
```php
// 数据库配置
define('DB_FILE', DATABASE_PATH . '/deploy.db');

// 上传配置
define('MAX_UPLOAD_SIZE', 500 * 1024 * 1024); // 500MB
define('ALLOWED_EXTENSIONS', ['tar.gz', 'tgz', 'zip']);

// 部署配置
define('MAX_CONCURRENT_DEPLOYMENTS', 3);
define('DEPLOYMENT_TIMEOUT', 1800); // 30分钟

// 原始部署脚本路径
define('ORIGINAL_DEPLOY_SCRIPT', '/path/to/original/script.sh');
```

#### config/app.json
```json
{
    "mysql_root_password": "your_mysql_password",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "",
    "backup_before_deploy": true,
    "auto_ssl_setup": true
}
```

## 🔍 验证安装

### 检查系统状态

```bash
# 1. 检查文件权限
ls -la /www/wwwroot/wp-deploy-manager/

# 2. 检查数据库
sqlite3 /www/wwwroot/wp-deploy-manager/database/deploy.db ".tables"

# 3. 检查服务状态
systemctl status wp-deploy-queue

# 4. 检查日志
tail -f /www/wwwroot/wp-deploy-manager/logs/system_$(date +%Y-%m-%d).log
```

### 测试功能

1. **访问主界面**: `http://your-domain/`
2. **上传测试模板**: 使用模板管理功能
3. **创建测试部署**: 部署一个测试域名
4. **查看部署日志**: 确认部署过程正常

## 🛠️ 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 重新设置权限
chown -R www:www /www/wwwroot/wp-deploy-manager/
chmod -R 755 /www/wwwroot/wp-deploy-manager/
```

#### 2. PHP扩展缺失
```bash
# 检查扩展
php -m | grep sqlite

# 安装扩展 (CentOS)
yum install php-sqlite3

# 重启PHP-FPM
systemctl restart php-fpm
```

#### 3. 数据库权限
```bash
# 检查数据库文件权限
ls -la /www/wwwroot/wp-deploy-manager/database/

# 修复权限
chown www:www /www/wwwroot/wp-deploy-manager/database/deploy.db
chmod 644 /www/wwwroot/wp-deploy-manager/database/deploy.db
```

#### 4. 队列服务问题
```bash
# 查看服务日志
journalctl -u wp-deploy-queue -f

# 重启服务
systemctl restart wp-deploy-queue

# 手动运行队列处理器
sudo -u www php /www/wwwroot/wp-deploy-manager/scripts/queue_processor.php
```

### 日志位置

- **系统日志**: `/www/wwwroot/wp-deploy-manager/logs/system_YYYY-MM-DD.log`
- **部署日志**: `/www/wwwroot/wp-deploy-manager/logs/job_*.log`
- **Nginx日志**: `/www/wwwroot/wp-deploy-manager/logs/nginx_*.log`
- **队列服务日志**: `journalctl -u wp-deploy-queue`

## 🔄 升级指南

### 备份数据

```bash
# 备份数据库
cp /www/wwwroot/wp-deploy-manager/database/deploy.db /backup/

# 备份配置
cp -r /www/wwwroot/wp-deploy-manager/config/ /backup/

# 备份模板
cp -r /www/wwwroot/wp-deploy-manager/templates/ /backup/
```

### 更新代码

```bash
# 停止服务
systemctl stop wp-deploy-queue

# 备份当前版本
mv /www/wwwroot/wp-deploy-manager /www/wwwroot/wp-deploy-manager.backup

# 部署新版本
# ... 按照安装步骤重新部署

# 恢复数据
cp /backup/deploy.db /www/wwwroot/wp-deploy-manager/database/
cp -r /backup/config/* /www/wwwroot/wp-deploy-manager/config/
cp -r /backup/templates/* /www/wwwroot/wp-deploy-manager/templates/

# 启动服务
systemctl start wp-deploy-queue
```

## 📞 技术支持

如果在部署过程中遇到问题，请:

1. 检查系统日志
2. 确认环境要求
3. 查看故障排除部分
4. 联系技术支持

---

**注意**: 请确保在生产环境中使用HTTPS，并定期备份数据库和配置文件。
