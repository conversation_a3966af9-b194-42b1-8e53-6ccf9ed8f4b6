2025/07/26 06:19:37 [error] 1221217#0: *2396836 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/logo.png HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:38 [error] 1221218#0: *2396840 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/templates.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:38 [error] 1221218#0: *2396842 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/components.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:38 [error] 1221218#0: *2396844 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/monitoring.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:38 [error] 1221217#0: *2396846 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/settings.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:38 [error] 1221218#0: *2396847 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/logs.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396834 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396848 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***********, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396849 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***********, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396850 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396851 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396853 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/assets/images/favicon.ico＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396853 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:39 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:48 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=7 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:49 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:51 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:51 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:19:59 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:01 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:09 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:09 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:09 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:09 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:09 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:11 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:19 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:21 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:20:22 [error] 1221218#0: *2396858 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/favicon.ico＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:22 [error] 1221218#0: *2396858 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:22 [error] 1221218#0: *2396859 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/favicon.ico＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:22 [error] 1221218#0: *2396859 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:23 [error] 1221218#0: *2396860 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/favicon.ico＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:23 [error] 1221218#0: *2396860 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396868 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/@vite/env＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /@vite/env HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396868 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /@vite/env HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396870 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/actuator/env＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /actuator/env HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396870 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /actuator/env HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396871 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/server＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /server HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:25 [error] 1221218#0: *2396871 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /server HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396872 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/.vscode/sftp.json＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.vscode/sftp.json HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396872 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.vscode/sftp.json HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396873 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/about＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /about HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396873 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /about HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396874 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/debug/default/view＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /debug/default/view?panel=config HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396874 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /debug/default/view?panel=config HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396875 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/v2/_catalog＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /v2/_catalog HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:26 [error] 1221218#0: *2396875 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /v2/_catalog HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396876 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396876 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396877 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/server-status＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /server-status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396877 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /server-status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396878 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/login.action＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /login.action HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396878 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /login.action HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396879 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/_all_dbs＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /_all_dbs HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:27 [error] 1221218#0: *2396879 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /_all_dbs HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:28 [error] 1221218#0: *2396880 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/.DS_Store＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.DS_Store HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:28 [error] 1221218#0: *2396880 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.DS_Store HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:28 [error] 1221218#0: *2396881 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.env HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:28 [error] 1221218#0: *2396882 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.git/config HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396883 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/s/33e27393e2431313e2838313/_/;/META-INF/maven/com.atlassian.jira/jira-webapp-dist/pom.properties＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /s/33e27393e2431313e2838313/_/;/META-INF/maven/com.atlassian.jira/jira-webapp-dist/pom.properties HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396883 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /s/33e27393e2431313e2838313/_/;/META-INF/maven/com.atlassian.jira/jira-webapp-dist/pom.properties HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396884 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/config.json＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /config.json HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396884 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /config.json HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396885 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/telescope/requests＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /telescope/requests HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396885 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /telescope/requests HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:29 [error] 1221218#0: *2396886 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /info.php HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:32 [error] 1221218#0: *2396893 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /.git/config HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:34 [error] 1221218#0: *2396895 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/favicon.ico＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:34 [error] 1221218#0: *2396895 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:35 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#dashboard＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23dashboard HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:35 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23dashboard HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:35 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#logs＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23logs HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:35 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23logs HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:36 [error] 1221218#0: *2396897 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/components.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:36 [error] 1221218#0: *2396898 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/monitoring.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:36 [error] 1221218#0: *2396899 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#templates＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23templates HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:36 [error] 1221218#0: *2396899 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23templates HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396900 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#monitoring＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23monitoring HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396900 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23monitoring HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#deploy＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23deploy HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396890 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /%23deploy HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396900 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/#settings＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23settings HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396900 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /%23settings HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396905 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/templates.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396906 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/logs.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:20:37 [error] 1221218#0: *2396907 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/settings.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:21:38 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:38 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:38 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:39 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:39 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:41 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:51 [error] 1221218#0: *2396913 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/templates.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:51 [error] 1221217#0: *2396916 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/monitoring.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:51 [error] 1221218#0: *2396915 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/logo.png HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:51 [error] 1221218#0: *2396919 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/components.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396922 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/settings.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396923 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/logs.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396911 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396925 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/assets/images/favicon.ico＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396925 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/favicon.ico HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:21:52 [error] 1221218#0: *2396926 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ***************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:22:15 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:22:18 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:22:19 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:22:23 [error] 1221218#0: *2396927 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /.git/config HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂
2025/07/26 06:23:10 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:11 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221217#0: *2396932 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/images/logo.png HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221217#0: *2396936 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/components.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396937 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/templates.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221217#0: *2396941 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/settings.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396940 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/monitoring.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396943 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /assets/js/logs.js HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:13 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:23 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:25 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:33 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:43 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:43 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:43 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:43 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:43 [error] 1221218#0: *2396930 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:44 [error] 1221218#0: *2396852 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: **************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:23:51 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:01 [error] 1221218#0: *2396947 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: ************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:04 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:04 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&limit=10 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:04 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:04 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_history&page=1&limit=20 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:05 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=system HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:05 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/deploy.php?action=get_deploy_stats&days=30 HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:05 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/wp-deploy-manager/api/status.php?action=queue HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂
2025/07/26 06:24:05 [error] 1221218#0: *2396949 open() ＂/www/wwwroot/wpd.cloudcheckout.shop/public/404.html＂ failed (2: No such file or directory), client: *************, server: wpd.cloudcheckout.shop, request: ＂GET /wp-deploy-manager/api/sse.php?action=system_status HTTP/2.0＂, host: ＂wpd.cloudcheckout.shop＂, referrer: ＂https://wpd.cloudcheckout.shop/＂