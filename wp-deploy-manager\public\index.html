<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/images/logo.png" alt="Logo" class="nav-logo">
                <span class="nav-title">WordPress 部署管理系统</span>
            </div>
            
            <div class="nav-menu">
                <div class="nav-item active" data-tab="dashboard">
                    <i class="icon-dashboard"></i>
                    <span>仪表板</span>
                </div>
                <div class="nav-item" data-tab="deploy">
                    <i class="icon-deploy"></i>
                    <span>部署管理</span>
                </div>
                <div class="nav-item" data-tab="templates">
                    <i class="icon-template"></i>
                    <span>模板管理</span>
                </div>
                <div class="nav-item" data-tab="monitoring">
                    <i class="icon-monitor"></i>
                    <span>监控中心</span>
                </div>
                <div class="nav-item" data-tab="logs">
                    <i class="icon-logs"></i>
                    <span>日志管理</span>
                </div>
                <div class="nav-item" data-tab="settings">
                    <i class="icon-settings"></i>
                    <span>系统设置</span>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="btn btn-icon" id="refresh-btn" title="刷新">
                    <i class="icon-refresh"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 仪表板 -->
        <section id="dashboard-tab" class="tab-content active">
            <div class="page-header">
                <h1>系统仪表板</h1>
                <p>WordPress部署管理系统概览</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-sites">0</div>
                        <div class="stat-label">总站点数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number" id="successful-deploys">0</div>
                        <div class="stat-label">成功部署</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pending-tasks">0</div>
                        <div class="stat-label">待处理任务</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-number" id="active-templates">2</div>
                        <div class="stat-label">活跃模板</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3>系统状态</h3>
                    <div id="system-status">
                        <div class="status-item">
                            <span class="status-label">服务状态</span>
                            <span class="status-value status-running">运行中</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">数据库</span>
                            <span class="status-value status-connected">已连接</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">API服务</span>
                            <span class="status-value status-running">正常</span>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3>最近活动</h3>
                    <div id="recent-activity">
                        <div class="activity-item">
                            <span class="activity-time">刚刚</span>
                            <span class="activity-text">系统启动完成</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-time">1分钟前</span>
                            <span class="activity-text">数据库连接成功</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-time">2分钟前</span>
                            <span class="activity-text">Docker容器启动</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 部署管理 -->
        <section id="deploy-tab" class="tab-content">
            <div class="page-header">
                <h1>部署管理</h1>
                <p>管理WordPress站点部署</p>
            </div>
            
            <div class="deploy-actions">
                <button class="btn btn-primary" id="single-deploy-btn">单域名部署</button>
                <button class="btn btn-secondary" id="batch-deploy-btn">批量部署</button>
            </div>
            
            <div class="deploy-history">
                <h3>部署历史</h3>
                <div id="deploy-history-list">
                    <div class="deploy-item">
                        <div class="deploy-info">
                            <strong>example.com</strong>
                            <span class="deploy-status status-completed">已完成</span>
                        </div>
                        <div class="deploy-time">2024-07-26 10:30:00</div>
                    </div>
                    <div class="deploy-item">
                        <div class="deploy-info">
                            <strong>test.com</strong>
                            <span class="deploy-status status-pending">待处理</span>
                        </div>
                        <div class="deploy-time">2024-07-26 10:25:00</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 模板管理 -->
        <section id="templates-tab" class="tab-content">
            <div class="page-header">
                <h1>模板管理</h1>
                <p>管理WordPress模板文件</p>
            </div>
            
            <div class="template-actions">
                <button class="btn btn-primary" id="upload-template-btn">上传模板</button>
            </div>
            
            <div class="templates-list">
                <div id="templates-container">
                    <div class="template-item">
                        <div class="template-info">
                            <h4>测试模板1</h4>
                            <p>这是一个测试模板</p>
                            <small>文件大小: 1.0 MB</small>
                        </div>
                        <div class="template-actions">
                            <button class="btn btn-secondary">编辑</button>
                            <button class="btn btn-danger">删除</button>
                        </div>
                    </div>
                    <div class="template-item">
                        <div class="template-info">
                            <h4>测试模板2</h4>
                            <p>这是另一个测试模板</p>
                            <small>文件大小: 2.0 MB</small>
                        </div>
                        <div class="template-actions">
                            <button class="btn btn-secondary">编辑</button>
                            <button class="btn btn-danger">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 监控中心 -->
        <section id="monitoring-tab" class="tab-content">
            <div class="page-header">
                <h1>监控中心</h1>
                <p>系统性能和健康状态监控</p>
            </div>
            
            <div class="monitoring-grid">
                <div class="monitor-card">
                    <h3>系统资源</h3>
                    <div id="system-resources">
                        <div class="resource-item">
                            <span>CPU使用率</span>
                            <span id="cpu-usage">15%</span>
                        </div>
                        <div class="resource-item">
                            <span>内存使用率</span>
                            <span id="memory-usage">45%</span>
                        </div>
                        <div class="resource-item">
                            <span>磁盘使用率</span>
                            <span id="disk-usage">60%</span>
                        </div>
                    </div>
                </div>
                
                <div class="monitor-card">
                    <h3>网络状态</h3>
                    <div id="network-status">
                        <div class="resource-item">
                            <span>入站流量</span>
                            <span>1.2 MB/s</span>
                        </div>
                        <div class="resource-item">
                            <span>出站流量</span>
                            <span>0.8 MB/s</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 日志管理 -->
        <section id="logs-tab" class="tab-content">
            <div class="page-header">
                <h1>日志管理</h1>
                <p>查看系统日志和错误信息</p>
            </div>
            
            <div class="logs-container">
                <div id="logs-content">
                    <div class="log-item log-info">
                        <span class="log-time">2024-07-26 10:30:15</span>
                        <span class="log-level">[INFO]</span>
                        <span class="log-message">系统启动完成</span>
                    </div>
                    <div class="log-item log-info">
                        <span class="log-time">2024-07-26 10:30:10</span>
                        <span class="log-level">[INFO]</span>
                        <span class="log-message">数据库连接成功</span>
                    </div>
                    <div class="log-item log-debug">
                        <span class="log-time">2024-07-26 10:30:05</span>
                        <span class="log-level">[DEBUG]</span>
                        <span class="log-message">加载配置文件</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统设置 -->
        <section id="settings-tab" class="tab-content">
            <div class="page-header">
                <h1>系统设置</h1>
                <p>配置系统参数和选项</p>
            </div>
            
            <div class="settings-form">
                <div class="form-group">
                    <label>最大并发任务数</label>
                    <input type="number" id="max-concurrent-jobs" value="3" min="1" max="10">
                </div>
                <div class="form-group">
                    <label>默认超时时间（秒）</label>
                    <input type="number" id="default-timeout" value="1800" min="300" max="3600">
                </div>
                <div class="form-group">
                    <label>启用通知</label>
                    <select id="enable-notifications">
                        <option value="false">禁用</option>
                        <option value="true">启用</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" id="save-settings-btn">保存设置</button>
                    <button class="btn btn-secondary" id="reset-settings-btn">重置设置</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/api.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
