#!/bin/bash

# 创建JavaScript文件脚本

echo "📜 创建JavaScript文件..."

# 创建API.js文件
cat > public/assets/js/api.js << 'EOF'
/**
 * API工具类
 * 处理与后端API的通信
 */

class API {
    constructor() {
        this.baseUrl = '/api';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 发送HTTP请求
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            ...options
        };

        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'API request failed');
            }

            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: data instanceof FormData ? data : JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // ==================== 模板相关API ====================

    /**
     * 获取模板列表
     */
    async getTemplates(filters = {}) {
        return this.get('/templates.php?action=list', filters);
    }

    /**
     * 上传模板
     */
    async uploadTemplate(formData) {
        return this.post('/templates.php?action=upload', formData);
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId) {
        return this.delete(`/templates.php?action=delete&id=${templateId}`);
    }

    // ==================== 部署相关API ====================

    /**
     * 单域名部署
     */
    async singleDeploy(domain, templateId, config = {}) {
        return this.post('/deploy.php?action=single_deploy', {
            domain,
            template_id: templateId,
            config
        });
    }

    /**
     * 批量部署
     */
    async batchDeploy(domains, templateId, config = {}) {
        return this.post('/deploy.php?action=batch_deploy', {
            domains,
            template_id: templateId,
            config
        });
    }

    /**
     * 获取部署历史
     */
    async getDeployHistory(filters = {}) {
        return this.get('/deploy.php?action=get_deploy_history', filters);
    }

    /**
     * 获取部署统计
     */
    async getDeployStats(days = 30) {
        return this.get('/deploy.php?action=get_deploy_stats', { days });
    }

    // ==================== 系统状态API ====================

    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        return this.get('/status.php?action=system');
    }

    /**
     * 获取队列状态
     */
    async getQueueStatus() {
        return this.get('/status.php?action=queue');
    }

    // ==================== 设置相关API ====================

    /**
     * 获取系统设置
     */
    async getSettings() {
        return this.get('/settings.php?action=get');
    }

    /**
     * 保存系统设置
     */
    async saveSettings(settings) {
        return this.post('/settings.php?action=save', settings);
    }

    // ==================== 日志相关API ====================

    /**
     * 获取日志
     */
    async getLogs(filters = {}) {
        return this.get('/logs.php?action=get_logs', filters);
    }
}

// 创建全局API实例
window.api = new API();

// 导出API类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
EOF

echo "✅ api.js 创建完成"

# 创建main.js文件
cat > public/assets/js/main.js << 'EOF'
/**
 * WordPress Deploy Manager - 主应用脚本
 */

class WPDeployManager {
    constructor() {
        this.currentTab = 'dashboard';
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        console.log('WordPress Deploy Manager initialized');
        
        // 绑定事件
        this.bindEvents();
        
        // 加载初始数据
        this.loadInitialData();
        
        // 设置定时刷新
        this.setupAutoRefresh();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentTab();
            });
        }

        // 设置保存按钮
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // 部署按钮
        const singleDeployBtn = document.getElementById('single-deploy-btn');
        if (singleDeployBtn) {
            singleDeployBtn.addEventListener('click', () => {
                this.showSingleDeployDialog();
            });
        }

        const batchDeployBtn = document.getElementById('batch-deploy-btn');
        if (batchDeployBtn) {
            batchDeployBtn.addEventListener('click', () => {
                this.showBatchDeployDialog();
            });
        }

        // 模板上传按钮
        const uploadTemplateBtn = document.getElementById('upload-template-btn');
        if (uploadTemplateBtn) {
            uploadTemplateBtn.addEventListener('click', () => {
                this.showUploadTemplateDialog();
            });
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // 加载对应数据
        this.loadTabData(tabName);
    }

    /**
     * 加载标签页数据
     */
    async loadTabData(tabName) {
        try {
            switch (tabName) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'deploy':
                    await this.loadDeployData();
                    break;
                case 'templates':
                    await this.loadTemplatesData();
                    break;
                case 'monitoring':
                    await this.loadMonitoringData();
                    break;
                case 'logs':
                    await this.loadLogsData();
                    break;
                case 'settings':
                    await this.loadSettingsData();
                    break;
            }
        } catch (error) {
            console.error(`Failed to load ${tabName} data:`, error);
            this.showNotification(`加载${tabName}数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 加载仪表板数据
     */
    async loadDashboardData() {
        try {
            // 加载统计数据
            const stats = await window.api.getDeployStats();
            this.updateDashboardStats(stats.data || {});

            // 加载系统状态
            const systemStatus = await window.api.getSystemStatus();
            this.updateSystemStatus(systemStatus.data || {});

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            // 显示默认数据
            this.updateDashboardStats({});
            this.updateSystemStatus({});
        }
    }

    /**
     * 更新仪表板统计
     */
    updateDashboardStats(stats) {
        document.getElementById('total-sites').textContent = stats.total_sites || 0;
        document.getElementById('successful-deploys').textContent = stats.successful_deploys || 0;
        document.getElementById('pending-tasks').textContent = stats.pending_tasks || 0;
        document.getElementById('active-templates').textContent = stats.active_templates || 0;
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(status) {
        // 这里可以根据实际状态更新UI
        console.log('System status updated:', status);
    }

    /**
     * 加载部署数据
     */
    async loadDeployData() {
        try {
            const history = await window.api.getDeployHistory({ limit: 10 });
            this.updateDeployHistory(history.data || []);
        } catch (error) {
            console.error('Failed to load deploy data:', error);
            document.getElementById('deploy-history-list').innerHTML = '<p>加载部署历史失败</p>';
        }
    }

    /**
     * 更新部署历史
     */
    updateDeployHistory(history) {
        const container = document.getElementById('deploy-history-list');
        if (history.length === 0) {
            container.innerHTML = '<p>暂无部署记录</p>';
            return;
        }

        const html = history.map(item => `
            <div class="deploy-item">
                <div class="deploy-info">
                    <strong>${item.domain || 'Unknown'}</strong>
                    <span class="deploy-status status-${item.status}">${item.status}</span>
                </div>
                <div class="deploy-time">${item.created_at}</div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 加载模板数据
     */
    async loadTemplatesData() {
        try {
            const templates = await window.api.getTemplates();
            this.updateTemplatesList(templates.data || []);
        } catch (error) {
            console.error('Failed to load templates:', error);
            document.getElementById('templates-container').innerHTML = '<p>加载模板失败</p>';
        }
    }

    /**
     * 更新模板列表
     */
    updateTemplatesList(templates) {
        const container = document.getElementById('templates-container');
        if (templates.length === 0) {
            container.innerHTML = '<p>暂无模板</p>';
            return;
        }

        const html = templates.map(template => `
            <div class="template-item">
                <div class="template-info">
                    <h4>${template.name}</h4>
                    <p>${template.description || '无描述'}</p>
                    <small>文件大小: ${this.formatFileSize(template.file_size)}</small>
                </div>
                <div class="template-actions">
                    <button class="btn btn-secondary" onclick="app.deleteTemplate('${template.id}')">删除</button>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 加载监控数据
     */
    async loadMonitoringData() {
        try {
            const status = await window.api.getSystemStatus();
            this.updateMonitoringData(status.data || {});
        } catch (error) {
            console.error('Failed to load monitoring data:', error);
            document.getElementById('cpu-usage').textContent = 'N/A';
            document.getElementById('memory-usage').textContent = 'N/A';
        }
    }

    /**
     * 更新监控数据
     */
    updateMonitoringData(data) {
        document.getElementById('cpu-usage').textContent = data.cpu_usage || '0%';
        document.getElementById('memory-usage').textContent = data.memory_usage || '0%';
    }

    /**
     * 加载日志数据
     */
    async loadLogsData() {
        try {
            const logs = await window.api.getLogs({ limit: 50 });
            this.updateLogsDisplay(logs.data || []);
        } catch (error) {
            console.error('Failed to load logs:', error);
            document.getElementById('logs-content').innerHTML = '<p>加载日志失败</p>';
        }
    }

    /**
     * 更新日志显示
     */
    updateLogsDisplay(logs) {
        const container = document.getElementById('logs-content');
        if (logs.length === 0) {
            container.innerHTML = '<p>暂无日志</p>';
            return;
        }

        const html = logs.map(log => `
            <div class="log-item log-${log.level.toLowerCase()}">
                <span class="log-time">${log.created_at}</span>
                <span class="log-level">[${log.level}]</span>
                <span class="log-message">${log.message}</span>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 加载设置数据
     */
    async loadSettingsData() {
        try {
            const settings = await window.api.getSettings();
            this.updateSettingsForm(settings.data || {});
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    /**
     * 更新设置表单
     */
    updateSettingsForm(settings) {
        const maxJobsInput = document.getElementById('max-concurrent-jobs');
        const timeoutInput = document.getElementById('default-timeout');

        if (maxJobsInput) maxJobsInput.value = settings.max_concurrent_jobs || 3;
        if (timeoutInput) timeoutInput.value = settings.default_timeout || 1800;
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            const settings = {
                max_concurrent_jobs: document.getElementById('max-concurrent-jobs').value,
                default_timeout: document.getElementById('default-timeout').value
            };

            await window.api.saveSettings(settings);
            this.showNotification('设置保存成功', 'success');
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('设置保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        await this.loadDashboardData();
    }

    /**
     * 刷新当前标签页
     */
    refreshCurrentTab() {
        this.loadTabData(this.currentTab);
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒刷新一次仪表板数据
        setInterval(() => {
            if (this.currentTab === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        // 这里可以实现更复杂的通知UI
        alert(message);
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 显示单域名部署对话框
     */
    showSingleDeployDialog() {
        const domain = prompt('请输入域名:');
        if (domain) {
            this.showNotification(`单域名部署功能开发中: ${domain}`, 'info');
        }
    }

    /**
     * 显示批量部署对话框
     */
    showBatchDeployDialog() {
        this.showNotification('批量部署功能开发中', 'info');
    }

    /**
     * 显示模板上传对话框
     */
    showUploadTemplateDialog() {
        this.showNotification('模板上传功能开发中', 'info');
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId) {
        if (confirm('确定要删除这个模板吗？')) {
            try {
                await window.api.deleteTemplate(templateId);
                this.showNotification('模板删除成功', 'success');
                this.loadTemplatesData();
            } catch (error) {
                this.showNotification('模板删除失败: ' + error.message, 'error');
            }
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new WPDeployManager();
});

// 导出应用类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WPDeployManager;
}
EOF

echo "✅ main.js 创建完成"

echo "📜 JavaScript文件创建完成！"
