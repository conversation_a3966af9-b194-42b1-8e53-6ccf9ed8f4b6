@echo off
echo 🚀 WordPress Deploy Manager 快速安装
echo ==================================================

REM 创建目录结构
echo 📁 创建目录结构...
mkdir logs uploads uploads\temp uploads\templates 2>nul

REM 创建Docker Compose文件
echo 🐳 创建Docker配置...
(
echo version: '3.8'
echo.
echo services:
echo   web:
echo     image: php:8.1-apache
echo     container_name: wp-deploy-web
echo     ports:
echo       - "80:80"
echo     volumes:
echo       - ./public:/var/www/html
echo     depends_on:
echo       - mysql
echo     environment:
echo       - APACHE_DOCUMENT_ROOT=/var/www/html
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo     command: >
echo       bash -c "
echo       apt-get update ^&^& apt-get install -y libzip-dev ^&^&
echo       docker-php-ext-install pdo_mysql zip ^&^&
echo       a2enmod rewrite ^&^&
echo       apache2-foreground"
echo.
echo   mysql:
echo     image: mysql:8.0
echo     container_name: wp-deploy-mysql
echo     ports:
echo       - "3306:3306"
echo     environment:
echo       MYSQL_ROOT_PASSWORD: wp_deploy_2024
echo       MYSQL_DATABASE: wp_deploy_manager
echo       MYSQL_USER: wp_deploy
echo       MYSQL_PASSWORD: wp_deploy_pass_2024
echo     volumes:
echo       - mysql_data:/var/lib/mysql
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo.
echo   phpmyadmin:
echo     image: phpmyadmin/phpmyadmin:latest
echo     container_name: wp-deploy-phpmyadmin
echo     ports:
echo       - "8080:80"
echo     environment:
echo       PMA_HOST: mysql
echo       PMA_USER: root
echo       PMA_PASSWORD: wp_deploy_2024
echo     depends_on:
echo       - mysql
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo.
echo volumes:
echo   mysql_data:
echo.
echo networks:
echo   wp-deploy-network:
echo     driver: bridge
) > docker-compose.yml

REM 创建简单的HTML页面
echo 🎨 创建前端页面...
mkdir public 2>nul
(
echo ^<!DOCTYPE html^>
echo ^<html lang="zh-CN"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>WordPress 部署管理系统^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
echo         .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1^); }
echo         h1 { color: #333; text-align: center; margin-bottom: 30px; }
echo         .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
echo         .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
echo         .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
echo         .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr^)^); gap: 20px; margin-top: 30px; }
echo         .link-card { padding: 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s; }
echo         .link-card:hover { background: #0056b3; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<h1^>🚀 WordPress 部署管理系统^</h1^>
echo         ^<div class="status success"^>
echo             ✅ 系统已成功启动！欢迎使用WordPress Deploy Manager
echo         ^</div^>
echo         ^<div class="status info"^>
echo             📊 这是一个用于批量部署和管理WordPress站点的系统
echo         ^</div^>
echo         ^<div class="links"^>
echo             ^<a href="/api/" class="link-card"^>
echo                 🔌 API接口^<br^>^<small^>查看API文档^</small^>
echo             ^</a^>
echo             ^<a href="http://localhost:8080" class="link-card" target="_blank"^>
echo                 🗄️ phpMyAdmin^<br^>^<small^>数据库管理^</small^>
echo             ^</a^>
echo         ^</div^>
echo         ^<div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;"^>
echo             ^<h3^>🔑 数据库信息^</h3^>
echo             ^<p^>^<strong^>主机:^</strong^> localhost:3306^</p^>
echo             ^<p^>^<strong^>数据库:^</strong^> wp_deploy_manager^</p^>
echo             ^<p^>^<strong^>用户:^</strong^> wp_deploy^</p^>
echo             ^<p^>^<strong^>密码:^</strong^> wp_deploy_pass_2024^</p^>
echo             ^<p^>^<strong^>Root密码:^</strong^> wp_deploy_2024^</p^>
echo         ^</div^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) > public\index.html

REM 创建简单的API
mkdir public\api 2>nul
(
echo ^<?php
echo header('Content-Type: application/json'^);
echo echo json_encode([
echo     'success' =^> true,
echo     'message' =^> 'WordPress Deploy Manager API',
echo     'version' =^> '1.0.0',
echo     'timestamp' =^> date('Y-m-d H:i:s'^),
echo     'endpoints' =^> [
echo         'status' =^> '/api/status.php',
echo         'deploy' =^> '/api/deploy.php',
echo         'templates' =^> '/api/templates.php'
echo     ]
echo ], JSON_PRETTY_PRINT^);
echo ?^>
) > public\api\index.php

REM 启动Docker服务
echo 🚀 启动Docker服务...
docker-compose up -d

echo.
echo ✅ 安装完成！
echo ==================================================
echo.
echo 🌐 访问地址:
echo    主应用:      http://localhost
echo    phpMyAdmin:  http://localhost:8080
echo    API接口:     http://localhost/api/
echo.
echo 🔑 数据库信息:
echo    主机: localhost:3306
echo    数据库: wp_deploy_manager
echo    用户: wp_deploy
echo    密码: wp_deploy_pass_2024
echo    Root密码: wp_deploy_2024
echo.
echo 🛠️ 常用命令:
echo    启动服务: docker-compose start
echo    停止服务: docker-compose stop
echo    查看状态: docker-compose ps
echo    查看日志: docker-compose logs -f
echo.
echo 🚀 现在可以访问 http://localhost 开始使用！

pause
