#!/bin/bash

# WordPress 部署管理系统 - 错误调试脚本
# 用于收集和分析系统错误日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  WordPress 部署管理系统 - 错误调试"
echo "==================================================${NC}"

# 获取当前目录
CURRENT_DIR="$(pwd)"
LOG_DIR="$CURRENT_DIR/logs"
ERROR_REPORT="$CURRENT_DIR/error_report_$(date +%Y%m%d_%H%M%S).txt"

echo "项目目录: $CURRENT_DIR"
echo "日志目录: $LOG_DIR"
echo "错误报告: $ERROR_REPORT"
echo ""

# 创建错误报告
cat > "$ERROR_REPORT" << EOF
WordPress 部署管理系统 - 错误调试报告
生成时间: $(date)
项目目录: $CURRENT_DIR

================================================
1. 系统环境信息
================================================
EOF

# 收集系统信息
echo -e "${BLUE}[1/8]${NC} 收集系统环境信息..."
{
    echo "操作系统: $(uname -a)"
    echo "PHP版本: $(php -v | head -n 1)"
    echo "MySQL版本: $(mysql --version)"
    echo "Nginx版本: $(nginx -v 2>&1)"
    echo "磁盘空间: $(df -h $CURRENT_DIR | tail -n 1)"
    echo "内存使用: $(free -h | grep Mem)"
    echo "负载平均: $(uptime)"
    echo ""
} >> "$ERROR_REPORT"

# 检查文件权限
echo -e "${BLUE}[2/8]${NC} 检查文件权限..."
{
    echo "================================================"
    echo "2. 文件权限检查"
    echo "================================================"
    echo "项目目录权限:"
    ls -la "$CURRENT_DIR" | head -20
    echo ""
    echo "关键目录权限:"
    for dir in config api core logs uploads templates; do
        if [ -d "$dir" ]; then
            echo "$dir/: $(ls -ld $dir)"
        else
            echo "$dir/: 目录不存在"
        fi
    done
    echo ""
} >> "$ERROR_REPORT"

# 检查配置文件
echo -e "${BLUE}[3/8]${NC} 检查配置文件..."
{
    echo "================================================"
    echo "3. 配置文件检查"
    echo "================================================"
    
    if [ -f "config/config.php" ]; then
        echo "✓ config/config.php 存在"
    else
        echo "✗ config/config.php 不存在"
    fi
    
    if [ -f "config/database.php" ]; then
        echo "✓ config/database.php 存在"
    else
        echo "✗ config/database.php 不存在"
    fi
    
    if [ -f "config/app.json" ]; then
        echo "✓ config/app.json 存在"
        echo "app.json 内容:"
        cat config/app.json | head -20
    else
        echo "✗ config/app.json 不存在"
    fi
    echo ""
} >> "$ERROR_REPORT"

# 检查数据库连接
echo -e "${BLUE}[4/8]${NC} 检查数据库连接..."
{
    echo "================================================"
    echo "4. 数据库连接检查"
    echo "================================================"
    
    if [ -f "config/database.php" ]; then
        # 提取数据库配置
        DB_NAME=$(grep "DB_NAME" config/database.php | cut -d"'" -f4 2>/dev/null || echo "unknown")
        DB_USER=$(grep "DB_USER" config/database.php | cut -d"'" -f4 2>/dev/null || echo "unknown")
        
        echo "数据库名: $DB_NAME"
        echo "数据库用户: $DB_USER"
        
        # 测试数据库连接
        if mysql -u "$DB_USER" -p"$(grep "DB_PASS" config/database.php | cut -d"'" -f4 2>/dev/null)" -e "SELECT 1;" 2>/dev/null; then
            echo "✓ 数据库连接正常"
            
            # 检查表结构
            echo "数据库表:"
            mysql -u "$DB_USER" -p"$(grep "DB_PASS" config/database.php | cut -d"'" -f4 2>/dev/null)" "$DB_NAME" -e "SHOW TABLES;" 2>/dev/null || echo "无法获取表列表"
        else
            echo "✗ 数据库连接失败"
        fi
    else
        echo "✗ 数据库配置文件不存在"
    fi
    echo ""
} >> "$ERROR_REPORT"

# 收集PHP错误日志
echo -e "${BLUE}[5/8]${NC} 收集PHP错误日志..."
{
    echo "================================================"
    echo "5. PHP错误日志"
    echo "================================================"
    
    # 查找PHP错误日志
    PHP_ERROR_LOG="/var/log/php_errors.log"
    if [ -f "$PHP_ERROR_LOG" ]; then
        echo "PHP错误日志 (最近50行):"
        tail -50 "$PHP_ERROR_LOG" | grep -E "(error|Error|ERROR|fatal|Fatal|FATAL)" || echo "无PHP错误"
    else
        echo "未找到PHP错误日志文件"
    fi
    
    # 检查PHP配置
    echo ""
    echo "PHP配置:"
    php -i | grep -E "(error_reporting|display_errors|log_errors|error_log)" || echo "无法获取PHP配置"
    echo ""
} >> "$ERROR_REPORT"

# 收集Nginx错误日志
echo -e "${BLUE}[6/8]${NC} 收集Nginx错误日志..."
{
    echo "================================================"
    echo "6. Nginx错误日志"
    echo "================================================"
    
    # 项目Nginx错误日志
    if [ -f "$LOG_DIR/nginx_error.log" ]; then
        echo "项目Nginx错误日志 (最近50行):"
        tail -50 "$LOG_DIR/nginx_error.log"
    else
        echo "项目Nginx错误日志不存在"
    fi
    
    # 系统Nginx错误日志
    NGINX_ERROR_LOG="/var/log/nginx/error.log"
    if [ -f "$NGINX_ERROR_LOG" ]; then
        echo ""
        echo "系统Nginx错误日志 (最近20行):"
        tail -20 "$NGINX_ERROR_LOG" | grep "$(basename $CURRENT_DIR)" || echo "无相关错误"
    fi
    echo ""
} >> "$ERROR_REPORT"

# 收集系统日志
echo -e "${BLUE}[7/8]${NC} 收集系统日志..."
{
    echo "================================================"
    echo "7. 系统日志"
    echo "================================================"
    
    if [ -d "$LOG_DIR" ]; then
        echo "系统日志文件:"
        ls -la "$LOG_DIR/"
        echo ""
        
        # 最新的系统日志
        LATEST_LOG=$(ls -t "$LOG_DIR"/system_*.log 2>/dev/null | head -1)
        if [ -n "$LATEST_LOG" ]; then
            echo "最新系统日志 ($LATEST_LOG) 最后50行:"
            tail -50 "$LATEST_LOG"
        else
            echo "无系统日志文件"
        fi
    else
        echo "日志目录不存在"
    fi
    echo ""
} >> "$ERROR_REPORT"

# 检查API接口
echo -e "${BLUE}[8/8]${NC} 检查API接口..."
{
    echo "================================================"
    echo "8. API接口检查"
    echo "================================================"
    
    # 检查API文件
    echo "API文件检查:"
    for api_file in deploy.php templates.php status.php logs.php settings.php sse.php; do
        if [ -f "api/$api_file" ]; then
            echo "✓ api/$api_file 存在"
        else
            echo "✗ api/$api_file 不存在"
        fi
    done
    
    echo ""
    echo "public/api/ 目录检查:"
    if [ -d "public/api" ]; then
        ls -la public/api/
    else
        echo "public/api/ 目录不存在"
    fi
    
    # 测试API接口
    echo ""
    echo "API接口测试:"
    
    # 测试基础API
    if command -v curl >/dev/null 2>&1; then
        DOMAIN=$(grep -o '"domain":\s*"[^"]*"' config/app.json 2>/dev/null | cut -d'"' -f4 || echo "localhost")
        
        echo "测试 http://$DOMAIN/api/"
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN/api/" --max-time 5 2>/dev/null || echo "000")
        echo "HTTP状态码: $HTTP_CODE"
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✓ API基础接口正常"
        else
            echo "✗ API基础接口异常"
        fi
    else
        echo "curl命令不可用，无法测试API"
    fi
    echo ""
} >> "$ERROR_REPORT"

# 生成总结
{
    echo "================================================"
    echo "9. 问题总结和建议"
    echo "================================================"
    
    echo "常见问题检查:"
    
    # 检查权限问题
    if [ "$(stat -c %U $CURRENT_DIR)" != "www" ]; then
        echo "⚠️  文件所有者不是www，可能导致权限问题"
        echo "   解决方案: chown -R www:www $CURRENT_DIR"
    fi
    
    # 检查PHP扩展
    echo ""
    echo "PHP扩展检查:"
    for ext in pdo_mysql json curl zip; do
        if php -m | grep -q "$ext"; then
            echo "✓ $ext 扩展已安装"
        else
            echo "✗ $ext 扩展未安装"
        fi
    done
    
    echo ""
    echo "建议的解决步骤:"
    echo "1. 检查文件权限: chown -R www:www $CURRENT_DIR"
    echo "2. 检查PHP扩展是否完整安装"
    echo "3. 检查数据库连接配置"
    echo "4. 查看具体错误日志定位问题"
    echo "5. 确保Nginx配置正确"
    
    echo ""
    echo "报告生成完成: $ERROR_REPORT"
} >> "$ERROR_REPORT"

echo -e "${GREEN}=================================================="
echo "  🔍 错误调试完成！"
echo "=================================================="
echo "错误报告已生成: $ERROR_REPORT"
echo ""
echo "请查看报告文件了解详细信息。"
echo "如需技术支持，请提供此报告文件。"
echo "==================================================${NC}"

# 显示报告摘要
echo -e "${YELLOW}报告摘要:${NC}"
echo "- 系统环境: $(uname -s)"
echo "- PHP版本: $(php -v | head -n 1 | cut -d' ' -f2)"
echo "- 项目目录: $CURRENT_DIR"
echo "- 配置状态: $([ -f "config/database.php" ] && echo "已配置" || echo "未配置")"
echo "- 日志文件: $(ls -1 $LOG_DIR/*.log 2>/dev/null | wc -l) 个"

exit 0
