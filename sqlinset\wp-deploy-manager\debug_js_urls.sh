#!/bin/bash

# 调试JavaScript中的URL问题

set -e

echo "=================================================="
echo "  调试JavaScript URL问题"
echo "=================================================="

echo "1. 检查API.js中的baseUrl设置:"
grep -n "baseUrl" public/assets/js/api.js

echo ""
echo "2. 检查所有JavaScript文件中的API路径:"
find public/assets/js -name "*.js" -exec grep -l "wp-deploy-manager" {} \; 2>/dev/null || echo "未找到包含wp-deploy-manager的JS文件"

echo ""
echo "3. 搜索所有可能的错误路径:"
find public/assets/js -name "*.js" -exec grep -n "wp-deploy-manager/api" {} \; 2>/dev/null || echo "未找到wp-deploy-manager/api路径"

echo ""
echo "4. 检查main.js中的配置:"
grep -n -A5 -B5 "baseUrl\|api" public/assets/js/main.js | head -20

echo ""
echo "5. 检查是否有全局配置:"
grep -n -A3 -B3 "config\|API\|baseUrl" public/assets/js/*.js | grep -v "function\|class\|comment" | head -10

echo ""
echo "6. 检查HTML中是否有配置:"
grep -n "api\|config" public/index.html | head -10

echo ""
echo "=================================================="
echo "  调试完成"
echo "=================================================="
