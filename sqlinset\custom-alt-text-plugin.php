<?php
/*
Plugin Name: Custom Alt Text for WooCommerce Images
Description: Automatically set the ALT text for WooCommerce product images using the product name.
Version: 1.1
Author: icosation
*/

// 为现有产品批量设置 ALT 文本（分页处理）
add_action('admin_init', 'batch_update_image_alt_text_paginated');

function batch_update_image_alt_text_paginated() {
    $paged = get_option('alt_text_batch_page', 1); // 从选项中获取当前页码
    $posts_per_page = 50; // 每次处理的产品数量
    $max_log_file_size = 5 * 1024 * 1024; // 每个日志文件最大 5MB
    $log_file_name = __DIR__ . '/alt_text_update_log.txt'; // 初始日志文件名

    // 获取已更新的图片ID列表（可以存储在临时的数据库表中或者文件中）
    $processed_image_ids = get_option('processed_image_ids', array());

    // 打开日志文件
    $log_file = fopen($log_file_name, 'a');

    do {
        // 获取分页的产品
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => $posts_per_page,
            'paged' => $paged,
            'post_status' => 'publish',
        );
        $products = get_posts($args);

        // 如果没有更多产品，结束循环
        if (empty($products)) {
            break;
        }

        foreach ($products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product) {
                // 获取产品名称作为 ALT 文本
                $alt_text = sanitize_text_field($product->get_name());

                // 获取主图 ID 并更新 ALT 文本
                $image_id = $product->get_image_id();
                if ($image_id && !in_array($image_id, $processed_image_ids)) {
                    update_post_meta($image_id, '_wp_attachment_image_alt', $alt_text);
                    // 写入日志
                    fwrite($log_file, "Updated ALT text for product ID: " . $product_post->ID . " (Main image ID: $image_id)\n");
                    $processed_image_ids[] = $image_id; // 添加到已处理数组
                }

                // 获取附加图片并更新 ALT 文本
                $gallery_image_ids = $product->get_gallery_image_ids();
                if (!empty($gallery_image_ids)) {
                    foreach ($gallery_image_ids as $gallery_image_id) {
                        if (!in_array($gallery_image_id, $processed_image_ids)) {
                            update_post_meta($gallery_image_id, '_wp_attachment_image_alt', $alt_text);
                            // 写入日志
                            fwrite($log_file, "Updated ALT text for gallery image ID: $gallery_image_id (Product ID: " . $product_post->ID . ")\n");
                            $processed_image_ids[] = $gallery_image_id; // 添加到已处理数组
                        }
                    }
                }
            }
        }

        // 每处理完一页后保存已处理的图片ID
        update_option('processed_image_ids', $processed_image_ids);

        // 保存当前页码到选项
        update_option('alt_text_batch_page', $paged); 

        // 翻页处理后延迟 2 秒
        $paged++;
        sleep(2); // 加一个延迟

        // 如果日志文件超过最大大小，则切割日志
        if (filesize($log_file_name) > $max_log_file_size) {
            fclose($log_file); // 关闭当前日志文件
            $log_file_name = __DIR__ . '/alt_text_update_log_' . time() . '.txt'; // 生成新的日志文件
            $log_file = fopen($log_file_name, 'a'); // 打开新的日志文件
        }

    } while (count($products) === $posts_per_page); // 处理完所有产品后退出循环

    // 处理完成后删除选项
    delete_option('alt_text_batch_page');

    // 关闭日志文件
    fclose($log_file);
}
