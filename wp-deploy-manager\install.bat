@echo off
chcp 65001 >nul
echo 🚀 WordPress Deploy Manager 一键安装
echo ==================================================

REM 检查Docker
echo 🔍 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装，请先安装Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker 已安装

REM 创建目录结构
echo 📁 创建项目结构...
mkdir api config database logs public uploads 2>nul
mkdir uploads\temp uploads\templates 2>nul
mkdir public\assets public\assets\css public\assets\js public\assets\images 2>nul
mkdir public\api docker docker\apache 2>nul

REM 创建Docker Compose文件
echo 🐳 创建Docker配置...
(
echo version: '3.8'
echo.
echo services:
echo   web:
echo     image: php:8.1-apache
echo     container_name: wp-deploy-web
echo     ports:
echo       - "80:80"
echo     volumes:
echo       - ./public:/var/www/html
echo       - ./logs:/var/www/html/logs
echo       - ./uploads:/var/www/html/uploads
echo     depends_on:
echo       - mysql
echo     environment:
echo       - APACHE_DOCUMENT_ROOT=/var/www/html
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo     command: ^>
echo       bash -c "
echo       apt-get update ^&^& apt-get install -y libzip-dev ^&^&
echo       docker-php-ext-install pdo_mysql zip ^&^&
echo       a2enmod rewrite ^&^&
echo       echo '^<Directory /var/www/html^>' ^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '    AllowOverride All' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '    RewriteEngine On' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '    RewriteCond %%{REQUEST_FILENAME} !-f' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '    RewriteCond %%{REQUEST_FILENAME} !-d' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '    RewriteRule ^^api/(.*)$$ api/index.php [QSA,L]' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       echo '^</Directory^>' ^>^> /etc/apache2/conf-available/rewrite.conf ^&^&
echo       a2enconf rewrite ^&^&
echo       apache2-foreground"
echo.
echo   mysql:
echo     image: mysql:8.0
echo     container_name: wp-deploy-mysql
echo     ports:
echo       - "3306:3306"
echo     environment:
echo       MYSQL_ROOT_PASSWORD: wp_deploy_2024
echo       MYSQL_DATABASE: wp_deploy_manager
echo       MYSQL_USER: wp_deploy
echo       MYSQL_PASSWORD: wp_deploy_pass_2024
echo     volumes:
echo       - mysql_data:/var/lib/mysql
echo       - ./database:/docker-entrypoint-initdb.d
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo.
echo   phpmyadmin:
echo     image: phpmyadmin/phpmyadmin:latest
echo     container_name: wp-deploy-phpmyadmin
echo     ports:
echo       - "8080:80"
echo     environment:
echo       PMA_HOST: mysql
echo       PMA_USER: root
echo       PMA_PASSWORD: wp_deploy_2024
echo     depends_on:
echo       - mysql
echo     networks:
echo       - wp-deploy-network
echo     restart: unless-stopped
echo.
echo volumes:
echo   mysql_data:
echo.
echo networks:
echo   wp-deploy-network:
echo     driver: bridge
) > docker-compose.yml

REM 创建数据库初始化文件
echo 🗄️ 创建数据库配置...
(
echo -- WordPress 部署管理系统数据库初始化脚本
echo.
echo -- 模板表
echo CREATE TABLE IF NOT EXISTS templates ^(
echo     id INT AUTO_INCREMENT PRIMARY KEY,
echo     uuid VARCHAR^(36^) UNIQUE NOT NULL,
echo     name VARCHAR^(255^) NOT NULL,
echo     description TEXT,
echo     filename VARCHAR^(255^) NOT NULL,
echo     file_path VARCHAR^(500^) NOT NULL,
echo     file_size BIGINT NOT NULL,
echo     file_hash VARCHAR^(64^) NOT NULL,
echo     status ENUM^('active', 'inactive', 'deleted'^) DEFAULT 'active',
echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
echo ^) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
echo.
echo -- 部署任务表
echo CREATE TABLE IF NOT EXISTS deploy_jobs ^(
echo     id INT AUTO_INCREMENT PRIMARY KEY,
echo     uuid VARCHAR^(36^) UNIQUE NOT NULL,
echo     type ENUM^('single', 'batch'^) NOT NULL,
echo     template_id INT,
echo     domains JSON NOT NULL,
echo     status ENUM^('pending', 'running', 'completed', 'failed', 'cancelled'^) DEFAULT 'pending',
echo     progress INT DEFAULT 0,
echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
echo     FOREIGN KEY ^(template_id^) REFERENCES templates^(id^) ON DELETE SET NULL
echo ^) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
echo.
echo -- 系统设置表
echo CREATE TABLE IF NOT EXISTS system_settings ^(
echo     id INT AUTO_INCREMENT PRIMARY KEY,
echo     setting_key VARCHAR^(100^) UNIQUE NOT NULL,
echo     setting_value TEXT,
echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
echo ^) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
echo.
echo -- 插入默认设置
echo INSERT IGNORE INTO system_settings ^(setting_key, setting_value^) VALUES
echo ^('max_concurrent_jobs', '3'^),
echo ^('default_timeout', '1800'^),
echo ^('enable_notifications', 'false'^);
echo.
echo -- 插入测试模板
echo INSERT IGNORE INTO templates ^(uuid, name, description, filename, file_path, file_size, file_hash^) VALUES
echo ^('test-template-1', '测试模板1', '这是一个测试模板', 'test1.tar.gz', '/uploads/templates/test1.tar.gz', 1024000, 'hash1'^),
echo ^('test-template-2', '测试模板2', '这是另一个测试模板', 'test2.tar.gz', '/uploads/templates/test2.tar.gz', 2048000, 'hash2'^);
) > database\init_mysql.sql

echo ✅ 数据库配置创建完成

REM 启动Docker服务
echo 🚀 启动Docker服务...
docker-compose up -d

echo.
echo ⏳ 等待服务启动...
timeout /t 20 /nobreak >nul

echo.
echo 📊 检查服务状态...
docker-compose ps

echo.
echo ✅ WordPress Deploy Manager 安装完成！
echo ==================================================
echo.
echo 🌐 访问地址:
echo    主应用:      http://localhost
echo    phpMyAdmin:  http://localhost:8080
echo    API接口:     http://localhost/api/
echo.
echo 🔑 数据库信息:
echo    主机: localhost:3306
echo    数据库: wp_deploy_manager
echo    用户: wp_deploy
echo    密码: wp_deploy_pass_2024
echo    Root密码: wp_deploy_2024
echo.
echo 🛠️ 常用命令:
echo    启动服务: docker-compose start
echo    停止服务: docker-compose stop
echo    查看状态: docker-compose ps
echo    查看日志: docker-compose logs -f
echo.
echo 🚀 现在可以访问 http://localhost 开始使用！

pause
