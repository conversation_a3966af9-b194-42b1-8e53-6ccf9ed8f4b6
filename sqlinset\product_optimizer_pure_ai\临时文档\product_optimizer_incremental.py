#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer_incremental.py - 增量保存版本
支持中途终止时保存已处理结果，避免数据丢失
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
import signal
from concurrent.futures import ThreadPoolExecutor, as_completed

# DeepSeek API配置
DEEPSEEK_API_KEY = "sk-4691a82b5b5041ecbfa7fd40fdabc71"
DEEPSEEK_API_URL = "https://api.deepseek.com/chat/completions"

# 路径配置
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/output"

# 并发配置 - 高性能优化
MAX_WORKERS = 8       # 增加到8线程 (33%提升)
BATCH_SIZE = 25       # 增加到25产品/批次 (25%提升)
REQUEST_DELAY = 0.08  # 减少到0.08秒 (20%提升)
TIMEOUT_SECONDS = 90  # 增加超时时间适应更大批次
MAX_RETRIES = 3

# 性能模式配置
PERFORMANCE_MODES = {
    "conservative": {  # 保守模式 (当前性能)
        "workers": 6,
        "batch_size": 20,
        "delay": 0.1,
        "description": "稳定优先，适合长时间运行"
    },
    "balanced": {      # 平衡模式 (1.5倍性能)
        "workers": 8,
        "batch_size": 25,
        "delay": 0.08,
        "description": "平衡性能与稳定性"
    },
    "aggressive": {    # 激进模式 (2倍性能)
        "workers": 10,
        "batch_size": 30,
        "delay": 0.06,
        "description": "最大性能，需要监控稳定性"
    }
}

# 全局变量
current_output_file = None
processed_results = []
results_lock = threading.Lock()
graceful_shutdown = False
processed_files = set()  # 已处理文件集合
file_monitor_active = False  # 文件监控状态

def signal_handler(signum, frame):
    """信号处理器 - 优雅关闭"""
    global graceful_shutdown
    print(f"\n🛑 接收到终止信号 ({signum})，正在保存已处理结果...")
    graceful_shutdown = True
    save_partial_results()
    print("✅ 已处理结果已保存，程序退出")
    sys.exit(0)

def save_partial_results():
    """保存部分结果"""
    global current_output_file, processed_results
    
    if not current_output_file or not processed_results:
        return
    
    try:
        with results_lock:
            # 创建临时文件名
            base_name = os.path.splitext(current_output_file)[0]
            partial_file = f"{base_name}_partial.txt"
            
            with open(partial_file, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")
                f.write(f"# 部分处理结果 - 保存时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 已处理产品数量: {len(processed_results)}\n")
                f.write("="*120 + "\n")
                
                for result in processed_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")
            
            print(f"💾 部分结果已保存到: {partial_file}")
            print(f"📊 已保存 {len(processed_results)} 个产品的处理结果")
            
    except Exception as e:
        print(f"❌ 保存部分结果失败: {e}")

def add_processed_result(batch_results):
    """添加已处理结果到全局列表"""
    global processed_results
    
    with results_lock:
        for result in batch_results:
            processed_results.append(result)
        
        # 每处理10个批次保存一次
        if len(processed_results) % 200 == 0:  # 每200个产品保存一次
            save_incremental_backup()

def save_incremental_backup():
    """增量备份"""
    global current_output_file, processed_results
    
    if not current_output_file:
        return
    
    try:
        base_name = os.path.splitext(current_output_file)[0]
        backup_file = f"{base_name}_backup_{int(time.time())}.txt"
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write("SEO Name | Category | Tags\n")
            f.write("="*120 + "\n")
            f.write(f"# 增量备份 - 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 已处理产品数量: {len(processed_results)}\n")
            f.write("="*120 + "\n")
            
            for result in processed_results:
                seo_name = result.get("seo_name", "").replace("|", "-")
                category = result.get("category", "")
                tags = result.get("tags", "")
                f.write(f"{seo_name} | {category} | {tags}\n")
        
        print(f"💾 增量备份: {len(processed_results)} 个产品 → {os.path.basename(backup_file)}")
        
    except Exception as e:
        print(f"⚠️ 增量备份失败: {e}")

def ai_optimize_products(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """AI产品优化 - 带结果保存"""
    global graceful_shutdown
    
    if graceful_shutdown:
        return None, []
    
    batch_num, products_batch = batch_data
    
    # 构建提示词
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for

Requirements:
- Output language: 自动检测
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    for attempt in range(max_retries):
        if graceful_shutdown:
            return None, []
            
        try:
            time.sleep(random.uniform(REQUEST_DELAY * 0.8, REQUEST_DELAY * 1.2))
            
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4000,
                "stream": False
            }
            
            response = requests.post(
                DEEPSEEK_API_URL,
                json=data,
                headers={
                    'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
                    'Content-Type': 'application/json'
                },
                timeout=TIMEOUT_SECONDS
            )
            
            response.raise_for_status()
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()
                
                try:
                    # 清理JSON内容
                    clean_content = content.strip()
                    
                    if "```json" in clean_content:
                        start_idx = clean_content.find("```json") + 7
                        end_idx = clean_content.find("```", start_idx)
                        if end_idx > start_idx:
                            clean_content = clean_content[start_idx:end_idx].strip()
                    
                    parsed_result = json.loads(clean_content)
                    
                    if isinstance(parsed_result, dict) and "results" in parsed_result:
                        results_array = parsed_result["results"]
                    elif isinstance(parsed_result, list):
                        results_array = parsed_result
                    else:
                        results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                    
                    # 清理结果
                    cleaned_results = []
                    for result in results_array:
                        seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                        category = result.get("category", "")
                        tags = result.get("tags", "")
                        
                        # 优化关键词
                        if tags:
                            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
                            optimized_tags = []
                            for tag in tag_list[:2]:
                                if len(tag) <= 25:
                                    optimized_tags.append(tag)
                            tags = ", ".join(optimized_tags)
                            if len(tags) > 30:
                                tags = optimized_tags[0] if optimized_tags else ""
                        
                        cleaned_result = {
                            "seo_name": seo_name,
                            "category": category,
                            "tags": tags
                        }
                        cleaned_results.append(cleaned_result)
                    
                    # 添加到已处理结果
                    add_processed_result(cleaned_results)
                    
                    print(f"✅ 批次 {batch_num} 完成 | 已处理: {len(processed_results)} 个产品")
                    
                    return batch_num, cleaned_results
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        return batch_num, generate_basic_results(products_batch)
        
        except requests.exceptions.RequestException as e:
            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                delay = 2 ** attempt
                time.sleep(delay)
        except Exception as e:
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    backup_results = generate_basic_results(products_batch)
    add_processed_result(backup_results)
    return batch_num, backup_results

def generate_basic_results(products_batch):
    """生成基础结果（备用方案）"""
    results = []
    
    for product in products_batch:
        words = product.split()
        brand = words[0] if words else "Unknown"
        
        results.append({
            "seo_name": product.replace("|", "-"),
            "category": "General > Products",
            "tags": f"{brand} products"
        })
    
    return results

def monitor_performance():
    """性能监控"""
    global processed_results

    start_time = time.time()
    last_count = 0

    while not graceful_shutdown:
        time.sleep(60)  # 每分钟检查一次

        current_count = len(processed_results)
        elapsed = time.time() - start_time

        if elapsed > 0:
            # 计算速度
            total_speed = current_count / (elapsed / 60)  # 产品/分钟
            recent_speed = (current_count - last_count)   # 最近1分钟速度

            print(f"📊 性能监控: 总速度 {total_speed:.1f} 产品/分钟, 最近 {recent_speed} 产品/分钟")

            # 性能警告
            if recent_speed < 30:  # 低于30产品/分钟
                print(f"⚠️ 性能警告: 处理速度较慢，可能遇到API限制")
            elif recent_speed > 150:  # 高于150产品/分钟
                print(f"🚀 性能优秀: 处理速度很快")

        last_count = current_count

def get_supported_files():
    """获取支持的文件列表"""
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    # 查找无扩展名文件
    try:
        for item in os.listdir(INPUT_DIR):
            item_path = os.path.join(INPUT_DIR, item)
            if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
                all_files.append(item_path)
    except FileNotFoundError:
        pass

    return sorted(list(set(all_files)))

def is_file_stable(file_path, wait_time=3):
    """检查文件是否写入完成"""
    try:
        size1 = os.path.getsize(file_path)
        time.sleep(wait_time)
        size2 = os.path.getsize(file_path)
        return size1 == size2
    except:
        return False

def file_monitor_thread():
    """文件监控线程"""
    global file_monitor_active, processed_files, graceful_shutdown

    print("🔍 启动文件监控线程...")
    print("💡 现在可以随时添加新文件到input目录")

    last_files = set()

    while file_monitor_active and not graceful_shutdown:
        try:
            # 获取当前文件列表
            current_files = set(get_supported_files())

            # 检查新增文件
            new_files = current_files - last_files - processed_files

            if new_files:
                print(f"\n🆕 发现 {len(new_files)} 个新文件:")
                for file_path in sorted(new_files):
                    filename = os.path.basename(file_path)
                    print(f"   - {filename}")

                # 等待文件写入完成并处理
                for file_path in sorted(new_files):
                    if graceful_shutdown:
                        break

                    filename = os.path.basename(file_path)
                    print(f"\n📝 检查新文件: {filename}")

                    # 等待文件稳定
                    if is_file_stable(file_path):
                        print(f"✅ 文件稳定，开始处理: {filename}")

                        # 处理新文件
                        base_name = os.path.splitext(filename)[0]
                        output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

                        success = process_file_with_incremental_save(file_path, output_file)

                        if success:
                            processed_files.add(file_path)
                            print(f"✅ 新文件处理完成: {filename}")
                        else:
                            print(f"❌ 新文件处理失败: {filename}")
                    else:
                        print(f"⏳ 文件可能还在写入，稍后重试: {filename}")

            last_files = current_files.copy()

            # 每10秒检查一次
            time.sleep(10)

        except Exception as e:
            print(f"⚠️ 文件监控异常: {e}")
            time.sleep(5)

    print("🔍 文件监控线程结束")

def process_file_with_incremental_save(input_path, output_path):
    """带增量保存的文件处理"""
    global current_output_file, processed_results, graceful_shutdown

    # 设置当前输出文件
    current_output_file = output_path
    processed_results = []

    print(f"📁 输入文件: {input_path}")
    print(f"📄 输出文件: {output_path}")
    print(f"💾 增量保存: 每200个产品自动备份")
    print(f"🛑 支持优雅终止: Ctrl+C 或 kill 会保存已处理结果")

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 产品数量: {len(products):,} 个")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), BATCH_SIZE):
        batch_num = (i // BATCH_SIZE) + 1
        batch = products[i:i+BATCH_SIZE]
        batch_data.append((batch_num, batch))

    print(f"🔄 开始处理 ({len(batch_data)} 个批次，{MAX_WORKERS} 个线程)...")

    start_time = time.time()

    # 并发处理
    try:
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_batch = {
                executor.submit(ai_optimize_products, batch, "auto"): batch[0]
                for batch in batch_data
            }

            completed_batches = 0

            for future in as_completed(future_to_batch):
                if graceful_shutdown:
                    break

                try:
                    batch_num, batch_results = future.result()
                    if batch_results:
                        completed_batches += 1

                        # 显示进度
                        progress = (completed_batches / len(batch_data)) * 100
                        elapsed = time.time() - start_time
                        eta = (elapsed / completed_batches) * (len(batch_data) - completed_batches) if completed_batches > 0 else 0

                        print(f"📊 进度: {progress:.1f}% ({completed_batches}/{len(batch_data)}) | ETA: {eta/60:.1f}分钟")

                except Exception as e:
                    print(f"❌ 批次处理异常: {e}")

    except KeyboardInterrupt:
        print(f"\n🛑 用户中断处理")
        graceful_shutdown = True
        save_partial_results()
        return False

    # 保存最终结果
    if not graceful_shutdown and processed_results:
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in processed_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

            elapsed_time = time.time() - start_time
            print(f"\n✅ 处理完成!")
            print(f"📊 成功处理: {len(processed_results)} 个产品")
            print(f"⏱️ 总时间: {elapsed_time/60:.1f} 分钟")
            print(f"🚀 处理速度: {len(processed_results)/(elapsed_time/60):.0f} 产品/分钟")
            print(f"💾 结果保存到: {output_path}")

            return True

        except Exception as e:
            print(f"❌ 保存最终结果错误: {e}")
            save_partial_results()
            return False

    return False

def is_background_mode():
    """检测是否为后台运行模式"""
    import sys
    return not sys.stdin.isatty()

def select_performance_mode():
    """选择性能模式"""
    global MAX_WORKERS, BATCH_SIZE, REQUEST_DELAY

    background = is_background_mode()

    if background:
        # 后台模式默认使用平衡模式
        mode = "balanced"
        print(f"🤖 后台模式: 自动选择平衡性能模式")
    else:
        # 交互模式提供选择
        print(f"\n⚡ 性能模式选择:")
        print(f"1. 保守模式 - 6线程, 20产品/批次 (稳定优先)")
        print(f"2. 平衡模式 - 8线程, 25产品/批次 (推荐, 1.5倍性能)")
        print(f"3. 激进模式 - 10线程, 30产品/批次 (2倍性能)")

        try:
            choice = input("请选择模式 (1/2/3, 默认2): ").strip()
            if choice == "1":
                mode = "conservative"
            elif choice == "3":
                mode = "aggressive"
            else:
                mode = "balanced"  # 默认
        except (EOFError, KeyboardInterrupt):
            mode = "balanced"  # 默认

    # 应用配置
    config = PERFORMANCE_MODES[mode]
    MAX_WORKERS = config["workers"]
    BATCH_SIZE = config["batch_size"]
    REQUEST_DELAY = config["delay"]

    print(f"⚡ 性能配置: {mode.upper()} 模式")
    print(f"   线程数: {MAX_WORKERS}")
    print(f"   批次大小: {BATCH_SIZE} 产品/批次")
    print(f"   请求延迟: {REQUEST_DELAY} 秒")
    print(f"   说明: {config['description']}")

    # 计算预期性能提升
    baseline_speed = 6 * 20 / 0.1  # 保守模式基准
    current_speed = MAX_WORKERS * BATCH_SIZE / REQUEST_DELAY
    speedup = current_speed / baseline_speed

    print(f"   预期提速: {speedup:.1f}x")

    return mode

def main():
    """主函数"""
    global file_monitor_active, processed_files

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # kill命令

    print("🚀 高性能增量保存产品优化器 + 动态文件监控")
    print("="*60)
    print(f"💾 特性: 支持中途终止时保存已处理结果")
    print(f"🛑 优雅终止: Ctrl+C 或 kill 会自动保存")
    print(f"🔍 动态监控: 可在处理过程中添加新文件")
    print(f"⚡ 性能优化: 支持1-2倍速度提升")
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"🔄 API类型: DeepSeek Chat (高性能)")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")

    # 选择性能模式
    performance_mode = select_performance_mode()

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找初始文件
    txt_files = get_supported_files()

    if not txt_files:
        print(f"📋 input目录为空，启动文件监控等待新文件...")
    else:
        print(f"📋 发现 {len(txt_files)} 个初始文件，开始处理...")

    # 启动文件监控线程
    file_monitor_active = True
    monitor_thread = threading.Thread(target=file_monitor_thread, daemon=True)
    monitor_thread.start()

    # 启动性能监控线程
    perf_thread = threading.Thread(target=monitor_performance, daemon=True)
    perf_thread.start()

    success_count = 0
    total_start_time = time.time()

    # 处理初始文件
    for i, input_file in enumerate(txt_files, 1):
        if graceful_shutdown:
            break

        print(f"\n{'='*60}")
        print(f"🔄 处理初始文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

        success = process_file_with_incremental_save(input_file, output_file)

        if success:
            success_count += 1
            processed_files.add(input_file)
            print(f"✅ 初始文件 {i} 处理完成")
        else:
            print(f"❌ 初始文件 {i} 处理失败或中断")

    # 如果没有被中断，继续监控新文件
    if not graceful_shutdown:
        print(f"\n{'='*60}")
        print(f"🎉 初始文件处理完成!")
        print(f"📊 成功处理: {success_count}/{len(txt_files)} 个初始文件")
        print(f"⏱️ 初始处理耗时: {(time.time() - total_start_time)/60:.1f} 分钟")
        print(f"🔍 继续监控新文件... (添加文件到 {INPUT_DIR})")
        print(f"💡 按 Ctrl+C 停止监控并退出")
        print(f"{'='*60}")

        # 保持监控状态
        try:
            while file_monitor_active and not graceful_shutdown:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n🛑 用户停止监控")
            graceful_shutdown = True

    # 停止文件监控
    file_monitor_active = False

    # 最终总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    if graceful_shutdown:
        print(f"🛑 处理被中断")
    else:
        print(f"🎉 监控结束!")
    print(f"📊 总共处理: {len(processed_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
