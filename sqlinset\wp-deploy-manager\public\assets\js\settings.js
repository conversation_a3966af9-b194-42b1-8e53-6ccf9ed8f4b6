/**
 * 系统设置模块
 */

class SettingsManager {
    constructor() {
        this.settings = {};
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
    }

    setupEventListeners() {
        const saveBtn = document.querySelector('[onclick="saveSettings()"]');
        if (saveBtn) {
            saveBtn.onclick = () => this.saveSettings();
        }

        const resetBtn = document.querySelector('[onclick="resetSettings()"]');
        if (resetBtn) {
            resetBtn.onclick = () => this.resetSettings();
        }

        const testBtn = document.querySelector('[onclick="testConnection()"]');
        if (testBtn) {
            testBtn.onclick = () => this.testConnection();
        }
    }

    async loadSettings() {
        try {
            const response = await api.getSettings();
            this.settings = response.data;
            
            this.renderSettings();
            
        } catch (error) {
            console.error('Failed to load settings:', error);
            app.showNotification('加载设置失败', 'error');
        }
    }

    renderSettings() {
        // 填充表单字段
        this.fillFormField('max_concurrent_jobs', this.settings.max_concurrent_jobs);
        this.fillFormField('default_timeout', this.settings.default_timeout);
        this.fillFormField('enable_notifications', this.settings.enable_notifications);
        this.fillFormField('notification_email', this.settings.notification_email);
        this.fillFormField('backup_before_deploy', this.settings.backup_before_deploy);
        this.fillFormField('auto_ssl_setup', this.settings.auto_ssl_setup);
        this.fillFormField('log_retention_days', this.settings.log_retention_days);
        this.fillFormField('auto_cleanup_logs', this.settings.auto_cleanup_logs);

        // 渲染系统信息
        this.renderSystemInfo();
    }

    fillFormField(name, value) {
        const field = document.querySelector(`[name="${name}"]`);
        if (!field) return;

        if (field.type === 'checkbox') {
            field.checked = Boolean(value);
        } else {
            field.value = value || '';
        }
    }

    renderSystemInfo() {
        const container = document.getElementById('system-info');
        if (!container || !this.settings.system_info) return;

        const info = this.settings.system_info;
        
        const html = `
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">PHP版本:</span>
                    <span class="info-value">${info.php_version || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">MySQL版本:</span>
                    <span class="info-value">${info.mysql_version || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">操作系统:</span>
                    <span class="info-value">${info.os || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Web服务器:</span>
                    <span class="info-value">${info.web_server || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">内存限制:</span>
                    <span class="info-value">${info.memory_limit || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">最大执行时间:</span>
                    <span class="info-value">${info.max_execution_time || 'Unknown'}秒</span>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    async saveSettings() {
        try {
            const formData = this.getFormData();
            
            const response = await api.updateSettings(formData);
            
            app.showNotification('设置保存成功', 'success');
            this.loadSettings(); // 重新加载设置
            
        } catch (error) {
            console.error('Save settings failed:', error);
            app.showNotification('保存设置失败: ' + error.message, 'error');
        }
    }

    getFormData() {
        const form = document.getElementById('settings-form');
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};

        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 处理复选框
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            data[checkbox.name] = checkbox.checked;
        });

        return data;
    }

    async resetSettings() {
        if (!confirm('确定要重置所有设置到默认值吗？')) {
            return;
        }

        try {
            const response = await api.resetSettings();
            
            app.showNotification('设置已重置为默认值', 'success');
            this.loadSettings();
            
        } catch (error) {
            console.error('Reset settings failed:', error);
            app.showNotification('重置设置失败', 'error');
        }
    }

    async testConnection() {
        try {
            app.showNotification('测试数据库连接...', 'info');
            
            const response = await api.testConnection();
            
            if (response.success) {
                app.showNotification('数据库连接正常', 'success');
                
                // 显示连接信息
                const info = response.data;
                const modal = new Modal({
                    title: '数据库连接信息',
                    content: `
                        <div class="connection-info">
                            <div class="info-item">
                                <span class="info-label">状态:</span>
                                <span class="info-value status-connected">已连接</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">主机:</span>
                                <span class="info-value">${info.host}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">数据库:</span>
                                <span class="info-value">${info.database}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">用户:</span>
                                <span class="info-value">${info.user}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">服务器版本:</span>
                                <span class="info-value">${info.server_version}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">配置表数量:</span>
                                <span class="info-value">${info.config_count}</span>
                            </div>
                        </div>
                    `
                });
                
                modal.open();
            } else {
                app.showNotification('数据库连接失败', 'error');
            }
            
        } catch (error) {
            console.error('Test connection failed:', error);
            app.showNotification('测试连接失败', 'error');
        }
    }
}

// 全局函数
function saveSettings() {
    if (window.settingsManager) {
        window.settingsManager.saveSettings();
    }
}

function resetSettings() {
    if (window.settingsManager) {
        window.settingsManager.resetSettings();
    }
}

function testConnection() {
    if (window.settingsManager) {
        window.settingsManager.testConnection();
    }
}

// 创建全局实例
window.settingsManager = new SettingsManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SettingsManager;
}
