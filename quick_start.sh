#!/bin/bash

# WordPress Deploy Manager 快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 WordPress Deploy Manager 快速启动${NC}"
echo "=================================================="

# 检查系统要求
echo -e "${BLUE}[1/6]${NC} 检查系统要求..."

# 检查Docker
if command -v docker &> /dev/null; then
    echo -e "   ✅ Docker 已安装: $(docker --version | cut -d' ' -f3)"
else
    echo -e "   ❌ Docker 未安装"
    echo -e "   请访问 https://docs.docker.com/get-docker/ 安装Docker"
    exit 1
fi

# 检查Docker Compose
if command -v docker-compose &> /dev/null; then
    echo -e "   ✅ Docker Compose 已安装: $(docker-compose --version | cut -d' ' -f3)"
elif docker compose version &> /dev/null; then
    echo -e "   ✅ Docker Compose (Plugin) 已安装"
    DOCKER_COMPOSE_CMD="docker compose"
else
    echo -e "   ❌ Docker Compose 未安装"
    exit 1
fi

# 设置Docker Compose命令
DOCKER_COMPOSE_CMD=${DOCKER_COMPOSE_CMD:-"docker-compose"}

# 检查端口占用
echo -e "${BLUE}[2/6]${NC} 检查端口占用..."
PORTS=(80 3306 6379 8080 8025)
for port in "${PORTS[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "   ⚠️  端口 $port 已被占用"
        read -p "   是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        echo -e "   ✅ 端口 $port 可用"
    fi
done

# 创建必要目录
echo -e "${BLUE}[3/6]${NC} 创建项目目录..."
mkdir -p {config,database,logs,uploads,uploads/temp,uploads/templates,docker/{apache,php,mysql,redis,supervisor,scripts}}

# 检查配置文件
echo -e "${BLUE}[4/6]${NC} 检查配置文件..."

# 创建数据库配置
if [ ! -f "config/database.php" ]; then
    echo -e "   创建数据库配置文件..."
    cat > config/database.php << 'EOF'
<?php
// Docker环境数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);
?>
EOF
    echo -e "   ✅ 数据库配置文件已创建"
fi

# 创建应用配置
if [ ! -f "config/app.json" ]; then
    echo -e "   创建应用配置文件..."
    cat > config/app.json << 'EOF'
{
    "mysql_root_password": "wp_deploy_2024",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "<EMAIL>",
    "backup_before_deploy": true,
    "auto_ssl_setup": false,
    "debug_mode": true,
    "log_level": "DEBUG",
    "redis_enabled": true,
    "redis_host": "redis",
    "redis_port": 6379
}
EOF
    echo -e "   ✅ 应用配置文件已创建"
fi

# 创建数据库初始化文件（如果不存在）
if [ ! -f "database/init_mysql.sql" ]; then
    echo -e "   创建数据库初始化文件..."
    cat > database/init_mysql.sql << 'EOF'
-- WordPress 部署管理系统数据库初始化脚本 (MySQL版本)

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    mother_domain VARCHAR(255),
    mother_db VARCHAR(255),
    mysql_root_pass VARCHAR(255),
    config_data JSON,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署任务表
CREATE TABLE IF NOT EXISTS deploy_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    type ENUM('single', 'batch') NOT NULL,
    template_id INT,
    domains JSON NOT NULL,
    config_data JSON,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署日志表
CREATE TABLE IF NOT EXISTS deploy_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    domain VARCHAR(255),
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') DEFAULT 'INFO',
    message TEXT NOT NULL,
    context JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES deploy_jobs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认设置
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('max_concurrent_jobs', '3', '最大并发任务数'),
('default_timeout', '1800', '默认超时时间（秒）'),
('enable_notifications', 'false', '启用通知'),
('notification_email', '', '通知邮箱'),
('backup_before_deploy', 'true', '部署前自动备份'),
('auto_ssl_setup', 'false', '自动SSL设置');

-- 创建索引
CREATE INDEX idx_deploy_jobs_status ON deploy_jobs(status);
CREATE INDEX idx_deploy_jobs_created_at ON deploy_jobs(created_at);
CREATE INDEX idx_deploy_logs_job_id ON deploy_logs(job_id);
CREATE INDEX idx_deploy_logs_level ON deploy_logs(level);
CREATE INDEX idx_templates_status ON templates(status);
EOF
    echo -e "   ✅ 数据库初始化文件已创建"
fi

# 启动Docker服务
echo -e "${BLUE}[5/6]${NC} 启动Docker服务..."
$DOCKER_COMPOSE_CMD up -d

# 等待服务启动
echo -e "${BLUE}[6/6]${NC} 等待服务启动..."
echo -e "   等待MySQL启动..."
sleep 10

# 检查服务状态
echo -e "${GREEN}检查服务状态...${NC}"
$DOCKER_COMPOSE_CMD ps

# 显示访问信息
echo -e "${GREEN}=================================================="
echo -e "  🎉 WordPress Deploy Manager 启动成功！"
echo -e "==================================================${NC}"
echo -e "📱 主应用:      ${BLUE}http://localhost${NC}"
echo -e "🗄️  phpMyAdmin:  ${BLUE}http://localhost:8080${NC}"
echo -e "📧 MailHog:     ${BLUE}http://localhost:8025${NC}"
echo -e "🏥 健康检查:    ${BLUE}http://localhost/health.php${NC}"
echo ""
echo -e "${YELLOW}数据库信息:${NC}"
echo -e "   主机: localhost:3306"
echo -e "   数据库: wp_deploy_manager"
echo -e "   用户: wp_deploy"
echo -e "   密码: wp_deploy_pass_2024"
echo ""
echo -e "${YELLOW}常用命令:${NC}"
echo -e "   查看日志: ${BLUE}$DOCKER_COMPOSE_CMD logs -f${NC}"
echo -e "   停止服务: ${BLUE}$DOCKER_COMPOSE_CMD down${NC}"
echo -e "   重启服务: ${BLUE}$DOCKER_COMPOSE_CMD restart${NC}"
echo -e "   进入容器: ${BLUE}$DOCKER_COMPOSE_CMD exec web bash${NC}"
echo ""
echo -e "${GREEN}🚀 开始使用WordPress Deploy Manager吧！${NC}"
