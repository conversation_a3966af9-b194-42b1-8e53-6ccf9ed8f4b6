import google.generativeai as genai
import os
import time

# --- 配置信息 ---
API_KEY = "AIzaSyA8XCSZHx_7knzDujBa9h3JQZrvClCskRA"  # 替换为您的真实 API 密钥
INPUT_FILE = "catetest.txt"    # 您的输入文件名
OUTPUT_FILE = "processed_products_gemini_cn.txt" # 输出文件名
BATCH_SIZE = 30  # 每次 API 调用处理的产品行数 (根据需要调整)
DELAY_BETWEEN_BATCHES = 5  # 每批处理之间的延迟秒数，以遵守速率限制

# 配置 Gemini API 客户端
genai.configure(api_key=API_KEY)

# --- 定义提示 (Prompt) ---
# 这个提示非常关键。它告诉 Gemini 具体要做什么。
# 我们会将成批的产品名称插入到这个提示中。
# !!! 注意：此 PROMPT_TEMPLATE 是给 Gemini 模型看的，因此保持英文 !!!
PROMPT_TEMPLATE = """
You are an expert e-commerce product data categorizer and optimizer.
For each product title provided below, please perform the following actions:

1.  **Category Hierarchy:** Create a 1 to 3-level category hierarchy in English. Use ">" as the separator (e.g., Automotive Exterior > Lighting > Headlights). Base the categories on common e-commerce structures like Amazon or Walmart for automotive parts.
2.  **SEO Optimized Product Name:** Generate an SEO-optimized product name in English. This name should be Google-friendly, highly descriptive, include key attributes, and be different from the original product name.
3.  **Tags (Long-tail Keywords):**
    *   Generate 1 or 2 highly relevant **long-tail keywords** as tags.
    *   These tags MUST be directly related to the specific product described in the "Original Product Name".
    *   **Analyze the "Original Product Name" carefully** to identify key features, product type, brand, intended use, or specific vehicle fitment if mentioned.
    *   The long-tail keywords should be phrases that a potential customer might use when searching for this specific item.
    *   **Example for a product like "ACDelco Gold PF2232G Engine Oil Filter":**
        *   Tag1: acdelco gold engine oil filter
        *   Tag2: pf2232g oil filter replacement
    *   **Example for "3D MAXpider Chevrolet Bolt EUV 2022-2022 Kagu Gray Row 1 Row 2":**
        *   Tag1: custom fit floor mats for 2022 chevy bolt euv
        *   Tag2: 3d maxpider kagu gray floor liners
    *   Avoid very generic single-word tags unless absolutely necessary and the product itself is extremely generic. Focus on specificity.
4.  **Output Format:** Format EACH processed product line strictly as:
    Original Product Name | Category Hierarchy | SEO Optimized Product Name | Tag1 | Tag2 (if two tags are generated, otherwise just Tag1)

**Important Instructions:**
*   Ensure each processed product is on a **NEW LINE**.
*   Provide ONLY the processed output data in the specified format. Do NOT include any introductory sentences, summaries, apologies, or any text other than the pipe-separated product data.
*   If a product name is very generic or unidentifiable, try your best or indicate difficulty within the "SEO Optimized Product Name" field, but still attempt to provide the structured output.

Here is an example of the input (one line from the list) and the desired output format for THAT SINGLE line:

**Example Input Line:**
3D MAXpider Chevrolet Bolt EUV 2022-2022 Kagu Gray Row 1 Row 2

**Example Desired Output Line for the above input:**
3D MAXpider Chevrolet Bolt EUV 2022-2022 Kagu Gray Row 1 Row 2 | Automotive Interior > Floor Mats > Custom Fit Floor Mats | 3D MAXpider Kagu All-Weather Custom Fit Floor Mats for 2022 Chevy Bolt EUV (1st & 2nd Row, Gray) | custom fit floor mats for 2022 chevy bolt euv | 3d maxpider kagu gray floor liners

Now, process the following product titles:

{product_list_batch}
"""

def process_batch_with_gemini(product_lines_batch):
    """将一批产品行发送给 Gemini 进行处理。"""
    model = genai.GenerativeModel('gemini-2.5-pro')  # 或者 'gemini-1.0-pro'
    
    # 将批次中的产品行合并为单个字符串，每行一个
    product_list_str = "\n".join(product_lines_batch)
    
    # 构建完整的提示
    prompt = PROMPT_TEMPLATE.format(product_list_batch=product_list_str)
    
    try:
        # print(f"--- 正在发送批处理提示 ---") # 可选：用于调试
        # print(prompt[:500] + "...") # 可选：打印提示的开头部分
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                # candidate_count=1, # 默认为 1
                # stop_sequences=['...'], # 如果需要
                # max_output_tokens= (BATCH_SIZE * 150), # 估算值，根据需要调整。Gemini自身有其限制。
                temperature=0.2 # 较低的 temperature 以获得更偏向事实、创造性较低的输出
            ),
            # safety_settings=[ # 如果您因为良性内容过于频繁地遇到阻止，可以调整安全设置
            #     {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            #     {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            #     {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            #     {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            # ]
        )
        # print(f"--- 已收到响应 ---") # 可选
        # print(response.text[:500] + "...") # 可选：打印响应的开头部分
        return response.text.strip()
    except Exception as e:
        print(f"使用 Gemini 处理批次时出错: {e}")
        # 返回原始行，以便可以将它们写入错误文件或重试
        return "\n".join([f"{line} | ERROR_PROCESSING | Error: {e} | " for line in product_lines_batch])


def main():
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as infile, \
             open(OUTPUT_FILE, 'w', encoding='utf-8') as outfile:
            
            # 写入表头
            outfile.write("Original Product Name | Category Hierarchy | SEO Optimized Product Name | Tag1 | Tag2\n")

            batch = []
            line_count = 0
            total_lines_processed = 0 # 用于粗略统计实际处理的产品条目数

            for line in infile:
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                
                batch.append(line)
                line_count += 1
                
                if len(batch) >= BATCH_SIZE:
                    print(f"正在处理 {len(batch)} 行的批次 (已读取总行数: {line_count})...")
                    processed_text = process_batch_with_gemini(batch)
                    if processed_text:
                        outfile.write(processed_text + "\n") # API应该返回多行，每行一个产品
                        outfile.flush() #确保数据立即写入
                        # 尝试更准确地统计处理的行数
                        actual_processed_in_batch = len(processed_text.splitlines())
                        total_lines_processed += actual_processed_in_batch
                        print(f"批处理完成，该批次处理了 {actual_processed_in_batch} 个产品。")
                    else:
                        print(f"警告：批次（起始行: {batch[0]}）返回空响应。")
                        total_lines_processed += 0 # 或者标记为错误
                    batch = []
                    print(f"等待 {DELAY_BETWEEN_BATCHES} 秒...")
                    time.sleep(DELAY_BETWEEN_BATCHES)
            
            # 处理最后一批中剩余的行
            if batch:
                print(f"正在处理最后 {len(batch)} 行的批次...")
                processed_text = process_batch_with_gemini(batch)
                if processed_text:
                    outfile.write(processed_text + "\n")
                    outfile.flush()
                    actual_processed_in_batch = len(processed_text.splitlines())
                    total_lines_processed += actual_processed_in_batch
                    print(f"最后批处理完成，该批次处理了 {actual_processed_in_batch} 个产品。")


            print(f"\n处理完成。大约处理了 {total_lines_processed} 个产品。")
            print(f"输出已保存到: {OUTPUT_FILE}")

    except FileNotFoundError:
        print(f"错误: 输入文件 '{INPUT_FILE}' 未找到。")
    except Exception as e:
        print(f"发生意外错误: {e}")

if __name__ == "__main__":
    main()