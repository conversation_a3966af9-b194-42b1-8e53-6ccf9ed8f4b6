#!/bin/bash

# 包含域名列表的文件，每行一个域名
DOMAINS_FILE="domains.txt"
# 输出结果的 CSV 文件
OUTPUT_FILE="ssl_status_report.csv"

# 如果 domains.txt 不存在，创建一个用于测试
if [ ! -f "$DOMAINS_FILE" ]; then
  echo "example.com" > "$DOMAINS_FILE"
  echo "werkzeugdiscount.com" >> "$DOMAINS_FILE" # 你图片中的域名
  echo "expired.badssl.com" >> "$DOMAINS_FILE"   # 一个过期证书的例子
  echo "self-signed.badssl.com" >> "$DOMAINS_FILE" # 一个自签名证书的例子
  echo "google.com" >> "$DOMAINS_FILE"
fi

# 创建/清空输出文件并添加表头
echo "域名,状态,详情" > "$OUTPUT_FILE"

# 检查 curl 是否安装
if ! command -v curl &> /dev/null; then
    echo "未找到 curl 命令，请先安装。"
    exit 1
fi

while IFS= read -r domain || [[ -n "$domain" ]]; do
  # 跳过空行或注释行
  [[ "$domain" =~ ^\s*# ]] && continue
  [[ -z "$domain" ]] && continue

  echo "正在检查 $domain..."
  status="未知"
  details=""

  # -A: 设置 User-Agent，避免某些服务器阻止脚本访问
  # -s: 静默模式，不输出进度条
  # -L: 跟随重定向
  # --connect-timeout: 连接超时时间（秒）
  # --max-time: 总操作超时时间（秒）
  # 2>&1: 将标准错误重定向到标准输出，以便捕获 curl 本身的 SSL 错误信息
  response_output=$(curl -A "Mozilla/5.0 BatchSSLChecker/1.0" -sL --connect-timeout 5 --max-time 10 "https://thedomain.com$domain" 2>&1)
  curl_exit_code=$?

  if [ $curl_exit_code -ne 0 ]; then
    # curl 命令本身执行失败，可能是网络问题或客户端无法验证服务器证书
    status="CURL 错误"
    details="curl 退出代码 $curl_exit_code."

    if echo "$response_output" | grep -q "SSL certificate problem: certificate has expired"; then
      status="SSL 错误 (客户端检测)"
      details="证书已过期"
    elif echo "$response_output" | grep -q "SSL certificate problem: unable to get local issuer certificate"; then
      status="SSL 错误 (客户端检测)"
      details="不受信任的颁发者 / 自签名 / 证书链问题"
    elif echo "$response_output" | grep -q "SSL peer certificate or SSH remote key was not OK"; then
      status="SSL 错误 (客户端检测)"
      details="SSL 对端证书无效"
    elif echo "$response_output" | grep -q "Could not resolve host"; then
      status="DNS 错误"
      details="无法解析主机"
    elif echo "$response_output" | grep -q "Connection timed out"; then
      status="网络错误"
      details="连接超时"
    else
      # 其他 curl 错误
      details="$details $(echo "$response_output" | head -n 1)" # 添加 curl 输出的第一行作为参考
    fi
  else
    # curl 成功获取内容，检查 HTML 内容中是否有 Cloudflare 错误
    if echo "$response_output" | grep -Eiq "Error code 526|Invalid SSL certificate on origin"; then
      status="Cloudflare 错误 526"
      details="源服务器 SSL 证书无效。"
    elif echo "$response_output" | grep -Eiq "Error code 525|SSL handshake failed"; then
      status="Cloudflare 错误 525"
      details="Cloudflare 与源服务器 SSL 握手失败。"
    elif echo "$response_output" | grep -Eiq "Error 520"; then
      status="Cloudflare 错误 520"
      details="Web 服务器向 Cloudflare 返回了未知错误。"
    elif echo "$response_output" | grep -Eiq "502 Bad Gateway"; then
      status="HTTP 502"
      details="错误网关 (通常是源服务器问题)。"
    elif echo "$response_output" | grep -Eiq "503 Service Unavailable"; then
      status="HTTP 503"
      details="服务不可用 (通常是源服务器问题)。"
    elif echo "$response_output" | grep -Eiq "Invalid SSL certificate"; then # 如果不是526，但页面内容仍指示SSL无效
      status="SSL 无效 (HTML内容检测)"
      details="页面内容指示 SSL 证书无效。"
    else
      # 如果在内容中未找到明显错误，尝试获取 HTTP 状态码
      # 这需要再次调用 curl 或更复杂的头部解析
      http_code=$(curl -A "Mozilla/5.0 BatchSSLChecker/1.0" -sL --connect-timeout 5 --max-time 10 -w "%{http_code}" "https://thedomain.com$domain" -o /dev/null)
      if [[ "$http_code" == "2"* || "$http_code" == "3"* ]]; then # 2xx 或 3xx 状态码
        status="正常 (HTTP $http_code)"
        details="SSL 握手可能正常，HTTP 状态码 $http_code。"
      else
        status="HTTP 错误 ($http_code)"
        details="收到 HTTP 状态码 $http_code。"
      fi
    fi
  fi

  echo "$domain,\"$status\",\"$details\"" >> "$OUTPUT_FILE"
  # 可选：添加少量延迟，避免对服务器造成过大压力
  # sleep 0.1
done < "$DOMAINS_FILE"

echo "批量 SSL 检查完成。结果已保存到 $OUTPUT_FILE"