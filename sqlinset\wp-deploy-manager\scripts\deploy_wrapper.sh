#!/bin/bash

# WordPress 部署包装脚本
# 用于包装原始部署脚本，提供更好的错误处理和日志记录

set -e

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ORIGINAL_SCRIPT="/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh"

# 获取任务ID
JOB_ID="$1"

if [ -z "$JOB_ID" ]; then
    echo "错误: 缺少任务ID参数"
    exit 1
fi

# 日志函数
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 更新任务状态
update_job_status() {
    local job_id="$1"
    local status="$2"
    local progress="$3"
    local step="$4"
    local error_msg="$5"
    
    # 调用PHP脚本更新数据库状态
    php -r "
    define('WP_DEPLOY_MANAGER', true);
    require_once '$PROJECT_ROOT/config/config.php';

    try {
        \$db = getDatabase();
        
        \$sql = 'UPDATE deploy_jobs SET status = ?, progress = ?, current_step = ?, updated_at = CURRENT_TIMESTAMP';
        \$params = ['$status', $progress, '$step'];
        
        if ('$status' === 'running' && \$progress == 0) {
            \$sql .= ', started_at = CURRENT_TIMESTAMP';
        } elseif (in_array('$status', ['completed', 'failed', 'cancelled'])) {
            \$sql .= ', completed_at = CURRENT_TIMESTAMP';
        }
        
        if (!empty('$error_msg')) {
            \$sql .= ', error_message = ?';
            \$params[] = '$error_msg';
        }
        
        \$sql .= ' WHERE id = ?';
        \$params[] = $job_id;
        
        \$stmt = \$db->prepare(\$sql);
        \$stmt->execute(\$params);
        
        echo 'Status updated successfully';
    } catch (Exception \$e) {
        echo 'Error updating status: ' . \$e->getMessage();
    }
    "
}

# 获取任务信息
get_job_info() {
    local job_id="$1"
    
    php -r "
    define('WP_DEPLOY_MANAGER', true);
    require_once '$PROJECT_ROOT/config/config.php';
    
    try {
        \$db = getDatabase();
        
        \$stmt = \$db->prepare('SELECT * FROM deploy_jobs WHERE id = ?');
        \$stmt->execute([$job_id]);
        \$job = \$stmt->fetch(PDO::FETCH_ASSOC);
        
        if (\$job) {
            echo json_encode(\$job);
        } else {
            echo '';
        }
    } catch (Exception \$e) {
        echo '';
    }
    "
}

# 获取模板信息
get_template_info() {
    local template_id="$1"
    
    php -r "
    define('WP_DEPLOY_MANAGER', true);
    require_once '$PROJECT_ROOT/config/config.php';
    
    try {
        \$db = getDatabase();
        
        \$stmt = \$db->prepare('SELECT * FROM templates WHERE id = ?');
        \$stmt->execute([$template_id]);
        \$template = \$stmt->fetch(PDO::FETCH_ASSOC);
        
        if (\$template) {
            echo json_encode(\$template);
        } else {
            echo '';
        }
    } catch (Exception \$e) {
        echo '';
    }
    "
}

# 执行单域名部署
execute_single_deploy() {
    local job_info="$1"
    local template_info="$2"
    
    # 解析配置
    local domain=$(echo "$job_info" | jq -r '.domain')
    local config_data=$(echo "$job_info" | jq -r '.config_data')
    
    # 解析模板信息
    local mother_domain=$(echo "$template_info" | jq -r '.mother_domain')
    local mother_db=$(echo "$template_info" | jq -r '.mother_db')
    local mysql_root_pass=$(echo "$template_info" | jq -r '.mysql_root_pass')
    
    log_info "开始部署域名: $domain"
    
    # 步骤1: 准备部署环境
    update_job_status "$JOB_ID" "running" 10 "准备部署环境"
    log_info "步骤1: 准备部署环境"
    
    # 生成数据库配置
    local db_name=$(echo "${domain//./_}" | tr '-' '_' | tr '[:upper:]' '[:lower:]')
    local db_user="$db_name"
    local db_pass=$(openssl rand -base64 12 | tr -d '=+/' | cut -c1-12)
    local email="info@$domain"
    
    # 步骤2: 验证原始脚本
    update_job_status "$JOB_ID" "running" 20 "验证部署脚本"
    log_info "步骤2: 验证部署脚本"
    
    if [ ! -f "$ORIGINAL_SCRIPT" ]; then
        log_error "原始部署脚本不存在: $ORIGINAL_SCRIPT"
        update_job_status "$JOB_ID" "failed" 20 "验证部署脚本" "原始部署脚本不存在"
        return 1
    fi
    
    if [ ! -x "$ORIGINAL_SCRIPT" ]; then
        log_error "原始部署脚本不可执行: $ORIGINAL_SCRIPT"
        update_job_status "$JOB_ID" "failed" 20 "验证部署脚本" "原始部署脚本不可执行"
        return 1
    fi
    
    # 步骤3: 检查域名冲突
    update_job_status "$JOB_ID" "running" 30 "检查域名冲突"
    log_info "步骤3: 检查域名冲突"
    
    if [ -d "/www/wwwroot/$domain" ]; then
        log_error "域名目录已存在: /www/wwwroot/$domain"
        update_job_status "$JOB_ID" "failed" 30 "检查域名冲突" "域名目录已存在"
        return 1
    fi
    
    # 步骤4: 创建临时配置文件
    update_job_status "$JOB_ID" "running" 40 "创建配置文件"
    log_info "步骤4: 创建配置文件"
    
    local temp_config=$(mktemp)
    echo "$domain $db_name $db_user $db_pass $email" > "$temp_config"
    
    log_info "配置信息: 域名=$domain, 数据库=$db_name, 用户=$db_user, 邮箱=$email"
    
    # 步骤5: 执行原始部署脚本
    update_job_status "$JOB_ID" "running" 50 "执行部署脚本"
    log_info "步骤5: 执行原始部署脚本"
    
    local deploy_output
    local deploy_exit_code
    
    # 切换到原始脚本目录并执行
    (
        cd "$(dirname "$ORIGINAL_SCRIPT")"
        "$ORIGINAL_SCRIPT" < "$temp_config"
    ) 2>&1
    deploy_exit_code=$?
    
    # 清理临时文件
    rm -f "$temp_config"
    
    if [ $deploy_exit_code -ne 0 ]; then
        log_error "原始部署脚本执行失败，退出码: $deploy_exit_code"
        update_job_status "$JOB_ID" "failed" 50 "执行部署脚本" "部署脚本执行失败"
        return 1
    fi
    
    # 步骤6: 验证部署结果
    update_job_status "$JOB_ID" "running" 70 "验证部署结果"
    log_info "步骤6: 验证部署结果"
    
    # 检查网站目录
    if [ ! -d "/www/wwwroot/$domain" ]; then
        log_error "网站目录未创建: /www/wwwroot/$domain"
        update_job_status "$JOB_ID" "failed" 70 "验证部署结果" "网站目录未创建"
        return 1
    fi
    
    # 检查WordPress配置文件
    if [ ! -f "/www/wwwroot/$domain/wp-config.php" ]; then
        log_error "WordPress配置文件不存在"
        update_job_status "$JOB_ID" "failed" 70 "验证部署结果" "WordPress配置文件不存在"
        return 1
    fi
    
    # 步骤7: 后处理优化
    update_job_status "$JOB_ID" "running" 85 "后处理优化"
    log_info "步骤7: 后处理优化"
    
    # 设置正确的文件权限
    chown -R www:www "/www/wwwroot/$domain" 2>/dev/null || true
    find "/www/wwwroot/$domain" -type d -exec chmod 755 {} \; 2>/dev/null || true
    find "/www/wwwroot/$domain" -type f -exec chmod 644 {} \; 2>/dev/null || true
    
    # 步骤8: 健康检查
    update_job_status "$JOB_ID" "running" 95 "健康检查"
    log_info "步骤8: 健康检查"
    
    # 简单的HTTP检查
    local http_status=$(curl -s -o /dev/null -w "%{http_code}" "http://$domain" --max-time 10 --connect-timeout 5 2>/dev/null || echo "000")
    
    if [ "$http_status" = "200" ] || [ "$http_status" = "301" ] || [ "$http_status" = "302" ]; then
        log_success "HTTP检查通过: $http_status"
    else
        log_error "HTTP检查失败: $http_status"
        # 不作为致命错误，继续完成部署
    fi
    
    # 步骤9: 完成部署
    update_job_status "$JOB_ID" "completed" 100 "部署完成"
    log_success "域名 $domain 部署完成"
    
    return 0
}

# 执行批量部署
execute_batch_deploy() {
    local job_info="$1"
    
    log_info "开始批量部署"
    
    # 获取子任务
    local sub_jobs=$(php -r "
    define('WP_DEPLOY_MANAGER', true);
    require_once '$PROJECT_ROOT/config/config.php';
    
    try {
        \$db = getDatabase();
        
        \$stmt = \$db->prepare('SELECT * FROM deploy_sub_jobs WHERE parent_job_id = ? ORDER BY id');
        \$stmt->execute([$JOB_ID]);
        \$subJobs = \$stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(\$subJobs);
    } catch (Exception \$e) {
        echo '[]';
    }
    ")
    
    local total_jobs=$(echo "$sub_jobs" | jq length)
    local completed_jobs=0
    local failed_jobs=0
    
    log_info "总共需要部署 $total_jobs 个域名"
    
    # 处理每个子任务
    echo "$sub_jobs" | jq -c '.[]' | while read -r sub_job; do
        local sub_job_id=$(echo "$sub_job" | jq -r '.id')
        local domain=$(echo "$sub_job" | jq -r '.domain')
        local template_id=$(echo "$sub_job" | jq -r '.template_id')
        
        log_info "处理子任务: $domain (ID: $sub_job_id)"
        
        # 获取模板信息
        local template_info=$(get_template_info "$template_id")
        
        if [ -z "$template_info" ] || [ "$template_info" = "null" ]; then
            log_error "模板信息获取失败: $template_id"
            failed_jobs=$((failed_jobs + 1))
            continue
        fi
        
        # 创建临时任务来处理单个域名
        local temp_job_info=$(echo "$job_info" | jq --arg domain "$domain" '.domain = $domain')
        
        # 执行单域名部署
        if execute_single_deploy "$temp_job_info" "$template_info"; then
            completed_jobs=$((completed_jobs + 1))
            log_success "子任务完成: $domain"
        else
            failed_jobs=$((failed_jobs + 1))
            log_error "子任务失败: $domain"
        fi
        
        # 更新主任务进度
        local progress=$(( (completed_jobs + failed_jobs) * 100 / total_jobs ))
        update_job_status "$JOB_ID" "running" "$progress" "已完成: $completed_jobs, 失败: $failed_jobs"
    done
    
    # 确定最终状态
    local final_status="completed"
    if [ $failed_jobs -gt 0 ]; then
        if [ $completed_jobs -eq 0 ]; then
            final_status="failed"
        else
            final_status="partial"
        fi
    fi
    
    update_job_status "$JOB_ID" "$final_status" 100 "批量部署完成"
    log_success "批量部署完成: 成功 $completed_jobs, 失败 $failed_jobs"
    
    return 0
}

# 主执行函数
main() {
    log_info "开始执行部署任务: $JOB_ID"
    
    # 获取任务信息
    local job_info=$(get_job_info "$JOB_ID")
    
    if [ -z "$job_info" ] || [ "$job_info" = "null" ]; then
        log_error "任务信息获取失败: $JOB_ID"
        exit 1
    fi
    
    local job_type=$(echo "$job_info" | jq -r '.type')
    local template_id=$(echo "$job_info" | jq -r '.template_id')
    
    log_info "任务类型: $job_type"
    
    # 获取模板信息（单域名部署需要）
    local template_info=""
    if [ "$job_type" = "single" ]; then
        template_info=$(get_template_info "$template_id")
        
        if [ -z "$template_info" ] || [ "$template_info" = "null" ]; then
            log_error "模板信息获取失败: $template_id"
            update_job_status "$JOB_ID" "failed" 0 "获取模板信息" "模板信息获取失败"
            exit 1
        fi
    fi
    
    # 根据任务类型执行相应的部署
    case "$job_type" in
        "single")
            execute_single_deploy "$job_info" "$template_info"
            ;;
        "batch")
            execute_batch_deploy "$job_info"
            ;;
        *)
            log_error "未知的任务类型: $job_type"
            update_job_status "$JOB_ID" "failed" 0 "验证任务类型" "未知的任务类型"
            exit 1
            ;;
    esac
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "部署任务完成: $JOB_ID"
    else
        log_error "部署任务失败: $JOB_ID"
    fi
    
    exit $exit_code
}

# 错误处理
trap 'log_error "脚本执行被中断"; update_job_status "$JOB_ID" "failed" 0 "脚本中断" "脚本执行被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
