/**
 * 日志管理模块
 */

class LogsManager {
    constructor() {
        this.currentFilters = {};
        this.init();
    }

    init() {
        this.loadLogs();
        this.setupEventListeners();
    }

    setupEventListeners() {
        const exportBtn = document.querySelector('[onclick="exportLogs()"]');
        if (exportBtn) {
            exportBtn.onclick = () => this.exportLogs();
        }

        const clearBtn = document.querySelector('[onclick="clearLogs()"]');
        if (clearBtn) {
            clearBtn.onclick = () => this.clearLogs();
        }

        const filterBtn = document.querySelector('[onclick="filterLogs()"]');
        if (filterBtn) {
            filterBtn.onclick = () => this.filterLogs();
        }
    }

    async loadLogs(filters = {}) {
        try {
            const response = await api.getLogs(filters);
            const data = response.data;
            
            this.renderLogs(data.logs || []);
            
        } catch (error) {
            console.error('Failed to load logs:', error);
            app.showNotification('加载日志失败', 'error');
        }
    }

    renderLogs(logs) {
        const container = document.getElementById('log-viewer');
        if (!container) return;

        if (logs.length === 0) {
            container.innerHTML = `
                <div class="empty-logs">
                    <div class="empty-icon">📝</div>
                    <h3>暂无日志</h3>
                    <p>没有找到符合条件的日志记录</p>
                </div>
            `;
            return;
        }

        const html = `
            <div class="log-entries">
                ${logs.map(log => this.renderLogEntry(log)).join('')}
            </div>
        `;

        container.innerHTML = html;
    }

    renderLogEntry(log) {
        const levelClass = log.level.toLowerCase();
        const time = app.formatDateTime(log.created_at);
        
        return `
            <div class="log-entry log-${levelClass}">
                <div class="log-header">
                    <span class="log-level level-${levelClass}">${log.level}</span>
                    <span class="log-time">${time}</span>
                    <span class="log-source">${log.source || 'System'}</span>
                </div>
                <div class="log-message">${this.escapeHtml(log.message)}</div>
                ${log.context ? `
                    <div class="log-context">
                        <details>
                            <summary>详细信息</summary>
                            <pre>${JSON.stringify(JSON.parse(log.context), null, 2)}</pre>
                        </details>
                    </div>
                ` : ''}
            </div>
        `;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    filterLogs() {
        const filters = {
            level: document.getElementById('log-level-filter')?.value || '',
            start_date: document.getElementById('log-start-date')?.value || '',
            end_date: document.getElementById('log-end-date')?.value || '',
            search: document.getElementById('log-search')?.value || ''
        };

        this.currentFilters = filters;
        this.loadLogs(filters);
    }

    async exportLogs() {
        try {
            const format = 'json'; // 可以扩展为用户选择
            
            app.showNotification('正在导出日志...', 'info');
            
            const response = await api.exportLogs({
                ...this.currentFilters,
                format
            });
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(response.data, null, 2)], {
                type: 'application/json'
            });
            
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `system_logs_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            app.showNotification('日志导出成功', 'success');
            
        } catch (error) {
            console.error('Export logs failed:', error);
            app.showNotification('导出日志失败', 'error');
        }
    }

    async clearLogs() {
        const days = prompt('请输入要保留的天数（删除更早的日志）:', '30');
        
        if (!days || isNaN(days) || days < 1) {
            app.showNotification('请输入有效的天数', 'warning');
            return;
        }

        if (!confirm(`确定要删除 ${days} 天前的日志吗？此操作不可恢复。`)) {
            return;
        }

        try {
            const response = await api.cleanupLogs({ days: parseInt(days) });
            
            app.showNotification(`已删除 ${response.data.deleted_count} 条日志记录`, 'success');
            this.loadLogs(this.currentFilters);
            
        } catch (error) {
            console.error('Clear logs failed:', error);
            app.showNotification('清理日志失败', 'error');
        }
    }
}

// 全局函数
function filterLogs() {
    if (window.logsManager) {
        window.logsManager.filterLogs();
    }
}

function exportLogs() {
    if (window.logsManager) {
        window.logsManager.exportLogs();
    }
}

function clearLogs() {
    if (window.logsManager) {
        window.logsManager.clearLogs();
    }
}

// 创建全局实例
window.logsManager = new LogsManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LogsManager;
}
