# 🚀 火山引擎版产品优化器 - 快速开始

## 📁 文件结构
```
product_optimizer_volcengine_final/
├── product_optimizer_volcengine_simple.py  # 主脚本 (核心文件)
├── README_complete.md                      # 完整使用文档
├── QUICK_START.md                         # 快速开始指南 (本文件)
├── run_background.sh                      # 后台运行脚本
├── config_template.py                     # 配置模板
├── input/                                 # 输入目录
│   └── example_products.txt              # 示例产品文件
├── output/                               # 输出目录 (自动创建)
└── logs/                                 # 日志目录 (自动创建)
```

## ⚡ 5分钟快速开始

### 1️⃣ 配置API密钥 (必须)
编辑 `product_optimizer_volcengine_simple.py` 第13行:
```python
VOLC_API_KEY = "your_volcengine_api_key_here"  # 改为您的API密钥
```

### 2️⃣ 准备产品文件
将您的产品文件放入 `input/` 目录，每行一个产品名称:
```
Apple iPhone 15 Pro Max 256GB
Samsung Galaxy S24 Ultra 512GB
Sony WH-1000XM5 Headphones
```

### 3️⃣ 运行脚本
```bash
# 交互模式 (推荐新手)
python3 product_optimizer_volcengine_simple.py

# 非交互模式 (推荐批量处理)
python product_optimizer_volcengine_simple.py \
  --input input/your_products.txt \
  --output output/results.csv \
  --lang auto \
  --non-interactive
```

### 4️⃣ 查看结果
结果文件保存在 `output/` 目录中。

## 🌍 欧洲小语种使用

### 法语产品优化
```bash
python product_optimizer_volcengine_simple.py \
  --input input/french_products.txt \
  --output output/french_results.csv \
  --lang fr \
  --non-interactive
```

### 德语产品优化
```bash
python product_optimizer_volcengine_simple.py \
  --input input/german_products.txt \
  --output output/german_results.csv \
  --lang de \
  --non-interactive
```

### 意大利语产品优化
```bash
python product_optimizer_volcengine_simple.py \
  --input input/italian_products.txt \
  --output output/italian_results.csv \
  --lang it \
  --non-interactive
```

## 🔧 后台运行

### Linux/Mac 后台运行
```bash
# 使用提供的脚本
chmod +x run_background.sh
./run_background.sh

# 或者直接命令
nohup python product_optimizer_volcengine_simple.py \
  --input input/products.txt \
  --output output/results.csv \
  --lang auto \
  --non-interactive \
  --log logs/process.log &
```

### Windows 后台运行
```cmd
start /B python product_optimizer_volcengine_simple.py ^
  --input input/products.txt ^
  --output output/results.csv ^
  --lang auto ^
  --non-interactive ^
  --log logs/process.log
```

## 📊 支持的语言 (36种)

### 欧洲语言
```
en - English          es - Spanish         fr - French
de - German           it - Italian         pt - Portuguese
nl - Dutch            pl - Polish          ru - Russian
sv - Swedish          da - Danish          no - Norwegian
fi - Finnish          cs - Czech           hu - Hungarian
ro - Romanian         bg - Bulgarian       hr - Croatian
sk - Slovak           sl - Slovenian       et - Estonian
lv - Latvian          lt - Lithuanian      mt - Maltese
ga - Irish            cy - Welsh
```

### 其他语言
```
zh - Chinese          ja - Japanese        ko - Korean
ar - Arabic           he - Hebrew          tr - Turkish
hi - Hindi            th - Thai            vi - Vietnamese
auto - 自动检测
```

## ⚙️ Auto模式配置

脚本会根据文件大小自动选择最优配置:
- **小文件** (<1,000行): 2线程, 保守配置
- **中文件** (1,000-10,000行): 6线程, 平衡配置  
- **大文件** (>10,000行): 12线程, 高性能配置

## 🆘 常见问题

### Q: API密钥在哪里获取?
A: 登录火山引擎控制台，在API管理中创建密钥。

### Q: 支持哪些文件格式?
A: 输入支持TXT格式，输出支持CSV和TXT格式。

### Q: 如何处理大文件?
A: 使用Auto模式，脚本会自动优化配置。支持优雅终止。

### Q: 如何查看处理进度?
A: 使用 `--log` 参数，然后 `tail -f logs/process.log` 查看实时进度。

## 📚 更多信息

- 📖 **完整文档**: 查看 `README_complete.md`
- 🔧 **配置模板**: 查看 `config_template.py`
- 🐛 **问题反馈**: 检查日志文件

## 🎉 开始使用

1. 修改API密钥
2. 放入产品文件到 `input/` 目录
3. 运行 `python product_optimizer_volcengine_simple.py`
4. 查看 `output/` 目录中的结果

**就是这么简单！** 🎯✨
