#!/bin/bash

# WordPress站点监控和管理系统
# 提供健康检查、性能监控、批量管理功能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR/../.."
CONFIG_DIR="$PROJECT_ROOT/config"
LOGS_DIR="$PROJECT_ROOT/logs/monitoring"
DATA_DIR="$PROJECT_ROOT/data"

# 创建监控日志目录
mkdir -p "$LOGS_DIR"

# 加载配置
source "$SCRIPT_DIR/logger.sh" 2>/dev/null || {
    log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_warning() { echo "[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
}

# 监控配置
MONITORING_LOG="$LOGS_DIR/monitoring_$(date +%Y%m%d).log"
ALERT_LOG="$LOGS_DIR/alerts_$(date +%Y%m%d).log"
PERFORMANCE_LOG="$LOGS_DIR/performance_$(date +%Y%m%d).log"

# 站点健康检查
check_site_health() {
    local domain="$1"
    local detailed="${2:-false}"
    local health_score=0
    local max_score=10
    
    log_info "检查站点健康状况: $domain" | tee -a "$MONITORING_LOG"
    
    # 1. HTTP响应检查 (2分)
    local http_status=$(curl -s -o /dev/null -w "%{http_code}" "http://$domain" --max-time 10)
    local https_status=$(curl -s -o /dev/null -w "%{http_code}" "https://$domain" --max-time 10)
    
    if [ "$http_status" = "200" ] || [ "$http_status" = "301" ] || [ "$http_status" = "302" ]; then
        health_score=$((health_score + 1))
        [ "$detailed" = "true" ] && log_success "HTTP响应正常: $http_status"
    else
        [ "$detailed" = "true" ] && log_warning "HTTP响应异常: $http_status"
    fi
    
    if [ "$https_status" = "200" ]; then
        health_score=$((health_score + 1))
        [ "$detailed" = "true" ] && log_success "HTTPS响应正常"
    else
        [ "$detailed" = "true" ] && log_warning "HTTPS响应异常: $https_status"
    fi
    
    # 2. 响应时间检查 (2分)
    local response_time=$(curl -s -o /dev/null -w "%{time_total}" "https://$domain" --max-time 10)
    if (( $(echo "$response_time < 3.0" | bc -l) )); then
        health_score=$((health_score + 2))
        [ "$detailed" = "true" ] && log_success "响应时间良好: ${response_time}s"
    elif (( $(echo "$response_time < 5.0" | bc -l) )); then
        health_score=$((health_score + 1))
        [ "$detailed" = "true" ] && log_warning "响应时间一般: ${response_time}s"
    else
        [ "$detailed" = "true" ] && log_warning "响应时间过慢: ${response_time}s"
    fi
    
    # 3. SSL证书检查 (2分)
    local ssl_expiry=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null | grep notAfter | cut -d= -f2)
    if [ -n "$ssl_expiry" ]; then
        local expiry_timestamp=$(date -d "$ssl_expiry" +%s 2>/dev/null)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [ $days_until_expiry -gt 30 ]; then
            health_score=$((health_score + 2))
            [ "$detailed" = "true" ] && log_success "SSL证书有效: ${days_until_expiry}天后过期"
        elif [ $days_until_expiry -gt 7 ]; then
            health_score=$((health_score + 1))
            [ "$detailed" = "true" ] && log_warning "SSL证书即将过期: ${days_until_expiry}天"
        else
            [ "$detailed" = "true" ] && log_error "SSL证书即将过期: ${days_until_expiry}天" | tee -a "$ALERT_LOG"
        fi
    else
        [ "$detailed" = "true" ] && log_warning "无法获取SSL证书信息"
    fi
    
    # 4. WordPress检查 (2分)
    local webroot="/www/wwwroot/$domain"
    if [ -f "$webroot/wp-config.php" ]; then
        health_score=$((health_score + 1))
        
        # 检查数据库连接
        if wp --path="$webroot" db check --allow-root >/dev/null 2>&1; then
            health_score=$((health_score + 1))
            [ "$detailed" = "true" ] && log_success "WordPress数据库连接正常"
        else
            [ "$detailed" = "true" ] && log_warning "WordPress数据库连接异常"
        fi
    else
        [ "$detailed" = "true" ] && log_warning "WordPress配置文件不存在"
    fi
    
    # 5. 磁盘空间检查 (2分)
    local disk_usage=$(df "$webroot" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        health_score=$((health_score + 2))
        [ "$detailed" = "true" ] && log_success "磁盘空间充足: ${disk_usage}%"
    elif [ "$disk_usage" -lt 90 ]; then
        health_score=$((health_score + 1))
        [ "$detailed" = "true" ] && log_warning "磁盘空间紧张: ${disk_usage}%"
    else
        [ "$detailed" = "true" ] && log_error "磁盘空间不足: ${disk_usage}%" | tee -a "$ALERT_LOG"
    fi
    
    # 计算健康分数百分比
    local health_percentage=$((health_score * 100 / max_score))
    
    # 记录结果
    echo "$(date '+%Y-%m-%d %H:%M:%S'),$domain,$health_score,$max_score,$health_percentage,$response_time" >> "$PERFORMANCE_LOG"
    
    if [ $health_percentage -ge 80 ]; then
        [ "$detailed" = "true" ] && log_success "站点健康状况良好: $domain ($health_percentage%)"
        return 0
    elif [ $health_percentage -ge 60 ]; then
        [ "$detailed" = "true" ] && log_warning "站点健康状况一般: $domain ($health_percentage%)"
        return 1
    else
        [ "$detailed" = "true" ] && log_error "站点健康状况差: $domain ($health_percentage%)" | tee -a "$ALERT_LOG"
        return 2
    fi
}

# 批量健康检查
batch_health_check() {
    local domains_file="$DATA_DIR/enhanced_domains.csv"
    local report_file="$LOGS_DIR/health_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "开始批量健康检查"
    
    local total_sites=0
    local healthy_sites=0
    local warning_sites=0
    local critical_sites=0
    
    # 创建报告头部
    cat << EOF > "$report_file"
WordPress站点健康检查报告
========================
检查时间: $(date)

站点详情:
--------
EOF
    
    # 检查每个站点
    tail -n +2 "$domains_file" | while IFS=',' read -r domain template_type industry language region custom_config priority keywords; do
        [[ -z "$domain" || "$domain" =~ ^# ]] && continue
        
        total_sites=$((total_sites + 1))
        
        check_site_health "$domain" "false"
        local health_result=$?
        
        case $health_result in
            0)
                healthy_sites=$((healthy_sites + 1))
                echo "✓ $domain - 健康" >> "$report_file"
                ;;
            1)
                warning_sites=$((warning_sites + 1))
                echo "⚠ $domain - 警告" >> "$report_file"
                ;;
            2)
                critical_sites=$((critical_sites + 1))
                echo "✗ $domain - 严重" >> "$report_file"
                ;;
        esac
    done
    
    # 添加统计信息
    cat << EOF >> "$report_file"

统计摘要:
--------
总站点数: $total_sites
健康站点: $healthy_sites
警告站点: $warning_sites
严重问题: $critical_sites
健康率: $(( healthy_sites * 100 / total_sites ))%
EOF
    
    log_info "健康检查完成，报告已保存: $report_file"
}

# 性能监控
monitor_performance() {
    local domain="$1"
    local webroot="/www/wwwroot/$domain"
    
    log_info "监控站点性能: $domain"
    
    # 1. 页面加载时间
    local load_time=$(curl -s -o /dev/null -w "%{time_total}" "https://$domain")
    
    # 2. 页面大小
    local page_size=$(curl -s "https://$domain" | wc -c)
    
    # 3. HTTP头信息
    local headers=$(curl -s -I "https://$domain")
    local server=$(echo "$headers" | grep -i "server:" | cut -d' ' -f2-)
    local cache_control=$(echo "$headers" | grep -i "cache-control:" | cut -d' ' -f2-)
    
    # 4. WordPress特定指标
    local wp_version=""
    local plugin_count=0
    local theme_name=""
    
    if [ -f "$webroot/wp-config.php" ]; then
        wp_version=$(wp --path="$webroot" core version --allow-root 2>/dev/null)
        plugin_count=$(wp --path="$webroot" plugin list --status=active --format=count --allow-root 2>/dev/null)
        theme_name=$(wp --path="$webroot" theme list --status=active --field=name --allow-root 2>/dev/null)
    fi
    
    # 记录性能数据
    cat << EOF >> "$PERFORMANCE_LOG"
$(date '+%Y-%m-%d %H:%M:%S'),$domain,load_time,$load_time,page_size,$page_size,wp_version,$wp_version,plugins,$plugin_count,theme,$theme_name
EOF
    
    log_success "性能监控完成: $domain (加载时间: ${load_time}s, 页面大小: ${page_size}字节)"
}

# 批量更新WordPress
batch_update_wordpress() {
    local domains_file="$DATA_DIR/enhanced_domains.csv"
    local update_log="$LOGS_DIR/updates_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "开始批量更新WordPress" | tee -a "$update_log"
    
    tail -n +2 "$domains_file" | while IFS=',' read -r domain template_type industry language region custom_config priority keywords; do
        [[ -z "$domain" || "$domain" =~ ^# ]] && continue
        
        local webroot="/www/wwwroot/$domain"
        
        if [ -f "$webroot/wp-config.php" ]; then
            log_info "更新WordPress: $domain" | tee -a "$update_log"
            
            # 创建备份
            local backup_dir="/www/backup/auto_update_$(date +%Y%m%d)"
            mkdir -p "$backup_dir"
            tar -czf "$backup_dir/${domain}_$(date +%H%M%S).tar.gz" -C "/www/wwwroot" "$domain"
            
            # 更新WordPress核心
            wp --path="$webroot" core update --allow-root >> "$update_log" 2>&1
            
            # 更新插件
            wp --path="$webroot" plugin update --all --allow-root >> "$update_log" 2>&1
            
            # 更新主题
            wp --path="$webroot" theme update --all --allow-root >> "$update_log" 2>&1
            
            # 清理缓存
            wp --path="$webroot" cache flush --allow-root >> "$update_log" 2>&1
            
            log_success "更新完成: $domain" | tee -a "$update_log"
        else
            log_warning "跳过非WordPress站点: $domain" | tee -a "$update_log"
        fi
    done
    
    log_info "批量更新完成，详细日志: $update_log"
}

# 安全扫描
security_scan() {
    local domain="$1"
    local webroot="/www/wwwroot/$domain"
    local scan_log="$LOGS_DIR/security_scan_${domain}_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "执行安全扫描: $domain" | tee -a "$scan_log"
    
    local security_issues=0
    
    # 1. 检查文件权限
    log_info "检查文件权限..." | tee -a "$scan_log"
    local wrong_permissions=$(find "$webroot" -type f -perm 777 2>/dev/null | wc -l)
    if [ $wrong_permissions -gt 0 ]; then
        security_issues=$((security_issues + wrong_permissions))
        log_warning "发现 $wrong_permissions 个文件权限过于宽松" | tee -a "$scan_log"
    fi
    
    # 2. 检查可疑文件
    log_info "检查可疑文件..." | tee -a "$scan_log"
    local suspicious_files=$(find "$webroot" -name "*.php" -exec grep -l "eval\|base64_decode\|gzinflate" {} \; 2>/dev/null | wc -l)
    if [ $suspicious_files -gt 0 ]; then
        security_issues=$((security_issues + suspicious_files))
        log_warning "发现 $suspicious_files 个可疑PHP文件" | tee -a "$scan_log"
    fi
    
    # 3. 检查WordPress版本
    if [ -f "$webroot/wp-config.php" ]; then
        local wp_version=$(wp --path="$webroot" core version --allow-root 2>/dev/null)
        local latest_version=$(wp --path="$webroot" core check-update --format=json --allow-root 2>/dev/null | jq -r '.[0].version' 2>/dev/null)
        
        if [ "$wp_version" != "$latest_version" ] && [ -n "$latest_version" ]; then
            security_issues=$((security_issues + 1))
            log_warning "WordPress版本过旧: $wp_version (最新: $latest_version)" | tee -a "$scan_log"
        fi
    fi
    
    # 4. 检查插件安全
    if [ -f "$webroot/wp-config.php" ]; then
        local outdated_plugins=$(wp --path="$webroot" plugin list --update=available --format=count --allow-root 2>/dev/null)
        if [ "$outdated_plugins" -gt 0 ]; then
            security_issues=$((security_issues + outdated_plugins))
            log_warning "发现 $outdated_plugins 个过期插件" | tee -a "$scan_log"
        fi
    fi
    
    # 生成安全报告
    if [ $security_issues -eq 0 ]; then
        log_success "安全扫描完成，未发现问题: $domain" | tee -a "$scan_log"
        return 0
    else
        log_warning "安全扫描完成，发现 $security_issues 个问题: $domain" | tee -a "$scan_log"
        echo "$(date '+%Y-%m-%d %H:%M:%S'),$domain,$security_issues" >> "$ALERT_LOG"
        return 1
    fi
}

# 生成监控仪表板
generate_dashboard() {
    local dashboard_file="$LOGS_DIR/dashboard_$(date +%Y%m%d_%H%M%S).html"
    
    log_info "生成监控仪表板: $dashboard_file"
    
    # 统计数据
    local total_sites=$(tail -n +2 "$DATA_DIR/enhanced_domains.csv" | grep -v '^$' | wc -l)
    local today=$(date +%Y%m%d)
    local healthy_count=$(grep ",$today," "$PERFORMANCE_LOG" 2>/dev/null | awk -F',' '$5 >= 80' | wc -l)
    local warning_count=$(grep ",$today," "$PERFORMANCE_LOG" 2>/dev/null | awk -F',' '$5 >= 60 && $5 < 80' | wc -l)
    local critical_count=$(grep ",$today," "$PERFORMANCE_LOG" 2>/dev/null | awk -F',' '$5 < 60' | wc -l)
    
    # 生成HTML仪表板
    cat << EOF > "$dashboard_file"
<!DOCTYPE html>
<html>
<head>
    <title>WordPress站点监控仪表板</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2271b1; color: white; padding: 20px; border-radius: 5px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #f1f1f1; padding: 20px; border-radius: 5px; flex: 1; text-align: center; }
        .healthy { background: #d4edda; }
        .warning { background: #fff3cd; }
        .critical { background: #f8d7da; }
        .chart { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>WordPress站点监控仪表板</h1>
        <p>更新时间: $(date)</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <h3>总站点数</h3>
            <h2>$total_sites</h2>
        </div>
        <div class="stat-card healthy">
            <h3>健康站点</h3>
            <h2>$healthy_count</h2>
        </div>
        <div class="stat-card warning">
            <h3>警告站点</h3>
            <h2>$warning_count</h2>
        </div>
        <div class="stat-card critical">
            <h3>严重问题</h3>
            <h2>$critical_count</h2>
        </div>
    </div>
    
    <h2>最近性能数据</h2>
    <table>
        <tr>
            <th>域名</th>
            <th>健康分数</th>
            <th>响应时间</th>
            <th>检查时间</th>
        </tr>
EOF
    
    # 添加性能数据
    tail -20 "$PERFORMANCE_LOG" 2>/dev/null | while IFS=',' read -r timestamp domain metric value; do
        if [ "$metric" = "load_time" ]; then
            echo "        <tr><td>$domain</td><td>-</td><td>${value}s</td><td>$timestamp</td></tr>" >> "$dashboard_file"
        fi
    done
    
    cat << EOF >> "$dashboard_file"
    </table>
    
    <h2>最近告警</h2>
    <table>
        <tr>
            <th>时间</th>
            <th>域名</th>
            <th>问题描述</th>
        </tr>
EOF
    
    # 添加告警数据
    tail -10 "$ALERT_LOG" 2>/dev/null | while IFS= read -r alert_line; do
        echo "        <tr><td colspan='3'>$alert_line</td></tr>" >> "$dashboard_file"
    done
    
    cat << EOF >> "$dashboard_file"
    </table>
</body>
</html>
EOF
    
    log_success "监控仪表板已生成: $dashboard_file"
}

# 主函数
main() {
    local action="$1"
    local target="$2"
    
    case "$action" in
        "health")
            if [ -n "$target" ]; then
                check_site_health "$target" "true"
            else
                batch_health_check
            fi
            ;;
        "performance")
            if [ -n "$target" ]; then
                monitor_performance "$target"
            else
                log_error "请指定要监控的域名"
                exit 1
            fi
            ;;
        "update")
            batch_update_wordpress
            ;;
        "security")
            if [ -n "$target" ]; then
                security_scan "$target"
            else
                log_error "请指定要扫描的域名"
                exit 1
            fi
            ;;
        "dashboard")
            generate_dashboard
            ;;
        *)
            echo "用法: $0 {health|performance|update|security|dashboard} [域名]"
            echo ""
            echo "命令说明:"
            echo "  health [域名]    - 健康检查（不指定域名则批量检查）"
            echo "  performance 域名 - 性能监控"
            echo "  update          - 批量更新WordPress"
            echo "  security 域名   - 安全扫描"
            echo "  dashboard       - 生成监控仪表板"
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
