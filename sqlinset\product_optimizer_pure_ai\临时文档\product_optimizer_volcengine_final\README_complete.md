# 火山引擎版产品优化器 (完整版)

## 🎯 **回答您的3个关键问题**

### **✅ 1. 语种支持逻辑**
**答案: 现在支持36种语言，包含完整的欧洲语种**

#### **支持的语言列表:**
```
🌍 欧洲语言 (24种):
en - English (英语)          es - Spanish (西班牙语)
fr - French (法语)           de - German (德语)
it - Italian (意大利语)      pt - Portuguese (葡萄牙语)
nl - Dutch (荷兰语)          pl - Polish (波兰语)
ru - Russian (俄语)          sv - Swedish (瑞典语)
da - Danish (丹麦语)         no - Norwegian (挪威语)
fi - Finnish (芬兰语)        cs - Czech (捷克语)
hu - Hungarian (匈牙利语)    ro - Romanian (罗马尼亚语)
bg - Bulgarian (保加利亚语)  hr - Croatian (克罗地亚语)
sk - Slovak (斯洛伐克语)     sl - Slovenian (斯洛文尼亚语)
et - Estonian (爱沙尼亚语)   lv - Latvian (拉脱维亚语)
lt - Lithuanian (立陶宛语)   mt - Maltese (马耳他语)
ga - Irish (爱尔兰语)        cy - Welsh (威尔士语)

🌏 亚洲语言 (6种):
zh - Chinese (中文)          ja - Japanese (日语)
ko - Korean (韩语)           hi - Hindi (印地语)
th - Thai (泰语)             vi - Vietnamese (越南语)

🌍 其他语言 (6种):
ar - Arabic (阿拉伯语)       he - Hebrew (希伯来语)
tr - Turkish (土耳其语)      auto - 自动检测
```

### **✅ 2. Auto模式配置逻辑修复**
**答案: 已修复，现在大文件用更多线程**

#### **修复后的Auto模式配置:**
```
小文件 (<1,000行):   2线程, 10批次, 0.5秒间隔  [保守配置]
中文件 (1,000-10,000行): 6线程, 20批次, 0.3秒间隔  [平衡配置]
大文件 (>10,000行):  12线程, 30批次, 0.2秒间隔  [高性能配置]
```

#### **配置逻辑说明:**
- **小文件**: 数据量少，用少线程避免过度并发
- **中文件**: 平衡性能和稳定性
- **大文件**: 数据量大，用更多线程提高处理速度

### **✅ 3. 各种运行命令**
**答案: 完整的命令使用指南**

## 🚀 **完整运行命令指南**

### **1. 基础运行命令**

#### **交互模式 (推荐新手)**
```bash
# 基础交互模式
python product_optimizer_volcengine_simple.py

# 会逐步提示选择:
# - 输入文件
# - 输出格式 (CSV/TXT)
# - 输出语言 (36种语言)
# - 处理配置 (Auto/自定义)
```

#### **非交互模式 (推荐批量处理)**
```bash
# 基础非交互模式
python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --non-interactive

# 指定语言和格式
python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --format csv \
  --lang auto \
  --non-interactive
```

### **2. 欧洲小语种专用命令**

#### **法语产品优化**
```bash
# 交互模式
python product_optimizer_volcengine_simple.py
# 然后选择: 5. French (法语)

# 非交互模式
python product_optimizer_volcengine_simple.py \
  --input french_products.txt \
  --output french_results.csv \
  --lang fr \
  --non-interactive
```

#### **德语产品优化**
```bash
python product_optimizer_volcengine_simple.py \
  --input german_products.txt \
  --output german_results.csv \
  --lang de \
  --non-interactive
```

#### **意大利语产品优化**
```bash
python product_optimizer_volcengine_simple.py \
  --input italian_products.txt \
  --output italian_results.csv \
  --lang it \
  --non-interactive
```

#### **荷兰语产品优化**
```bash
python product_optimizer_volcengine_simple.py \
  --input dutch_products.txt \
  --output dutch_results.csv \
  --lang nl \
  --non-interactive
```

### **3. 后台运行命令**

#### **基础后台运行**
```bash
# 使用nohup后台运行
nohup python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --lang auto \
  --non-interactive \
  --log process.log &

# 查看进程
ps aux | grep product_optimizer

# 查看实时日志
tail -f process.log
```

#### **欧洲小语种后台运行**
```bash
# 法语后台处理
nohup python product_optimizer_volcengine_simple.py \
  --input french_products.txt \
  --output french_results.csv \
  --lang fr \
  --workers 6 \
  --batch-size 20 \
  --delay 0.3 \
  --non-interactive \
  --log french_process.log &

# 德语后台处理
nohup python product_optimizer_volcengine_simple.py \
  --input german_products.txt \
  --output german_results.csv \
  --lang de \
  --workers 4 \
  --batch-size 15 \
  --delay 0.5 \
  --non-interactive \
  --log german_process.log &
```

#### **大文件后台处理**
```bash
# 大文件高性能处理
nohup python product_optimizer_volcengine_simple.py \
  --input large_products.txt \
  --output large_results.csv \
  --lang auto \
  --workers 12 \
  --batch-size 30 \
  --delay 0.2 \
  --non-interactive \
  --log large_process.log &
```

### **4. 自定义配置命令**

#### **高性能配置**
```bash
python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --workers 12 \
  --batch-size 30 \
  --delay 0.2 \
  --non-interactive
```

#### **稳定配置 (网络不好时)**
```bash
python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --workers 2 \
  --batch-size 10 \
  --delay 1.0 \
  --non-interactive
```

#### **平衡配置**
```bash
python product_optimizer_volcengine_simple.py \
  --input products.txt \
  --output results.csv \
  --workers 6 \
  --batch-size 20 \
  --delay 0.3 \
  --non-interactive
```

### **5. 批量处理命令**

#### **处理多个文件**
```bash
# 处理多个法语文件
for file in french_*.txt; do
  output="output/${file%.txt}_optimized.csv"
  nohup python product_optimizer_volcengine_simple.py \
    --input "$file" \
    --output "$output" \
    --lang fr \
    --non-interactive \
    --log "logs/${file%.txt}.log" &
done

# 处理多种语言文件
python product_optimizer_volcengine_simple.py --input en_products.txt --output en_results.csv --lang en --non-interactive &
python product_optimizer_volcengine_simple.py --input fr_products.txt --output fr_results.csv --lang fr --non-interactive &
python product_optimizer_volcengine_simple.py --input de_products.txt --output de_results.csv --lang de --non-interactive &
```

### **6. 监控和管理命令**

#### **查看运行状态**
```bash
# 查看所有相关进程
ps aux | grep product_optimizer

# 查看进程详情
ps -ef | grep product_optimizer

# 查看系统资源使用
top -p $(pgrep -f product_optimizer)
```

#### **日志监控**
```bash
# 查看实时日志
tail -f process.log

# 查看最近100行日志
tail -n 100 process.log

# 搜索错误日志
grep -i error process.log

# 搜索完成状态
grep -i "处理完成" process.log
```

#### **停止进程**
```bash
# 优雅停止 (推荐)
kill -TERM $(pgrep -f product_optimizer)

# 强制停止
kill -9 $(pgrep -f product_optimizer)

# 停止特定进程
kill PID_NUMBER
```

## 📋 **完整参数说明**

```bash
python product_optimizer_volcengine_simple.py [OPTIONS]

参数说明:
  --input, -i FILE          输入文件路径
  --output, -o FILE         输出文件路径
  --format, -f {csv,txt}    输出格式 (默认: csv)
  --lang, -l LANG           输出语言 (默认: auto)
                           支持: auto, en, zh, es, fr, de, it, pt, nl, pl, ru, sv, da, no, fi, cs, hu, ro, bg, hr, sk, sl, et, lv, lt, mt, ga, cy, ja, ko, ar, he, tr, hi, th, vi
  --workers, -w INT         并发线程数 (1-20)
  --batch-size, -b INT      批次大小 (5-50)
  --delay, -d FLOAT         请求间隔秒数 (0.1-2.0)
  --log FILE                日志文件路径
  --non-interactive         非交互模式
  --help, -h                显示帮助信息
```

## 🎉 **总结**

### **问题解决状态:**
1. ✅ **语种支持**: 36种语言，包含完整欧洲语种
2. ✅ **Auto模式**: 已修复，大文件用更多线程
3. ✅ **运行命令**: 完整的命令使用指南

### **推荐使用方式:**
- 🔰 **新手**: 使用交互模式
- 🚀 **批量处理**: 使用非交互模式 + 后台运行
- 🌍 **欧洲小语种**: 指定具体语言代码
- 📊 **大文件**: 使用Auto模式或高性能配置

**现在完全支持欧洲小语种的后台批量处理！** 🎯✨
