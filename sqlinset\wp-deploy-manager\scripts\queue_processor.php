<?php
/**
 * 队列处理器
 * 后台运行的队列处理服务
 */

// 设置为CLI模式
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

define('WP_DEPLOY_MANAGER', true);
require_once dirname(__DIR__) . '/config/config.php';

class QueueProcessor {
    private $running = true;
    private $queueManager;
    private $logger;
    private $pidFile;
    
    public function __construct() {
        $this->queueManager = new QueueManager();
        $this->logger = new LogManager();
        $this->pidFile = LOGS_PATH . '/queue_processor.pid';
        
        // 注册信号处理器
        pcntl_signal(SIGTERM, [$this, 'handleSignal']);
        pcntl_signal(SIGINT, [$this, 'handleSignal']);
        pcntl_signal(SIGHUP, [$this, 'handleSignal']);
    }
    
    /**
     * 启动队列处理器
     */
    public function start() {
        // 检查是否已经在运行
        if ($this->isRunning()) {
            $this->logger->log('WARNING', 'Queue processor is already running');
            exit(1);
        }
        
        // 写入PID文件
        file_put_contents($this->pidFile, getmypid());
        
        $this->logger->log('INFO', 'Queue processor started', ['pid' => getmypid()]);
        
        // 主循环
        while ($this->running) {
            try {
                // 处理信号
                pcntl_signal_dispatch();
                
                // 检查队列是否暂停
                if ($this->queueManager->isQueuePaused()) {
                    $this->logger->log('DEBUG', 'Queue is paused, waiting...');
                    sleep(10);
                    continue;
                }
                
                // 处理队列
                $processed = $this->queueManager->processQueue();
                
                if ($processed) {
                    $this->logger->log('INFO', 'Queue processed successfully');
                } else {
                    $this->logger->log('DEBUG', 'No jobs to process or max concurrent reached');
                }
                
                // 清理僵尸任务
                $this->queueManager->cleanupZombieJobs();
                
                // 等待一段时间再检查
                sleep(5);
                
            } catch (Exception $e) {
                $this->logger->log('ERROR', 'Queue processor error: ' . $e->getMessage());
                sleep(10); // 出错时等待更长时间
            }
        }
        
        $this->cleanup();
    }
    
    /**
     * 停止队列处理器
     */
    public function stop() {
        $this->running = false;
        $this->logger->log('INFO', 'Queue processor stopping...');
    }
    
    /**
     * 处理系统信号
     */
    public function handleSignal($signal) {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->logger->log('INFO', 'Received termination signal', ['signal' => $signal]);
                $this->stop();
                break;
                
            case SIGHUP:
                $this->logger->log('INFO', 'Received reload signal');
                // 重新加载配置
                break;
        }
    }
    
    /**
     * 检查是否已经在运行
     */
    private function isRunning() {
        if (!file_exists($this->pidFile)) {
            return false;
        }
        
        $pid = trim(file_get_contents($this->pidFile));
        if (!$pid || !is_numeric($pid)) {
            return false;
        }
        
        // 检查进程是否存在
        $result = shell_exec("ps -p $pid -o pid= 2>/dev/null");
        return !empty(trim($result));
    }
    
    /**
     * 清理资源
     */
    private function cleanup() {
        if (file_exists($this->pidFile)) {
            unlink($this->pidFile);
        }
        
        $this->logger->log('INFO', 'Queue processor stopped');
    }
}

// 处理命令行参数
$command = $argv[1] ?? 'start';

switch ($command) {
    case 'start':
        $processor = new QueueProcessor();
        $processor->start();
        break;
        
    case 'stop':
        $pidFile = LOGS_PATH . '/queue_processor.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if ($pid && is_numeric($pid)) {
                posix_kill($pid, SIGTERM);
                echo "Stop signal sent to process $pid\n";
            } else {
                echo "Invalid PID file\n";
                exit(1);
            }
        } else {
            echo "Queue processor is not running\n";
            exit(1);
        }
        break;
        
    case 'restart':
        // 先停止
        $pidFile = LOGS_PATH . '/queue_processor.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if ($pid && is_numeric($pid)) {
                posix_kill($pid, SIGTERM);
                sleep(2); // 等待停止
            }
        }
        
        // 再启动
        $processor = new QueueProcessor();
        $processor->start();
        break;
        
    case 'status':
        $pidFile = LOGS_PATH . '/queue_processor.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if ($pid && is_numeric($pid)) {
                $result = shell_exec("ps -p $pid -o pid= 2>/dev/null");
                if (!empty(trim($result))) {
                    echo "Queue processor is running (PID: $pid)\n";
                    
                    // 显示队列状态
                    $queueManager = new QueueManager();
                    $status = $queueManager->getQueueStatus();
                    
                    echo "Queue Status:\n";
                    echo "  Running jobs: " . ($status['running_jobs'] ?? 0) . "\n";
                    echo "  Pending jobs: " . ($status['queue_length'] ?? 0) . "\n";
                    echo "  Max concurrent: " . ($status['max_concurrent'] ?? 3) . "\n";
                    echo "  Is paused: " . ($queueManager->isQueuePaused() ? 'Yes' : 'No') . "\n";
                } else {
                    echo "Queue processor is not running (stale PID file)\n";
                    unlink($pidFile);
                    exit(1);
                }
            } else {
                echo "Invalid PID file\n";
                exit(1);
            }
        } else {
            echo "Queue processor is not running\n";
            exit(1);
        }
        break;
        
    default:
        echo "Usage: php queue_processor.php {start|stop|restart|status}\n";
        exit(1);
}
