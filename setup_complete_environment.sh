#!/bin/bash

# WordPress Deploy Manager 完整环境搭建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🚀 WordPress Deploy Manager 完整环境搭建${NC}"
echo "=================================================================="
echo ""

# 检查系统要求
echo -e "${BLUE}[1/10]${NC} 检查系统要求..."

# 检查Docker
if command -v docker &> /dev/null; then
    echo -e "   ✅ Docker 已安装: $(docker --version | cut -d' ' -f3)"
else
    echo -e "   ❌ Docker 未安装"
    echo -e "   请访问 https://docs.docker.com/get-docker/ 安装Docker"
    exit 1
fi

# 检查Docker Compose
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
    echo -e "   ✅ Docker Compose 已安装"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
    echo -e "   ✅ Docker Compose (Plugin) 已安装"
else
    echo -e "   ❌ Docker Compose 未安装"
    exit 1
fi

# 检查端口占用
echo -e "${BLUE}[2/10]${NC} 检查端口占用..."
PORTS=(80 3306 8080)
for port in "${PORTS[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "   ⚠️  端口 $port 已被占用"
        read -p "   是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        echo -e "   ✅ 端口 $port 可用"
    fi
done

# 创建项目结构
echo -e "${BLUE}[3/10]${NC} 创建项目结构..."
chmod +x create_local_environment.sh
./create_local_environment.sh

# 创建前端文件
echo -e "${BLUE}[4/10]${NC} 创建前端文件..."
chmod +x create_frontend_files.sh
./create_frontend_files.sh

# 创建JavaScript文件
echo -e "${BLUE}[5/10]${NC} 创建JavaScript文件..."
chmod +x create_javascript_files.sh
./create_javascript_files.sh

# 创建API文件
echo -e "${BLUE}[6/10]${NC} 创建API文件..."
chmod +x create_api_files.sh
./create_api_files.sh

# 创建图片资源
echo -e "${BLUE}[7/10]${NC} 创建图片资源..."
mkdir -p public/assets/images

# 创建简单的logo
cat > public/assets/images/logo.svg << 'EOF'
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" rx="6" fill="#667eea"/>
  <text x="16" y="22" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">WP</text>
</svg>
EOF

# 复制SVG作为PNG（简单处理）
cp public/assets/images/logo.svg public/assets/images/logo.png

# 创建favicon
cp public/assets/images/logo.svg public/assets/images/favicon.ico

echo -e "   ✅ 图片资源创建完成"

# 设置权限
echo -e "${BLUE}[8/10]${NC} 设置文件权限..."
chmod -R 755 .
chmod -R 777 logs uploads
chmod +x *.sh

echo -e "   ✅ 权限设置完成"

# 构建Docker镜像
echo -e "${BLUE}[9/10]${NC} 构建Docker环境..."
echo -e "   正在构建Docker镜像..."
$DOCKER_COMPOSE_CMD build --no-cache

echo -e "   正在启动服务..."
$DOCKER_COMPOSE_CMD up -d

echo -e "   等待服务启动..."
sleep 15

# 检查服务状态
echo -e "   检查服务状态..."
$DOCKER_COMPOSE_CMD ps

# 验证安装
echo -e "${BLUE}[10/10]${NC} 验证安装..."

# 检查Web服务
echo -e "   检查Web服务..."
if curl -s http://localhost >/dev/null; then
    echo -e "   ✅ Web服务正常"
else
    echo -e "   ❌ Web服务异常"
fi

# 检查API服务
echo -e "   检查API服务..."
if curl -s http://localhost/api/ >/dev/null; then
    echo -e "   ✅ API服务正常"
else
    echo -e "   ❌ API服务异常"
fi

# 检查数据库
echo -e "   检查数据库..."
if $DOCKER_COMPOSE_CMD exec -T mysql mysql -u root -pwp_deploy_2024 -e "SELECT 1" >/dev/null 2>&1; then
    echo -e "   ✅ 数据库连接正常"
else
    echo -e "   ❌ 数据库连接异常"
fi

# 创建测试脚本
echo -e "${BLUE}创建测试和管理脚本...${NC}"

# 创建测试脚本
cat > test_installation.sh << 'EOF'
#!/bin/bash

echo "🧪 测试WordPress Deploy Manager安装..."

# 测试Web服务
echo "1. 测试Web服务..."
if curl -s http://localhost | grep -q "WordPress 部署管理系统"; then
    echo "   ✅ Web服务正常"
else
    echo "   ❌ Web服务异常"
fi

# 测试API服务
echo "2. 测试API服务..."
API_RESPONSE=$(curl -s http://localhost/api/)
if echo "$API_RESPONSE" | grep -q "WordPress Deploy Manager API"; then
    echo "   ✅ API服务正常"
    echo "   API响应: $API_RESPONSE"
else
    echo "   ❌ API服务异常"
fi

# 测试数据库
echo "3. 测试数据库..."
if docker-compose exec -T mysql mysql -u wp_deploy -pwp_deploy_pass_2024 wp_deploy_manager -e "SELECT COUNT(*) FROM templates;" >/dev/null 2>&1; then
    echo "   ✅ 数据库正常"
else
    echo "   ❌ 数据库异常"
fi

echo ""
echo "🎉 测试完成！"
EOF

chmod +x test_installation.sh

# 创建管理脚本
cat > manage.sh << 'EOF'
#!/bin/bash

# WordPress Deploy Manager 管理脚本

case "$1" in
    start)
        echo "🚀 启动服务..."
        docker-compose up -d
        ;;
    stop)
        echo "🛑 停止服务..."
        docker-compose down
        ;;
    restart)
        echo "🔄 重启服务..."
        docker-compose restart
        ;;
    logs)
        echo "📋 查看日志..."
        docker-compose logs -f
        ;;
    status)
        echo "📊 服务状态..."
        docker-compose ps
        ;;
    test)
        echo "🧪 运行测试..."
        ./test_installation.sh
        ;;
    clean)
        echo "🧹 清理环境..."
        docker-compose down -v
        docker system prune -f
        ;;
    backup)
        echo "💾 备份数据..."
        docker-compose exec mysql mysqldump -u root -pwp_deploy_2024 wp_deploy_manager > backup_$(date +%Y%m%d_%H%M%S).sql
        echo "备份完成: backup_$(date +%Y%m%d_%H%M%S).sql"
        ;;
    *)
        echo "WordPress Deploy Manager 管理脚本"
        echo ""
        echo "用法: $0 {start|stop|restart|logs|status|test|clean|backup}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动所有服务"
        echo "  stop    - 停止所有服务"
        echo "  restart - 重启所有服务"
        echo "  logs    - 查看服务日志"
        echo "  status  - 查看服务状态"
        echo "  test    - 运行功能测试"
        echo "  clean   - 清理所有数据"
        echo "  backup  - 备份数据库"
        ;;
esac
EOF

chmod +x manage.sh

# 创建README文件
cat > README.md << 'EOF'
# WordPress Deploy Manager - 本地开发环境

## 🎉 安装完成！

您的WordPress Deploy Manager本地开发环境已经成功搭建完成。

## 🌐 访问地址

- **主应用**: http://localhost
- **phpMyAdmin**: http://localhost:8080
- **API文档**: http://localhost/api/

## 🔑 登录信息

### 数据库
- **主机**: localhost:3306
- **数据库**: wp_deploy_manager
- **用户**: wp_deploy
- **密码**: wp_deploy_pass_2024

### phpMyAdmin
- **用户**: root
- **密码**: wp_deploy_2024

## 🛠️ 管理命令

```bash
# 启动服务
./manage.sh start

# 停止服务
./manage.sh stop

# 查看状态
./manage.sh status

# 查看日志
./manage.sh logs

# 运行测试
./manage.sh test

# 备份数据
./manage.sh backup
```

## 🧪 功能测试

运行以下命令测试所有功能：

```bash
./test_installation.sh
```

## 📁 项目结构

```
├── api/                # API端点
├── config/             # 配置文件
├── database/           # 数据库文件
├── docker/             # Docker配置
├── logs/               # 日志文件
├── public/             # Web根目录
├── uploads/            # 上传文件
├── docker-compose.yml  # Docker编排
└── manage.sh           # 管理脚本
```

## 🚀 开始开发

1. 访问 http://localhost 查看主界面
2. 使用 phpMyAdmin 管理数据库
3. 查看 logs/ 目录中的日志文件
4. 修改代码后重启服务生效

## 📞 获取帮助

如果遇到问题，请检查：
1. Docker服务是否正常运行
2. 端口是否被占用
3. 查看服务日志：`./manage.sh logs`

祝您开发愉快！🎉
EOF

echo ""
echo -e "${GREEN}=================================================================="
echo -e "  🎉 WordPress Deploy Manager 环境搭建完成！"
echo -e "=================================================================="
echo -e "📱 主应用:      ${BLUE}http://localhost${NC}"
echo -e "🗄️  phpMyAdmin:  ${BLUE}http://localhost:8080${NC}"
echo -e "🔌 API接口:     ${BLUE}http://localhost/api/${NC}"
echo ""
echo -e "${YELLOW}🔑 数据库信息:${NC}"
echo -e "   主机: localhost:3306"
echo -e "   数据库: wp_deploy_manager"
echo -e "   用户: wp_deploy / 密码: wp_deploy_pass_2024"
echo -e "   Root密码: wp_deploy_2024"
echo ""
echo -e "${YELLOW}🛠️  管理命令:${NC}"
echo -e "   启动服务: ${BLUE}./manage.sh start${NC}"
echo -e "   停止服务: ${BLUE}./manage.sh stop${NC}"
echo -e "   查看状态: ${BLUE}./manage.sh status${NC}"
echo -e "   运行测试: ${BLUE}./test_installation.sh${NC}"
echo ""
echo -e "${GREEN}🚀 现在可以开始使用WordPress Deploy Manager了！${NC}"
echo -e "${GREEN}   访问 http://localhost 开始体验${NC}"
echo ""
