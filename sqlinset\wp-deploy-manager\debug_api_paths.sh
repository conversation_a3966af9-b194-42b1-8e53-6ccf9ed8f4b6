#!/bin/bash

# API路径调试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  API路径调试"
echo "==================================================${NC}"

DOMAIN="wpd.cloudcheckout.shop"

echo "域名: $DOMAIN"
echo "项目目录: $(pwd)"
echo ""

# 测试各种API路径
echo -e "${BLUE}[1/4]${NC} 测试API路径访问..."

# 测试基础API路径
echo "测试 https://$DOMAIN/api/"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/" --max-time 10 2>/dev/null || echo "000")
echo "HTTP状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✓ 基础API路径正常"
    
    # 获取API响应内容
    echo "API响应内容:"
    curl -s "https://$DOMAIN/api/" --max-time 10 2>/dev/null | head -5
else
    echo "✗ 基础API路径异常"
fi

echo ""

# 测试具体的API端点
echo -e "${BLUE}[2/4]${NC} 测试具体API端点..."

API_ENDPOINTS=(
    "deploy.php"
    "templates.php" 
    "status.php"
    "logs.php"
    "settings.php"
    "sse.php"
)

for endpoint in "${API_ENDPOINTS[@]}"; do
    echo "测试 https://$DOMAIN/api/$endpoint"
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/$endpoint" --max-time 10 2>/dev/null || echo "000")
    echo "HTTP状态码: $HTTP_CODE"
    
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "400" ]; then
        echo "✓ $endpoint 可访问"
    else
        echo "✗ $endpoint 不可访问"
    fi
    echo ""
done

# 检查文件结构
echo -e "${BLUE}[3/4]${NC} 检查文件结构..."

echo "public/api/ 目录内容:"
if [ -d "public/api" ]; then
    ls -la public/api/
else
    echo "✗ public/api/ 目录不存在"
fi

echo ""
echo "api/ 目录内容:"
if [ -d "api" ]; then
    ls -la api/
else
    echo "✗ api/ 目录不存在"
fi

# 检查Nginx配置
echo -e "${BLUE}[4/4]${NC} 检查Nginx配置..."

echo "检查Nginx配置文件..."
NGINX_CONF="/www/server/panel/vhost/nginx/$DOMAIN.conf"

if [ -f "$NGINX_CONF" ]; then
    echo "✓ Nginx配置文件存在: $NGINX_CONF"
    
    # 检查API路由配置
    if grep -q "location /api/" "$NGINX_CONF"; then
        echo "✓ 找到API路由配置"
        echo "API路由配置:"
        grep -A 3 "location /api/" "$NGINX_CONF"
    else
        echo "✗ 未找到API路由配置"
    fi
    
    # 检查root路径
    echo ""
    echo "Root路径配置:"
    grep "root " "$NGINX_CONF" | head -1
    
else
    echo "✗ Nginx配置文件不存在"
fi

echo ""
echo -e "${GREEN}=================================================="
echo "  调试完成！"
echo "=================================================="

# 生成修复建议
echo -e "${YELLOW}修复建议:${NC}"

if [ "$HTTP_CODE" != "200" ]; then
    echo "1. 检查Nginx配置中的API路由"
    echo "2. 确保public/api/index.php文件存在"
    echo "3. 检查文件权限"
    echo "4. 重启Nginx服务"
fi

echo ""
echo "如果API路径仍有问题，请检查:"
echo "- Nginx错误日志: /www/wwwlogs/$DOMAIN.error.log"
echo "- PHP错误日志: /var/log/php_errors.log"
echo "- 系统日志: logs/system_*.log"

exit 0
