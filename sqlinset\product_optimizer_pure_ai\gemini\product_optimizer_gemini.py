#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer_gemini.py Gemini版本产品优化器
使用Google Gemini 1.5 Flash API，保持核心功能完全不变
15 RPM免费速率，持续免费使用
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
from concurrent.futures import ThreadPoolExecutor, as_completed

# Google Gemini API配置 - 简化配置，只支持gemini-1.5-flash
GEMINI_API_KEY = "AIzaSyD6gGFh03s-oUaiIVn9CIlyaFsHPYxDnF8"  # 更新的API密钥
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# 路径配置
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/output"

# 并发配置 - 针对Gemini 1.5 Flash (15 RPM)优化
MAX_WORKERS = 1  # 单线程，确保不超过15 RPM限制
BATCH_SIZE = 8   # 每批8个产品
REQUEST_DELAY = 4.5  # 4.5秒延迟，确保不超过15 RPM (60/15 = 4秒，加安全边际)
TIMEOUT_SECONDS = 90
MAX_RETRIES = 4

# 速率限制保护
RATE_LIMIT_BUFFER = 0.5  # 额外缓冲时间
MIN_REQUEST_INTERVAL = 4.0  # 最小请求间隔

# 语言配置
LANGUAGE_CONFIG = {
    "en": {"locale": "en-US", "name": "英语(美式)"},
    "fr": {"locale": "fr-FR", "name": "法语"},
    "es": {"locale": "es-ES", "name": "西班牙语"},
    "de": {"locale": "de-DE", "name": "德语"},
    "auto": {"locale": "auto", "name": "自动检测"}
}

# 统计锁和速率控制
stats_lock = threading.Lock()
rate_limit_lock = threading.Lock()  # 速率限制锁
last_request_time = 0  # 上次请求时间

processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_products": 0,
    "failed_products": 0,
    "network_errors": 0,
    "timeout_errors": 0,
    "rate_limit_errors": 0,
    "total_requests": 0,
    "avg_request_interval": 0
}

def ai_optimize_products_gemini(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """使用Gemini Pro的AI产品优化"""
    batch_num, products_batch = batch_data
    lang_config = LANGUAGE_CONFIG.get(target_lang, LANGUAGE_CONFIG["auto"])
    lang_name = lang_config["name"]
    
    # 与原版完全相同的提示词 - 保持核心功能不变
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: {lang_name}
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    for attempt in range(max_retries):
        try:
            # 严格的Gemini速率限制保护
            with rate_limit_lock:
                global last_request_time
                current_time = time.time()

                # 计算需要等待的时间
                time_since_last = current_time - last_request_time
                min_interval = REQUEST_DELAY + RATE_LIMIT_BUFFER

                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    print(f"   🕐 速率限制保护: 等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)

                # 额外随机延迟，避免突发请求
                extra_delay = random.uniform(0.2, 0.8)
                time.sleep(extra_delay)

                # 更新请求时间
                last_request_time = time.time()

                # 统计请求
                with stats_lock:
                    processing_stats["total_requests"] += 1
            
            # Gemini API请求格式 - 简化配置
            data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 40,
                    "topP": 0.8,
                    "maxOutputTokens": 4000
                }
            }

            # 发送请求到Gemini API - 使用标准格式
            response = requests.post(
                f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=TIMEOUT_SECONDS
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析Gemini响应
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                candidate = result['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content']:
                    content = candidate['content']['parts'][0]['text'].strip()
                    
                    try:
                        # 清理JSON内容 - 与原版相同的处理逻辑
                        clean_content = content.strip()
                        
                        # 移除markdown标记
                        if "```json" in clean_content:
                            start_idx = clean_content.find("```json") + 7
                            end_idx = clean_content.find("```", start_idx)
                            if end_idx > start_idx:
                                clean_content = clean_content[start_idx:end_idx].strip()
                        
                        # 解析JSON
                        parsed_result = json.loads(clean_content)
                        
                        # 提取结果数组
                        if isinstance(parsed_result, dict) and "results" in parsed_result:
                            results_array = parsed_result["results"]
                        elif isinstance(parsed_result, list):
                            results_array = parsed_result
                        else:
                            results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                        
                        # 清理和优化结果 - 与原版相同的逻辑
                        cleaned_results = []
                        for result in results_array:
                            # 清理SEO标题中的管道符
                            seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                            
                            # 获取分类
                            category = result.get("category", "")
                            
                            # 优化关键词
                            tags = result.get("tags", "")
                            tags = optimize_tags(tags)
                            
                            cleaned_result = {
                                "seo_name": seo_name,
                                "category": category,
                                "tags": tags
                            }
                            cleaned_results.append(cleaned_result)
                        
                        # 更新统计
                        with stats_lock:
                            processing_stats["completed_batches"] += 1
                            processing_stats["successful_products"] += len(cleaned_results)
                            
                            # 显示进度
                            completed = processing_stats["completed_batches"]
                            total = processing_stats["total_batches"]
                            elapsed = time.time() - processing_stats["start_time"]
                            
                            if completed > 0:
                                rate = completed / elapsed
                                eta = (total - completed) / rate if rate > 0 else 0
                                progress = (completed / total) * 100
                                
                                print(f"✅ 批次 {batch_num} 完成 | 进度: {progress:.1f}% ({completed}/{total}) | ETA: {eta/60:.1f}分钟")
                        
                        return batch_num, cleaned_results
                        
                    except json.JSONDecodeError as e:
                        print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                        if attempt == max_retries - 1:
                            return batch_num, generate_basic_results(products_batch)
            
        except requests.exceptions.RequestException as e:
            # 统计网络错误
            with stats_lock:
                if "429" in str(e) or "rate" in str(e).lower():
                    processing_stats["rate_limit_errors"] += 1
                elif "timeout" in str(e).lower():
                    processing_stats["timeout_errors"] += 1
                else:
                    processing_stats["network_errors"] += 1
            
            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                # 如果是速率限制错误，延迟更长时间
                if "429" in str(e) or "rate" in str(e).lower() or "quota" in str(e).lower():
                    delay = 10 * (attempt + 1)  # 速率限制时延迟更长
                    print(f"   � 速率限制触发，等待 {delay} 秒...")
                    print(f"   💡 建议: 降低并发线程数或增加延迟时间")
                elif "timeout" in str(e).lower():
                    delay = 3 ** attempt
                else:
                    delay = 2 ** attempt
                time.sleep(delay)
        except Exception as e:
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    # 所有尝试失败
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_products"] += len(products_batch)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    return batch_num, generate_basic_results(products_batch)

def optimize_tags(tags):
    """优化关键词质量 - 与原版相同"""
    if not tags:
        return ""
    
    # 分割并清理关键词
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    
    # 限制数量和长度
    optimized_tags = []
    for tag in tag_list[:2]:  # 最多2个
        # 限制单个关键词长度
        if len(tag) <= 25:
            optimized_tags.append(tag)
    
    result = ", ".join(optimized_tags)
    
    # 总长度控制
    if len(result) > 30:
        result = optimized_tags[0] if optimized_tags else ""
    
    return result

def generate_basic_results(products_batch):
    """生成基础结果（备用方案） - 与原版相同"""
    results = []
    
    for product in products_batch:
        # 简单的产品分析
        words = product.split()
        brand = words[0] if words else "Unknown"
        
        results.append({
            "seo_name": product.replace("|", "-"),
            "category": "General > Products",
            "tags": f"{brand} products"
        })
    
    return results

def is_background_mode():
    """检测是否为后台运行模式"""
    import sys
    import os

    # 检查是否在nohup或后台运行
    if not sys.stdin.isatty() or not sys.stdout.isatty():
        return True

    # 检查环境变量
    if os.getenv('NOHUP') or os.getenv('BACKGROUND_MODE'):
        return True

    # 检查父进程
    try:
        import psutil
        parent = psutil.Process().parent()
        if parent and 'nohup' in parent.name().lower():
            return True
    except:
        pass

    return False

def detect_optimal_workers_gemini():
    """为Gemini 1.5 Flash检测最优配置 - 15 RPM限制"""
    import multiprocessing

    cpu_count = multiprocessing.cpu_count()
    background = is_background_mode()

    print(f"🖥️ 系统资源检测:")
    print(f"   CPU核心数: {cpu_count}")
    if background:
        print(f"   运行模式: 后台模式")

    # Gemini 1.5 Flash: 15 RPM限制
    print(f"🔄 Gemini 1.5 Flash限制: 15 RPM (每分钟15次请求)")
    print(f"⚠️ 速率限制策略: 单线程 + 4.5秒延迟")

    # 严格限制：只使用1个线程确保不超限
    recommended = 1
    level = "Gemini 1.5 Flash配置"

    print(f"   推荐配置: {level} ({recommended} 线程)")
    print(f"   💡 策略: 确保不超过15 RPM，持续免费使用")
    print(f"   📊 预估速度: 约10-12产品/分钟")

    return recommended

def process_file_in_chunks(output_path, products, chunk_size, max_workers, batch_size, output_format, target_lang):
    """自动分片处理大文件"""
    total_products = len(products)
    total_chunks = (total_products + chunk_size - 1) // chunk_size

    print(f"\n🔄 启动自动分片处理:")
    print(f"   总产品数: {total_products:,}")
    print(f"   分片大小: {chunk_size:,} 产品/片")
    print(f"   分片数量: {total_chunks} 片")
    print(f"   预估总时间: {(total_products / 10) / 60:.1f} 小时")

    # 创建临时目录
    temp_dir = os.path.join(os.path.dirname(output_path), "temp_chunks")
    os.makedirs(temp_dir, exist_ok=True)

    all_results = []
    successful_chunks = 0

    try:
        for chunk_idx in range(total_chunks):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, total_products)
            chunk_products = products[start_idx:end_idx]

            print(f"\n{'='*60}")
            print(f"🔄 处理分片 {chunk_idx + 1}/{total_chunks}")
            print(f"   产品范围: {start_idx + 1}-{end_idx}")
            print(f"   产品数量: {len(chunk_products)}")
            print(f"{'='*60}")

            # 处理当前分片
            chunk_results = process_chunk(chunk_products, max_workers, batch_size, target_lang, chunk_idx + 1)

            if chunk_results:
                all_results.extend(chunk_results)
                successful_chunks += 1
                print(f"✅ 分片 {chunk_idx + 1} 处理完成")

                # 保存中间结果
                temp_file = os.path.join(temp_dir, f"chunk_{chunk_idx + 1:03d}.json")
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(chunk_results, f, ensure_ascii=False, indent=2)
            else:
                print(f"❌ 分片 {chunk_idx + 1} 处理失败")

            # 显示总体进度
            overall_progress = ((chunk_idx + 1) / total_chunks) * 100
            processed_products = len(all_results)
            print(f"📊 总体进度: {overall_progress:.1f}% ({processed_products}/{total_products} 产品)")

        # 保存最终结果
        if all_results:
            save_chunk_results(all_results, output_path, output_format)

            print(f"\n🎉 分片处理完成!")
            print(f"📊 成功分片: {successful_chunks}/{total_chunks}")
            print(f"📊 处理产品: {len(all_results)}/{total_products}")
            print(f"📁 输出文件: {output_path}")

            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)

            return True
        else:
            print(f"❌ 所有分片处理失败")
            return False

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断处理")
        print(f"📊 已处理: {len(all_results)} 个产品")
        if all_results:
            save_chunk_results(all_results, output_path, output_format)
            print(f"💾 部分结果已保存到: {output_path}")
        return False
    except Exception as e:
        print(f"❌ 分片处理异常: {e}")
        return False

def process_chunk(products_chunk, max_workers, batch_size, target_lang, chunk_num):
    """处理单个分片"""
    # 准备批次数据
    batch_data = []
    for i in range(0, len(products_chunk), batch_size):
        batch_num = (i // batch_size) + 1
        batch = products_chunk[i:i+batch_size]
        batch_data.append((batch_num, batch))

    print(f"   批次数量: {len(batch_data)}")

    # 重置统计
    global processing_stats
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0
    processing_stats["start_time"] = time.time()

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_gemini, batch, target_lang): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                print(f"   ❌ 分片{chunk_num} 批次{batch_num} 异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    return final_results

def save_chunk_results(results, output_path, output_format):
    """保存分片处理结果"""
    try:
        if output_format == "csv":
            with open(output_path, 'w', encoding='utf-8-sig') as f:
                f.write("seo_name,category,tags\n")
                for result in results:
                    seo_name = result.get("seo_name", "").replace('"', '""')
                    category = result.get("category", "").replace('"', '""')
                    tags = result.get("tags", "").replace('"', '""')
                    f.write(f'"{seo_name}","{category}","{tags}"\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

        return True
    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def process_file_gemini(input_path, output_path, max_workers, batch_size, output_format, target_lang="auto"):
    """使用Gemini的文件处理 - 保持与原版相同的逻辑"""
    print(f"🤖 Gemini版产品优化器")

    # 处理绝对路径
    if not os.path.isabs(input_path):
        input_path = os.path.abspath(input_path)

    print(f"📁 输入文件: {input_path}")
    print(f"🔧 并发配置: {max_workers} 个线程，{batch_size} 个产品/批次")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")
    print(f"🔄 API类型: Google Gemini 1.5 Flash (15 RPM免费)")

    # 检查文件
    if not os.path.exists(input_path):
        print(f"❌ 错误: 文件不存在 - {input_path}")
        print(f"📂 当前工作目录: {os.getcwd()}")
        return False

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 产品数量: {len(products):,} 个")

    # 大文件自动分片处理策略
    if len(products) >= 5000:
        print(f"🔍 检测到大文件 ({len(products):,} 行)")
        estimated_time = len(products) / 10  # 考虑15 RPM限制，约10产品/分钟
        print(f"⏱️ 预估处理时间: {estimated_time:.1f} 分钟 ({estimated_time/60:.1f} 小时)")

        if len(products) >= 10000:
            background = is_background_mode()

            if background:
                # 后台模式：自动启用分片处理
                print(f"\n🤖 后台模式：自动启用分片处理")
                chunk_size = 3000  # 每片3000行
                return process_file_in_chunks(output_path, products, chunk_size, max_workers, batch_size, output_format, target_lang)
            else:
                # 交互模式：提供选择
                print(f"\n🤖 大文件处理选项:")
                print(f"1. 自动分片处理 (推荐) - 分成小块逐个处理")
                print(f"2. 整体处理 - 一次性处理全部数据")

                try:
                    choice = input("请选择处理方式 (1/2): ").strip()
                except (EOFError, KeyboardInterrupt):
                    # 输入异常时默认使用分片处理
                    choice = "1"

                if choice != "2":
                    # 自动分片处理
                    chunk_size = 3000  # 每片3000行
                    return process_file_in_chunks(output_path, products, chunk_size, max_workers, batch_size, output_format, target_lang)

        # 大文件优化配置
        if len(products) >= 20000:
            print(f"🔧 自动启用大文件优化配置")
            max_workers = 1  # 强制单线程
            batch_size = 6   # 减少批次大小
            print(f"   优化后配置: {max_workers} 线程, {batch_size} 产品/批次")

    # 语言设置
    if target_lang is None:
        target_lang = "en"

    # 设置输出文件
    if output_path is None:
        base_name = os.path.splitext(input_path)[0]
        if output_format == "csv":
            output_path = f"{base_name}_gemini.csv"
        else:
            output_path = f"{base_name}_gemini.txt"

    print(f"📄 输出文件: {output_path}")

    # 计算性能预估
    total_batches = (len(products) + batch_size - 1) // batch_size
    # Gemini 15 RPM限制下的时间估算
    concurrent_time_estimate = (total_batches / max_workers) * 5  # 考虑延迟，约5秒/批次

    print(f"\n📈 Gemini性能预估:")
    print(f"   总批次数: {total_batches}")
    print(f"   API限制: 15 RPM (每分钟15次请求)")
    print(f"   预估时间: {concurrent_time_estimate/60:.1f} 分钟")
    print(f"   处理策略: 速率限制优先")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), batch_size):
        batch_num = (i // batch_size) + 1
        batch = products[i:i+batch_size]
        batch_data.append((batch_num, batch))

    # 初始化统计
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0
    processing_stats["network_errors"] = 0
    processing_stats["timeout_errors"] = 0
    processing_stats["rate_limit_errors"] = 0

    print(f"\n🔄 开始Gemini并发处理 ({len(batch_data)} 个批次，{max_workers} 个线程)...")

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_gemini, batch, target_lang): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                print(f"❌ 批次 {batch_num} 执行异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    # 保存结果 - 与原版相同的格式
    try:
        if output_format == "csv":
            with open(output_path, 'w', encoding='utf-8-sig') as f:
                f.write("seo_name,category,tags\n")
                for result in final_results:
                    seo_name = result.get("seo_name", "").replace('"', '""')
                    category = result.get("category", "").replace('"', '""')
                    tags = result.get("tags", "").replace('"', '""')
                    f.write(f'"{seo_name}","{category}","{tags}"\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in final_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_products"]
        failed = processing_stats["failed_products"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        print(f"\n✅ Gemini处理完成!")
        print(f"📊 成功: {successful:,} | 失败: {failed:,} | 成功率: {success_rate:.1f}%")
        print(f"⏱️ 总时间: {elapsed_time/60:.1f} 分钟")
        print(f"🚀 处理速度: {len(products)/(elapsed_time/60):.0f} 产品/分钟")

        # 显示网络统计
        network_errors = processing_stats.get("network_errors", 0)
        timeout_errors = processing_stats.get("timeout_errors", 0)
        rate_limit_errors = processing_stats.get("rate_limit_errors", 0)

        # 显示详细统计
        total_requests = processing_stats.get("total_requests", 0)
        actual_rpm = (total_requests / elapsed_time) * 60 if elapsed_time > 0 else 0

        print(f"📊 Gemini API统计:")
        print(f"   总请求数: {total_requests}")
        print(f"   实际RPM: {actual_rpm:.1f} (限制: 60)")
        print(f"   平均间隔: {elapsed_time/total_requests:.1f}秒" if total_requests > 0 else "   平均间隔: N/A")

        if network_errors > 0 or timeout_errors > 0 or rate_limit_errors > 0:
            print(f"🌐 网络统计:")
            if rate_limit_errors > 0:
                print(f"   🚫 速率限制错误: {rate_limit_errors} 次")
            if timeout_errors > 0:
                print(f"   ⏰ 超时错误: {timeout_errors} 次")
            if network_errors > 0:
                print(f"   🌐 其他网络错误: {network_errors} 次")

        # 速率限制建议
        if actual_rpm > 55:
            print(f"⚠️ 速率接近限制，建议降低并发或增加延迟")
        elif rate_limit_errors > 0:
            print(f"🚫 检测到速率限制错误，建议使用严格模式")
        else:
            print(f"✅ 速率控制良好，未触发限制")

        print(f"💾 结果保存到: {output_path}")

        return True

    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def process_input_directory_gemini():
    """处理input目录下的所有文件 - Gemini版本"""
    background = is_background_mode()

    if background:
        print("🤖 Gemini版产品优化器 - 后台模式")
        print(f"📂 输入目录: {INPUT_DIR}")
        print(f"📁 输出目录: {OUTPUT_DIR}")
        print(f"🔄 API类型: Google Gemini 1.5 Flash (持续免费)")
        print(f"⏰ 启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("🤖 Gemini版Input目录产品优化器")
        print("="*60)
        print(f"📂 输入目录: {INPUT_DIR}")
        print(f"📁 输出目录: {OUTPUT_DIR}")
        print(f"📋 支持格式: .txt, .csv, .tsv, .dat, .text, 无扩展名文件")
        print(f"🔄 API类型: Google Gemini 1.5 Flash (持续免费)")

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找文件 - 与原版相同的逻辑
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    for item in os.listdir(INPUT_DIR):
        item_path = os.path.join(INPUT_DIR, item)
        if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
            all_files.append(item_path)

    txt_files = sorted(list(set(all_files)))

    if not txt_files:
        print(f"❌ 在 {INPUT_DIR} 目录中未找到支持的文件")
        print(f"💡 请将要处理的文件放入input目录")
        return False

    print(f"\n📋 发现 {len(txt_files)} 个文件:")
    total_size = 0
    for i, file_path in enumerate(txt_files, 1):
        file_size = os.path.getsize(file_path) / (1024*1024)  # MB
        total_size += file_size
        print(f"   {i}. {os.path.basename(file_path)} ({file_size:.1f} MB)")

    print(f"📊 总大小: {total_size:.1f} MB")

    # Gemini优化配置检测
    recommended_workers = detect_optimal_workers_gemini()

    # 选择处理模式
    background = is_background_mode()

    if background:
        # 后台模式：自动处理所有文件
        print(f"\n📋 后台模式：自动处理所有文件")
        mode_choice = "1"
    else:
        # 交互模式：提供选择
        print(f"\n📋 处理模式:")
        print(f"1. 处理所有文件 (推荐)")
        print(f"2. 选择特定文件")

        try:
            mode_choice = input("请选择模式 (1/2): ").strip()
        except (EOFError, KeyboardInterrupt):
            mode_choice = "1"  # 默认处理所有文件

    if mode_choice == "2" and not background:
        print(f"\n请选择要处理的文件 (输入文件编号，多个文件用空格分隔):")
        try:
            file_choice = input("文件编号: ").strip()
            indices = [int(x) - 1 for x in file_choice.split()]
            selected_files = [txt_files[i] for i in indices if 0 <= i < len(txt_files)]
            if not selected_files:
                print("❌ 无效的文件选择")
                return False
            txt_files = selected_files
        except (EOFError, KeyboardInterrupt):
            print("❌ 输入中断，处理所有文件")
        except:
            print("❌ 输入格式错误，处理所有文件")

    # 并发配置 - Gemini 1.5 Flash限制
    if background:
        # 后台模式：使用推荐配置
        print(f"\n🚀 后台模式：使用推荐配置 ({recommended_workers} 线程)")
        config_choice = "1"
    else:
        # 交互模式：提供选择
        print(f"\n🚀 Gemini 1.5 Flash配置:")
        print(f"1. 推荐模式 ({recommended_workers} 线程) - 安全稳定，推荐")
        print(f"2. 自定义配置 - 需要自行控制速率")
        print(f"⚠️ 警告: 超过15 RPM可能导致API被限制")

        try:
            config_choice = input("请选择 (1/2): ").strip()
        except (EOFError, KeyboardInterrupt):
            config_choice = "1"  # 默认使用推荐配置
    if config_choice == "2" and not background:
        try:
            print(f"⚠️ 自定义配置风险提示:")
            print(f"   - 15 RPM = 每4秒最多1次请求")
            print(f"   - 建议: 1线程 + 4.5秒延迟")

            max_workers = int(input("请输入线程数 (1-2): "))
            batch_size = int(input("请输入批次大小 (5-12): "))
            max_workers = max(1, min(2, max_workers))  # 最多2线程
            batch_size = max(5, min(12, batch_size))

            estimated_rpm = max_workers * (60 / REQUEST_DELAY)
            print(f"🔧 自定义配置: {max_workers} 线程, {batch_size} 产品/批次")
            print(f"📊 预估RPM: {estimated_rpm:.1f} (限制: 15)")

            if estimated_rpm > 15:
                print(f"🚫 警告: 预估RPM超过限制，强烈建议降低配置")
        except (EOFError, KeyboardInterrupt):
            print("❌ 输入中断，使用推荐配置")
            max_workers = recommended_workers
            batch_size = BATCH_SIZE
        except:
            print("❌ 输入错误，使用推荐配置")
            max_workers = recommended_workers
            batch_size = BATCH_SIZE
    else:
        max_workers = recommended_workers
        batch_size = BATCH_SIZE

    # 输出格式
    if background:
        # 后台模式：默认使用文本格式
        print(f"\n📋 后台模式：使用文本格式输出")
        output_format = "txt"
    else:
        # 交互模式：提供选择
        print(f"\n📋 选择输出格式:")
        print(f"1. 文本格式 (.txt)")
        print(f"2. CSV格式 (.csv)")

        try:
            format_choice = input("请选择格式 (1/2): ").strip()
            output_format = "csv" if format_choice == "2" else "txt"
        except (EOFError, KeyboardInterrupt):
            output_format = "txt"  # 默认使用文本格式

    # 处理每个文件
    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(txt_files, 1):
        print(f"\n{'='*60}")
        print(f"🔄 处理文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        if output_format == "csv":
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_gemini.csv")
        else:
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_gemini.txt")

        # 处理单个文件
        success = process_file_gemini(input_file, output_file, max_workers, batch_size, output_format, "auto")

        if success:
            success_count += 1
            print(f"✅ 文件 {i} 处理完成")
        else:
            print(f"❌ 文件 {i} 处理失败")

    # 总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print(f"🎉 Gemini处理完成!")
    print(f"📊 成功处理: {success_count}/{len(txt_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"💰 费用: 免费 (Gemini Pro持续免费)")
    print(f"{'='*60}")

    return success_count == len(txt_files)

def main():
    """主函数"""
    print("🤖 Gemini版产品优化器")
    print("="*60)
    print(f"🎯 核心功能 (与原版完全相同):")
    print(f"   ✅ 完全AI智能分析 (无硬编码)")
    print(f"   ✅ 支持任何产品类型")
    print(f"   ✅ 智能分类生成 (1-4级)")
    print(f"   ✅ SEO标题优化")
    print(f"   ✅ 精准关键词生成")
    print(f"   ✅ 并发处理提速")
    print(f"🔄 API优势:")
    print(f"   ✅ Google Gemini 1.5 Flash")
    print(f"   ✅ 15 RPM 免费速率")
    print(f"   ✅ 持续免费使用")
    print(f"   ✅ 无Token总量限制")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")

    # 命令行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "auto":
            print("\n🤖 自动模式: 处理所有文件")
            # 使用Gemini推荐配置自动处理
            recommended_workers = detect_optimal_workers_gemini()

            # 查找并处理文件
            os.makedirs(INPUT_DIR, exist_ok=True)
            os.makedirs(OUTPUT_DIR, exist_ok=True)

            supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
            all_files = []

            for pattern in supported_extensions:
                file_pattern = os.path.join(INPUT_DIR, pattern)
                found_files = glob.glob(file_pattern)
                all_files.extend(found_files)

            for item in os.listdir(INPUT_DIR):
                item_path = os.path.join(INPUT_DIR, item)
                if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
                    all_files.append(item_path)

            txt_files = sorted(list(set(all_files)))

            if not txt_files:
                print(f"❌ 在 {INPUT_DIR} 目录中未找到文件")
                return

            print(f"📋 发现 {len(txt_files)} 个文件，开始Gemini自动处理...")

            for i, input_file in enumerate(txt_files, 1):
                print(f"\n🔄 处理文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")

                base_name = os.path.splitext(os.path.basename(input_file))[0]
                output_file = os.path.join(OUTPUT_DIR, f"{base_name}_gemini.txt")

                # 检查文件大小，自动决定是否分片
                try:
                    with open(input_file, 'r', encoding='utf-8') as f:
                        products = [line.strip() for line in f if line.strip()]

                    if len(products) >= 10000:
                        print(f"🤖 自动启用分片处理 ({len(products):,} 行)")
                        chunk_size = 3000
                        process_file_in_chunks(output_file, products, chunk_size, recommended_workers, BATCH_SIZE, "txt", "auto")
                    else:
                        process_file_gemini(input_file, output_file, recommended_workers, BATCH_SIZE, "txt", "auto")
                except Exception as e:
                    print(f"❌ 处理文件 {input_file} 失败: {e}")
                    continue
        else:
            print("❌ 无效参数")
            print("💡 支持的参数: auto - 自动处理所有文件")
            return
    else:
        # 交互模式
        process_input_directory_gemini()

if __name__ == "__main__":
    main()
