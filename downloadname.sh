#!/bin/bash

# 检查参数是否为空
if [ $# -ne 2 ]; then
    echo "Usage: $0 <image_links_file> <target_folder>"
    exit 1
fi

# 获取输入参数
image_links_file="$1"
target_folder="$2"

# 创建目标文件夹
mkdir -p "$target_folder/www/imagerdown/imagedownload"
echo "Directory created: $target_folder/www/imagerdown/imagedownload"

# 定义下载函数
download_image() {
    url="$1"
    
    # 提取图片路径并生成新文件名
    file_path=$(echo "$url" | grep -oP '(?<=/)[^/]+(?=/[^/]+$)' | tr '/' '-') # 提取 URL 中路径段并用 "-" 连接
    filename=$(basename "$url")                                              # 提取原文件名
    new_filename="${file_path}-${filename}"                                 # 合成新文件名

    # 检查文件是否已经存在
    if [ -f "$target_folder/www/imagerdown/imagedownload/$new_filename" ]; then
        echo "File $new_filename already exists, skipping download."
        return
    fi

    # 添加随机延迟，范围为 1 到 3 秒
    #sleep_time=$((RANDOM % 3 + 1))
    #sleep "$sleep_time"

    # 下载图片
    curl -o "$target_folder/www/imagerdown/imagedownload/$new_filename" "$url"
    echo "Downloaded: $new_filename"
}

export -f download_image

# 使用 GNU Parallel 执行多线程下载
parallel -j 5 download_image :::: "$image_links_file"
