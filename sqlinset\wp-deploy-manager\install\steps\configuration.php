<div class="configuration-content">
    <h2>系统配置</h2>
    <p>请配置系统的基本参数</p>
    
    <form method="post" class="configuration-form">
        <!-- MySQL配置 -->
        <div class="config-section">
            <h3>数据库配置</h3>
            <div class="form-group">
                <label for="mysql_root_password">MySQL Root 密码 *</label>
                <input type="password" id="mysql_root_password" name="mysql_root_password" required>
                <small class="form-help">用于创建部署管理系统的数据库</small>
            </div>
        </div>
        
        <!-- 部署配置 -->
        <div class="config-section">
            <h3>部署配置</h3>
            <div class="form-group">
                <label for="max_concurrent_jobs">最大并发任务数</label>
                <select id="max_concurrent_jobs" name="max_concurrent_jobs">
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3" selected>3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                </select>
                <small class="form-help">同时运行的最大部署任务数</small>
            </div>
            
            <div class="form-group">
                <label for="default_timeout">默认超时时间（秒）</label>
                <select id="default_timeout" name="default_timeout">
                    <option value="900">15分钟</option>
                    <option value="1800" selected>30分钟</option>
                    <option value="3600">1小时</option>
                    <option value="7200">2小时</option>
                </select>
                <small class="form-help">单个部署任务的超时时间</small>
            </div>
        </div>
        
        <!-- 通知配置 -->
        <div class="config-section">
            <h3>通知配置</h3>
            <div class="checkbox-group">
                <input type="checkbox" id="enable_notifications" name="enable_notifications">
                <label for="enable_notifications">启用邮件通知</label>
            </div>
            
            <div class="form-group">
                <label for="notification_email">通知邮箱</label>
                <input type="email" id="notification_email" name="notification_email">
                <small class="form-help">接收部署通知的邮箱地址</small>
            </div>
        </div>
        
        <!-- 安全配置 -->
        <div class="config-section">
            <h3>安全配置</h3>
            <div class="checkbox-group">
                <input type="checkbox" id="backup_before_deploy" name="backup_before_deploy" checked>
                <label for="backup_before_deploy">部署前自动备份</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="auto_ssl_setup" name="auto_ssl_setup" checked>
                <label for="auto_ssl_setup">自动SSL证书设置</label>
            </div>
        </div>
        
        <!-- 高级配置 -->
        <div class="config-section">
            <h3>高级配置</h3>
            <div class="form-group">
                <label for="log_retention_days">日志保留天数</label>
                <select id="log_retention_days" name="log_retention_days">
                    <option value="7">7天</option>
                    <option value="30" selected>30天</option>
                    <option value="90">90天</option>
                    <option value="365">1年</option>
                </select>
                <small class="form-help">系统日志的保留时间</small>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="auto_cleanup_logs" name="auto_cleanup_logs" checked>
                <label for="auto_cleanup_logs">自动清理过期日志</label>
            </div>
        </div>
    </form>
    
    <div class="config-preview">
        <h4>配置预览</h4>
        <div class="preview-content">
            <div class="preview-item">
                <strong>数据库：</strong> wp_deploy_manager
            </div>
            <div class="preview-item">
                <strong>用户：</strong> wp_deploy
            </div>
            <div class="preview-item">
                <strong>项目目录：</strong> <?php echo INSTALL_ROOT; ?>
            </div>
            <div class="preview-item">
                <strong>Web目录：</strong> <?php echo INSTALL_ROOT; ?>/public
            </div>
        </div>
    </div>
</div>

<style>
.configuration-content {
    max-width: 600px;
    margin: 0 auto;
}

.configuration-form {
    margin: 30px 0;
}

.config-section {
    margin-bottom: 40px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.config-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 18px;
}

.form-help {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.config-preview {
    margin-top: 30px;
    padding: 20px;
    background: #e8f5e8;
    border-radius: 8px;
    border: 1px solid #d4edda;
}

.config-preview h4 {
    margin-bottom: 15px;
    color: #155724;
}

.preview-content {
    display: grid;
    gap: 10px;
}

.preview-item {
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    font-size: 14px;
}

.preview-item strong {
    color: #2c3e50;
}
</style>

<script>
// 实时预览配置
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.configuration-form');
    const inputs = form.querySelectorAll('input, select');
    
    inputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    function updatePreview() {
        // 这里可以添加实时预览逻辑
        console.log('Configuration updated');
    }
    
    // 验证MySQL密码
    const mysqlPassword = document.getElementById('mysql_root_password');
    mysqlPassword.addEventListener('blur', function() {
        if (this.value.length < 6) {
            this.setCustomValidity('密码长度至少6位');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
