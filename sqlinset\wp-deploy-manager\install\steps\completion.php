<div class="completion-content">
    <div class="success-header">
        <div class="success-icon">🎉</div>
        <h2>安装完成！</h2>
        <p>WordPress 部署管理系统已成功安装并配置完成</p>
    </div>
    
    <div class="completion-summary">
        <div class="summary-card">
            <h3>🌐 访问信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">管理界面：</span>
                    <span class="info-value">
                        <a href="/" target="_blank">http://<?php echo $_SERVER['HTTP_HOST']; ?></a>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">项目目录：</span>
                    <span class="info-value"><?php echo INSTALL_ROOT; ?></span>
                </div>
            </div>
        </div>
        
        <div class="summary-card">
            <h3>🗄️ 数据库信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">数据库名：</span>
                    <span class="info-value">wp_deploy_manager</span>
                </div>
                <div class="info-item">
                    <span class="info-label">用户名：</span>
                    <span class="info-value">wp_deploy</span>
                </div>
                <div class="info-item">
                    <span class="info-label">配置文件：</span>
                    <span class="info-value">config/database.php</span>
                </div>
            </div>
        </div>
        
        <div class="summary-card">
            <h3>📁 重要目录</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">模板存储：</span>
                    <span class="info-value">templates/</span>
                </div>
                <div class="info-item">
                    <span class="info-label">日志目录：</span>
                    <span class="info-value">logs/</span>
                </div>
                <div class="info-item">
                    <span class="info-label">上传目录：</span>
                    <span class="info-value">uploads/</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="next-steps">
        <h3>🚀 下一步操作</h3>
        <div class="steps-list">
            <div class="step-item">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>设置运行目录</h4>
                    <p>在宝塔面板中设置网站运行目录为 <code>/public</code></p>
                    <small>网站设置 → 网站目录 → 运行目录</small>
                </div>
            </div>
            
            <div class="step-item">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>启动队列服务</h4>
                    <p>启动后台队列处理服务</p>
                    <code>systemctl start wp-deploy-queue</code>
                </div>
            </div>
            
            <div class="step-item">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>上传模板</h4>
                    <p>上传第一个WordPress站点模板开始使用</p>
                    <small>支持 .tar.gz 和 .zip 格式</small>
                </div>
            </div>
            
            <div class="step-item">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h4>开始部署</h4>
                    <p>选择模板，输入域名，开始您的第一次部署</p>
                    <small>支持单域名和批量部署</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="security-notice">
        <h3>🔒 安全提醒</h3>
        <div class="notice-content">
            <ul>
                <li>请删除或重命名 <code>install</code> 目录以防止重复安装</li>
                <li>定期备份数据库和配置文件</li>
                <li>监控系统日志，及时发现问题</li>
                <li>保持系统和依赖组件的更新</li>
            </ul>
        </div>
    </div>
    
    <div class="quick-actions">
        <a href="/" class="btn btn-primary btn-lg">
            <i class="icon-home"></i>
            进入管理界面
        </a>
        
        <button type="button" class="btn btn-secondary btn-lg" onclick="copyInstallInfo()">
            <i class="icon-copy"></i>
            复制安装信息
        </button>
        
        <a href="../DEPLOYMENT_GUIDE.md" class="btn btn-outline btn-lg" target="_blank">
            <i class="icon-book"></i>
            查看使用指南
        </a>
    </div>
    
    <div class="support-info">
        <h4>📞 技术支持</h4>
        <p>如果您在使用过程中遇到问题，请：</p>
        <ul>
            <li>查看系统日志：<code>logs/system_*.log</code></li>
            <li>检查队列服务状态：<code>systemctl status wp-deploy-queue</code></li>
            <li>参考部署指南中的故障排除部分</li>
        </ul>
    </div>
</div>

<style>
.completion-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.success-header {
    margin-bottom: 40px;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.success-header h2 {
    color: #28a745;
    margin-bottom: 10px;
}

.success-header p {
    color: #666;
    font-size: 18px;
}

.completion-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 40px 0;
    text-align: left;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-card h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 18px;
}

.info-grid {
    display: grid;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-label {
    font-weight: 500;
    color: #495057;
}

.info-value {
    color: #2c3e50;
    font-family: monospace;
    font-size: 14px;
}

.info-value a {
    color: #007bff;
    text-decoration: none;
}

.info-value a:hover {
    text-decoration: underline;
}

.next-steps {
    margin: 40px 0;
    text-align: left;
}

.next-steps h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

.steps-list {
    display: grid;
    gap: 20px;
}

.step-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    flex-shrink: 0;
}

.step-content h4 {
    margin-bottom: 8px;
    color: #2c3e50;
}

.step-content p {
    margin-bottom: 5px;
    color: #495057;
}

.step-content small {
    color: #6c757d;
    font-style: italic;
}

.step-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
}

.security-notice {
    margin: 40px 0;
    padding: 25px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    text-align: left;
}

.security-notice h3 {
    margin-bottom: 15px;
    color: #856404;
}

.notice-content ul {
    margin: 0;
    padding-left: 20px;
}

.notice-content li {
    margin-bottom: 8px;
    color: #856404;
}

.notice-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}

.quick-actions {
    margin: 40px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.support-info {
    margin: 40px 0;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    text-align: left;
}

.support-info h4 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.support-info ul {
    margin: 15px 0 0 20px;
}

.support-info li {
    margin-bottom: 8px;
    color: #495057;
}

.support-info code {
    background: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}
</style>

<script>
function copyInstallInfo() {
    const info = `WordPress 部署管理系统 - 安装信息

访问地址: http://<?php echo $_SERVER['HTTP_HOST']; ?>
项目目录: <?php echo INSTALL_ROOT; ?>
数据库名: wp_deploy_manager
用户名: wp_deploy
配置文件: config/database.php

重要目录:
- 模板存储: templates/
- 日志目录: logs/
- 上传目录: uploads/

下一步操作:
1. 设置运行目录为 /public
2. 启动队列服务: systemctl start wp-deploy-queue
3. 上传WordPress模板
4. 开始部署站点

安装时间: ${new Date().toLocaleString('zh-CN')}`;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(info).then(() => {
            alert('安装信息已复制到剪贴板');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = info;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('安装信息已复制到剪贴板');
    }
}

// 自动检查网站访问状态
setTimeout(function() {
    fetch('/')
        .then(response => {
            if (response.ok) {
                console.log('Website is accessible');
            }
        })
        .catch(error => {
            console.log('Website check failed:', error);
        });
}, 2000);
</script>
