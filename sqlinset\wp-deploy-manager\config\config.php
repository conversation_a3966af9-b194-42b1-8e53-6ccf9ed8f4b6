<?php
/**
 * WordPress 部署管理系统 - 主配置文件
 */

// 防止直接访问
if (!defined('WP_DEPLOY_MANAGER')) {
    die('Direct access not allowed');
}

// 基础路径配置
define('ROOT_PATH', dirname(__DIR__));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('API_PATH', ROOT_PATH . '/api');
define('CORE_PATH', ROOT_PATH . '/core');
define('DATABASE_PATH', ROOT_PATH . '/database');
define('SCRIPTS_PATH', ROOT_PATH . '/scripts');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// 加载数据库配置
require_once ROOT_PATH . '/config/database.php';

// 上传配置
define('MAX_UPLOAD_SIZE', 500 * 1024 * 1024); // 500MB
define('ALLOWED_EXTENSIONS', ['tar.gz', 'tgz', 'zip']);
define('UPLOAD_TEMP_DIR', UPLOADS_PATH . '/temp');

// 部署配置
define('MAX_CONCURRENT_DEPLOYMENTS', 3);
define('DEPLOYMENT_TIMEOUT', 1800); // 30分钟
define('HEALTH_CHECK_TIMEOUT', 30);

// 原始部署脚本路径
define('ORIGINAL_DEPLOY_SCRIPT', '/www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh');

// 默认配置
define('DEFAULT_MYSQL_ROOT_PASS', '2KHNS4XSsMBLZJ7B');
define('DEFAULT_WEB_USER', 'www');
define('DEFAULT_WEB_GROUP', 'www');

// 安全配置
define('API_SECRET_KEY', 'wp_deploy_' . md5(__DIR__));
define('SESSION_TIMEOUT', 3600); // 1小时

// 日志配置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 10);

// 系统配置
define('TIMEZONE', 'Asia/Shanghai');
define('DATE_FORMAT', 'Y-m-d H:i:s');

// 设置时区
date_default_timezone_set(TIMEZONE);

// 错误报告配置
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 自动加载函数
spl_autoload_register(function ($class) {
    $file = CORE_PATH . '/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 创建必要目录
$directories = [
    LOGS_PATH,
    UPLOADS_PATH,
    UPLOAD_TEMP_DIR,
    TEMPLATES_PATH . '/uploads',
    TEMPLATES_PATH . '/configs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 全局函数
function getConfig($key, $default = null) {
    static $config = null;
    
    if ($config === null) {
        $configFile = ROOT_PATH . '/config/app.json';
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
        } else {
            $config = [];
        }
    }
    
    return isset($config[$key]) ? $config[$key] : $default;
}

function setConfig($key, $value) {
    $configFile = ROOT_PATH . '/config/app.json';
    $config = [];
    
    if (file_exists($configFile)) {
        $config = json_decode(file_get_contents($configFile), true);
    }
    
    $config[$key] = $value;
    file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
}

function logMessage($level, $message, $context = []) {
    $logger = new LogManager();
    $logger->log($level, $message, $context);
}

function generateUUID() {
    return sprintf(
        '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateDomain($domain) {
    return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME);
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function isValidTemplateFile($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if ($extension === 'gz') {
        $basename = pathinfo($filename, PATHINFO_FILENAME);
        $extension = strtolower(pathinfo($basename, PATHINFO_EXTENSION)) . '.gz';
    }
    
    return in_array($extension, ALLOWED_EXTENSIONS);
}

function getSystemInfo() {
    return [
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'disk_free_space' => disk_free_space(ROOT_PATH),
        'disk_total_space' => disk_total_space(ROOT_PATH),
        'load_average' => sys_getloadavg(),
        'memory_usage' => memory_get_usage(true),
        'memory_peak_usage' => memory_get_peak_usage(true)
    ];
}

function checkSystemRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'sqlite_extension' => extension_loaded('pdo_sqlite'),
        'json_extension' => extension_loaded('json'),
        'curl_extension' => extension_loaded('curl'),
        'zip_extension' => extension_loaded('zip'),
        'writable_logs' => is_writable(LOGS_PATH),
        'writable_uploads' => is_writable(UPLOADS_PATH),
        'writable_templates' => is_writable(TEMPLATES_PATH),
        'original_script_exists' => file_exists(ORIGINAL_DEPLOY_SCRIPT),
        'original_script_executable' => is_executable(ORIGINAL_DEPLOY_SCRIPT)
    ];
    
    return $requirements;
}

// 响应函数
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function errorResponse($message, $status = 400, $code = null) {
    jsonResponse([
        'success' => false,
        'error' => $message,
        'code' => $code,
        'timestamp' => date(DATE_FORMAT)
    ], $status);
}

function successResponse($data = null, $message = null) {
    $response = [
        'success' => true,
        'timestamp' => date(DATE_FORMAT)
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    jsonResponse($response);
}

// 安全检查
function checkApiAccess() {
    // 检查请求方法
    $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
    if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
        errorResponse('Method not allowed', 405);
    }
    
    // 检查Content-Type（POST请求）
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                errorResponse('Invalid JSON format');
            }
            $_POST = array_merge($_POST, $input ?: []);
        }
    }
    
    // 基础安全头
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
}

// 初始化数据库
function initDatabase() {
    try {
        $db = getDatabase();

        // 检查表是否存在
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (empty($tables)) {
            // 执行初始化SQL
            $initSql = file_get_contents(DATABASE_PATH . '/init_mysql.sql');
            $db->exec($initSql);
        }

        return $db;
    } catch (Exception $e) {
        logMessage('ERROR', 'Database initialization failed: ' . $e->getMessage());
        throw $e;
    }
}

// 启动时检查数据库连接
try {
    $testDb = getDatabase();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}
