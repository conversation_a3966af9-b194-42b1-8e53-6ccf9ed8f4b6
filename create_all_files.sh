#!/bin/bash

# WordPress Deploy Manager 完整文件创建脚本

echo "🚀 创建WordPress Deploy Manager完整项目文件..."

# 1. 创建Docker配置文件
echo "📦 创建Docker配置..."

# docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: wp-deploy-web
    ports:
      - "80:80"
    volumes:
      - ./:/var/www/html
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/uploads
    depends_on:
      - mysql
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    networks:
      - wp-deploy-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_mysql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - wp-deploy-network
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
    depends_on:
      - mysql
    networks:
      - wp-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  wp-deploy-network:
    driver: bridge
EOF

# Dockerfile
cat > docker/Dockerfile << 'EOF'
FROM php:8.1-apache

WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev libzip-dev \
    zip unzip nano wget && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip xml curl

# 启用Apache模块
RUN a2enmod rewrite

# 复制Apache配置
COPY docker/apache/vhost.conf /etc/apache2/sites-available/000-default.conf

# 设置权限
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80
EOF

# Apache配置
cat > docker/apache/vhost.conf << 'EOF'
<VirtualHost *:80>
    DocumentRoot /var/www/html/public
    ServerName localhost
    
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^api/(.*)$ api/index.php [QSA,L]
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF

echo "✅ Docker配置创建完成"

# 2. 创建配置文件
echo "⚙️ 创建配置文件..."

# 数据库配置
cat > config/database.php << 'EOF'
<?php
// 数据库配置
define('DB_HOST', 'mysql');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', 'wp_deploy_pass_2024');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);

// 数据库连接函数
function getDatabase() {
    static $db = null;
    if ($db === null) {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        $db = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
    }
    return $db;
}
?>
EOF

# 主配置文件
cat > config/config.php << 'EOF'
<?php
// 环境配置
define('ENVIRONMENT', 'development');
define('DEBUG_MODE', true);

// 基础路径配置
define('ROOT_PATH', dirname(__DIR__));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('API_PATH', ROOT_PATH . '/api');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// 应用配置
define('MAX_UPLOAD_SIZE', 100 * 1024 * 1024); // 100MB
define('MAX_CONCURRENT_DEPLOYMENTS', 2);
define('DEPLOYMENT_TIMEOUT', 900); // 15分钟

// 时区配置
define('TIMEZONE', 'Asia/Shanghai');
date_default_timezone_set(TIMEZONE);

// 加载数据库配置
require_once ROOT_PATH . '/config/database.php';

// 日志函数
function logMessage($level, $message) {
    $logFile = LOGS_PATH . '/system_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 响应函数
function successResponse($data = null, $message = 'Success') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

function errorResponse($message = 'Error', $code = 500) {
    http_response_code($code);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}
?>
EOF

echo "✅ 配置文件创建完成"

# 3. 创建数据库初始化文件
echo "🗄️ 创建数据库文件..."

cat > database/init_mysql.sql << 'EOF'
-- WordPress 部署管理系统数据库初始化脚本

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 部署任务表
CREATE TABLE IF NOT EXISTS deploy_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    type ENUM('single', 'batch') NOT NULL,
    template_id INT,
    domains JSON NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认设置
INSERT IGNORE INTO system_settings (setting_key, setting_value) VALUES
('max_concurrent_jobs', '3'),
('default_timeout', '1800'),
('enable_notifications', 'false');

-- 插入测试模板
INSERT IGNORE INTO templates (uuid, name, description, filename, file_path, file_size, file_hash) VALUES
('test-template-1', '测试模板1', '这是一个测试模板', 'test1.tar.gz', '/uploads/templates/test1.tar.gz', 1024000, 'hash1'),
('test-template-2', '测试模板2', '这是另一个测试模板', 'test2.tar.gz', '/uploads/templates/test2.tar.gz', 2048000, 'hash2');
EOF

echo "✅ 数据库文件创建完成"

# 4. 创建前端文件
echo "🎨 创建前端文件..."

# 主HTML文件
cat > public/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/images/logo.png" alt="Logo" class="nav-logo">
                <span class="nav-title">WordPress 部署管理系统</span>
            </div>

            <div class="nav-menu">
                <div class="nav-item active" data-tab="dashboard">
                    <i class="icon-dashboard"></i>
                    <span>仪表板</span>
                </div>
                <div class="nav-item" data-tab="deploy">
                    <i class="icon-deploy"></i>
                    <span>部署管理</span>
                </div>
                <div class="nav-item" data-tab="templates">
                    <i class="icon-template"></i>
                    <span>模板管理</span>
                </div>
                <div class="nav-item" data-tab="monitoring">
                    <i class="icon-monitor"></i>
                    <span>监控中心</span>
                </div>
                <div class="nav-item" data-tab="logs">
                    <i class="icon-logs"></i>
                    <span>日志管理</span>
                </div>
                <div class="nav-item" data-tab="settings">
                    <i class="icon-settings"></i>
                    <span>系统设置</span>
                </div>
            </div>

            <div class="nav-actions">
                <button class="btn btn-icon" id="refresh-btn" title="刷新">
                    <i class="icon-refresh"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 仪表板 -->
        <section id="dashboard-tab" class="tab-content active">
            <div class="page-header">
                <h1>系统仪表板</h1>
                <p>WordPress部署管理系统概览</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-sites">0</div>
                        <div class="stat-label">总站点数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number" id="successful-deploys">0</div>
                        <div class="stat-label">成功部署</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pending-tasks">0</div>
                        <div class="stat-label">待处理任务</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-number" id="active-templates">0</div>
                        <div class="stat-label">活跃模板</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3>系统状态</h3>
                    <div id="system-status">
                        <div class="status-item">
                            <span class="status-label">服务状态</span>
                            <span class="status-value status-running">运行中</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">数据库</span>
                            <span class="status-value status-connected">已连接</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>最近活动</h3>
                    <div id="recent-activity">
                        <div class="activity-item">
                            <span class="activity-time">刚刚</span>
                            <span class="activity-text">系统启动完成</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 部署管理 -->
        <section id="deploy-tab" class="tab-content">
            <div class="page-header">
                <h1>部署管理</h1>
                <p>管理WordPress站点部署</p>
            </div>

            <div class="deploy-actions">
                <button class="btn btn-primary" id="single-deploy-btn">单域名部署</button>
                <button class="btn btn-secondary" id="batch-deploy-btn">批量部署</button>
            </div>

            <div class="deploy-history">
                <h3>部署历史</h3>
                <div id="deploy-history-list">
                    <p>暂无部署记录</p>
                </div>
            </div>
        </section>

        <!-- 模板管理 -->
        <section id="templates-tab" class="tab-content">
            <div class="page-header">
                <h1>模板管理</h1>
                <p>管理WordPress模板文件</p>
            </div>

            <div class="template-actions">
                <button class="btn btn-primary" id="upload-template-btn">上传模板</button>
            </div>

            <div class="templates-list">
                <div id="templates-container">
                    <p>正在加载模板...</p>
                </div>
            </div>
        </section>

        <!-- 监控中心 -->
        <section id="monitoring-tab" class="tab-content">
            <div class="page-header">
                <h1>监控中心</h1>
                <p>系统性能和健康状态监控</p>
            </div>

            <div class="monitoring-grid">
                <div class="monitor-card">
                    <h3>系统资源</h3>
                    <div id="system-resources">
                        <div class="resource-item">
                            <span>CPU使用率</span>
                            <span id="cpu-usage">0%</span>
                        </div>
                        <div class="resource-item">
                            <span>内存使用率</span>
                            <span id="memory-usage">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 日志管理 -->
        <section id="logs-tab" class="tab-content">
            <div class="page-header">
                <h1>日志管理</h1>
                <p>查看系统日志和错误信息</p>
            </div>

            <div class="logs-container">
                <div id="logs-content">
                    <p>正在加载日志...</p>
                </div>
            </div>
        </section>

        <!-- 系统设置 -->
        <section id="settings-tab" class="tab-content">
            <div class="page-header">
                <h1>系统设置</h1>
                <p>配置系统参数和选项</p>
            </div>

            <div class="settings-form">
                <div class="form-group">
                    <label>最大并发任务数</label>
                    <input type="number" id="max-concurrent-jobs" value="3" min="1" max="10">
                </div>
                <div class="form-group">
                    <label>默认超时时间（秒）</label>
                    <input type="number" id="default-timeout" value="1800" min="300" max="3600">
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" id="save-settings-btn">保存设置</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/api.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
EOF

echo "✅ HTML文件创建完成"

echo "🎉 基础文件创建完成！接下来创建CSS和JavaScript文件..."
