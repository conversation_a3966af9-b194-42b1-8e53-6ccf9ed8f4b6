#!/bin/bash

# WordPress 部署管理系统 - 修复Console错误脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  修复Console错误 - 快速诊断和修复"
echo "==================================================${NC}"

# 获取当前目录
CURRENT_DIR="$(pwd)"
echo "项目目录: $CURRENT_DIR"

# 步骤1: 检查关键文件是否存在
echo -e "${BLUE}[1/6]${NC} 检查关键文件..."

# 检查public目录结构
echo "检查public目录结构:"
if [ -d "public" ]; then
    echo "✓ public/ 目录存在"
    
    # 检查assets目录
    if [ -d "public/assets" ]; then
        echo "✓ public/assets/ 目录存在"
        
        # 检查CSS文件
        if [ -f "public/assets/css/main.css" ]; then
            echo "✓ main.css 存在"
        else
            echo "✗ main.css 不存在"
        fi
        
        if [ -f "public/assets/css/components.css" ]; then
            echo "✓ components.css 存在"
        else
            echo "✗ components.css 不存在"
        fi
        
        # 检查JavaScript文件
        JS_FILES=("utils.js" "api.js" "components.js" "dashboard.js" "deploy.js" "templates.js" "monitoring.js" "logs.js" "settings.js" "main.js")
        
        echo "检查JavaScript文件:"
        for js_file in "${JS_FILES[@]}"; do
            if [ -f "public/assets/js/$js_file" ]; then
                echo "✓ $js_file 存在"
            else
                echo "✗ $js_file 不存在"
            fi
        done
        
    else
        echo "✗ public/assets/ 目录不存在"
    fi
    
    # 检查index.html
    if [ -f "public/index.html" ]; then
        echo "✓ index.html 存在"
    else
        echo "✗ index.html 不存在"
    fi
    
else
    echo "✗ public/ 目录不存在"
fi

# 步骤2: 检查API目录
echo -e "${BLUE}[2/6]${NC} 检查API目录..."

if [ -d "public/api" ]; then
    echo "✓ public/api/ 目录存在"
    
    if [ -f "public/api/index.php" ]; then
        echo "✓ public/api/index.php 存在"
    else
        echo "✗ public/api/index.php 不存在"
    fi
else
    echo "✗ public/api/ 目录不存在，正在创建..."
    mkdir -p public/api
fi

# 步骤3: 修复文件权限
echo -e "${BLUE}[3/6]${NC} 修复文件权限..."
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod +x scripts/*.sh 2>/dev/null || true
echo "✓ 文件权限已修复"

# 步骤4: 创建缺失的目录
echo -e "${BLUE}[4/6]${NC} 创建缺失的目录..."
mkdir -p public/assets/css
mkdir -p public/assets/js
mkdir -p public/assets/images
mkdir -p public/api
mkdir -p logs
mkdir -p uploads/temp
mkdir -p templates/uploads
echo "✓ 目录结构已创建"

# 步骤5: 检查Nginx配置
echo -e "${BLUE}[5/6]${NC} 生成Nginx配置建议..."

cat > nginx_config_suggestion.txt << 'EOF'
# Nginx配置建议 - 添加到站点配置中

# 在宝塔面板 -> 网站设置 -> 配置文件中添加以下内容到 server 块中：

# 静态文件缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
}

# API路由
location /api/ {
    try_files $uri $uri/ /api/index.php?$query_string;
}

# 禁止访问敏感目录
location ~ ^/(config|database|logs|scripts|install|core)/ {
    deny all;
    return 404;
}

# 禁止访问敏感文件
location ~ \.(sql|log|sh)$ {
    deny all;
    return 404;
}

# PHP处理
location ~ \.php$ {
    try_files $uri =404;
    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_pass unix:/tmp/php-cgi-81.sock;
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}
EOF

echo "✓ Nginx配置建议已生成: nginx_config_suggestion.txt"

# 步骤6: 测试文件访问
echo -e "${BLUE}[6/6]${NC} 测试文件访问..."

# 获取域名
DOMAIN="wpd.cloudcheckout.shop"

echo "测试关键文件访问:"

# 测试主页
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/" --max-time 10 2>/dev/null || echo "000")
echo "主页 (https://$DOMAIN/): HTTP $HTTP_CODE"

# 测试CSS文件
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/assets/css/main.css" --max-time 10 2>/dev/null || echo "000")
echo "CSS文件: HTTP $HTTP_CODE"

# 测试JS文件
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/assets/js/main.js" --max-time 10 2>/dev/null || echo "000")
echo "JS文件: HTTP $HTTP_CODE"

# 测试API
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/" --max-time 10 2>/dev/null || echo "000")
echo "API接口: HTTP $HTTP_CODE"

echo ""
echo -e "${GREEN}=================================================="
echo "  修复完成！"
echo "=================================================="
echo "下一步操作:"
echo "1. 查看 nginx_config_suggestion.txt 并应用Nginx配置"
echo "2. 在宝塔面板重启Nginx"
echo "3. 清除浏览器缓存后重新访问网站"
echo "4. 如果仍有问题，请运行: ./debug_errors.sh"
echo "==================================================${NC}"

# 显示文件统计
echo -e "${YELLOW}文件统计:${NC}"
echo "CSS文件: $(find public/assets/css -name "*.css" 2>/dev/null | wc -l) 个"
echo "JS文件: $(find public/assets/js -name "*.js" 2>/dev/null | wc -l) 个"
echo "PHP文件: $(find api -name "*.php" 2>/dev/null | wc -l) 个"
echo "总文件: $(find public -type f 2>/dev/null | wc -l) 个"

exit 0
