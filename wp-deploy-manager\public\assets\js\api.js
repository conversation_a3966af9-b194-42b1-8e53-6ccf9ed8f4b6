/**
 * API工具类
 * 处理与后端API的通信
 */

class API {
    constructor() {
        this.baseUrl = '/api';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 发送HTTP请求
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            ...options
        };

        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success === false) {
                throw new Error(data.error || 'API request failed');
            }

            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: data instanceof FormData ? data : JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // ==================== 模板相关API ====================

    /**
     * 获取模板列表
     */
    async getTemplates(filters = {}) {
        return this.get('/templates.php?action=list', filters);
    }

    /**
     * 上传模板
     */
    async uploadTemplate(formData) {
        return this.post('/templates.php?action=upload', formData);
    }

    /**
     * 删除模板
     */
    async deleteTemplate(templateId) {
        return this.delete(`/templates.php?action=delete&id=${templateId}`);
    }

    // ==================== 部署相关API ====================

    /**
     * 单域名部署
     */
    async singleDeploy(domain, templateId, config = {}) {
        return this.post('/deploy.php?action=single_deploy', {
            domain,
            template_id: templateId,
            config
        });
    }

    /**
     * 批量部署
     */
    async batchDeploy(domains, templateId, config = {}) {
        return this.post('/deploy.php?action=batch_deploy', {
            domains,
            template_id: templateId,
            config
        });
    }

    /**
     * 获取部署历史
     */
    async getDeployHistory(filters = {}) {
        return this.get('/deploy.php?action=get_deploy_history', filters);
    }

    /**
     * 获取部署统计
     */
    async getDeployStats(days = 30) {
        return this.get('/deploy.php?action=get_deploy_stats', { days });
    }

    // ==================== 系统状态API ====================

    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        return this.get('/status.php?action=system');
    }

    /**
     * 获取队列状态
     */
    async getQueueStatus() {
        return this.get('/status.php?action=queue');
    }

    // ==================== 设置相关API ====================

    /**
     * 获取系统设置
     */
    async getSettings() {
        return this.get('/settings.php?action=get');
    }

    /**
     * 保存系统设置
     */
    async saveSettings(settings) {
        return this.post('/settings.php?action=save', settings);
    }

    // ==================== 日志相关API ====================

    /**
     * 获取日志
     */
    async getLogs(filters = {}) {
        return this.get('/logs.php?action=get_logs', filters);
    }

    // ==================== 测试API ====================

    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            const response = await this.get('/');
            return {
                success: true,
                message: 'API连接正常',
                data: response
            };
        } catch (error) {
            return {
                success: false,
                message: 'API连接失败',
                error: error.message
            };
        }
    }
}

// 创建全局API实例
window.api = new API();

// 导出API类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
