#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
category_optimizer.py 产品分类优化器
根据产品分类名优化层级结构并生成相关关键词
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
from concurrent.futures import ThreadPoolExecutor, as_completed

# DeepSeek API配置
API_KEY = "***********************************"
API_URL = "https://api.deepseek.com/v1/chat/completions"

# 路径配置
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/category_input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/category_output"

# 并发配置
MAX_WORKERS = 4  # 分类处理相对简单，使用较少线程
BATCH_SIZE = 15  # 每批处理15个分类
REQUEST_DELAY = 0.2
TIMEOUT_SECONDS = 90
MAX_RETRIES = 4

# 统计锁
stats_lock = threading.Lock()
processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_categories": 0,
    "failed_categories": 0,
    "network_errors": 0,
    "timeout_errors": 0
}

def ai_optimize_categories(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """AI分类优化处理"""
    batch_num, categories_batch = batch_data
    
    # 语言配置
    LANGUAGE_CONFIG = {
        "en": {"locale": "en-US", "name": "英语(美式)"},
        "fr": {"locale": "fr-FR", "name": "法语"},
        "es": {"locale": "es-ES", "name": "西班牙语"},
        "de": {"locale": "de-DE", "name": "德语"},
        "auto": {"locale": "auto", "name": "自动检测"}
    }
    
    lang_config = LANGUAGE_CONFIG.get(target_lang, LANGUAGE_CONFIG["auto"])
    lang_name = lang_config["name"]
    
    # 分类优化提示词
    prompt = f"""
You are a professional e-commerce category structure expert. Please analyze and optimize the following product categories:

Category List:
{chr(10).join([f"{i+1}. {category}" for i, category in enumerate(categories_batch)])}

Please complete the following tasks for each category:

1. **Category Level Analysis**:
   - Determine if the provided category is a complete hierarchy or just a partial/leaf category
   - If it's incomplete, build the complete 2-4 level hierarchy based on standard e-commerce practices
   - Reference Amazon/eBay category structures for consistency
   - Ensure logical parent-child relationships

2. **Category Hierarchy Optimization**:
   - For single-level categories: Build complete 2-4 level hierarchy
   - For partial hierarchies: Complete missing parent or child levels
   - For complete hierarchies: Validate and optimize if needed
   - Maintain industry-standard naming conventions

3. **Long-tail Keywords Generation**:
   - Generate 1-2 long-tail keywords specifically related to this category
   - Keywords should be search terms customers would use to find products in this category
   - Focus on category-specific, actionable search phrases
   - Keep total keyword length within 40 characters

Examples:
- Input: "Air Filters" → Output: "Automotive > Engine > Air Filters" + Keywords: "car air filter, engine filtration"
- Input: "Living Room > Sofas" → Output: "Home & Garden > Furniture > Living Room > Sofas" + Keywords: "living room sofa, sectional couch"
- Input: "Electronics > Computers > Laptops > Gaming" → Output: "Electronics > Computers > Laptops > Gaming" + Keywords: "gaming laptop, high performance notebook"

Requirements:
- Output language: {lang_name}
- Build logical, complete category hierarchies (2-4 levels)
- Generate practical, searchable long-tail keywords
- Follow standard e-commerce category naming conventions
- Ensure categories are specific enough to be useful for product placement

Output Format (Strict JSON):
{{
  "results": [
    {{
      "original_category": "Original category name",
      "optimized_hierarchy": "Complete optimized category hierarchy",
      "level_count": 3,
      "optimization_type": "completed/validated/restructured",
      "keywords": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(categories_batch)} categories.
"""
    
    for attempt in range(max_retries):
        try:
            # 随机延迟避免并发冲突
            time.sleep(random.uniform(0, REQUEST_DELAY * 2))
            
            # 请求数据
            data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.8
            }
            
            # 请求头
            headers = {
                'Authorization': f'Bearer {API_KEY}',
                'Content-Type': 'application/json'
            }
            
            # 发送请求
            response = requests.post(
                API_URL,
                json=data,
                headers=headers,
                timeout=TIMEOUT_SECONDS
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()
                
                try:
                    # 清理JSON内容
                    clean_content = content.strip()
                    
                    # 移除markdown标记
                    if "```json" in clean_content:
                        start_idx = clean_content.find("```json") + 7
                        end_idx = clean_content.find("```", start_idx)
                        if end_idx > start_idx:
                            clean_content = clean_content[start_idx:end_idx].strip()
                    
                    # 解析JSON
                    parsed_result = json.loads(clean_content)
                    
                    # 提取结果数组
                    if isinstance(parsed_result, dict) and "results" in parsed_result:
                        results_array = parsed_result["results"]
                    elif isinstance(parsed_result, list):
                        results_array = parsed_result
                    else:
                        results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                    
                    # 清理和优化结果
                    cleaned_results = []
                    for result in results_array:
                        # 优化关键词
                        keywords = result.get("keywords", "")
                        keywords = optimize_category_keywords(keywords)
                        
                        # 验证层级数量
                        hierarchy = result.get("optimized_hierarchy", "")
                        level_count = len(hierarchy.split(" > ")) if hierarchy and " > " in hierarchy else 1
                        
                        cleaned_result = {
                            "original_category": result.get("original_category", ""),
                            "optimized_hierarchy": hierarchy,
                            "level_count": level_count,
                            "optimization_type": result.get("optimization_type", "processed"),
                            "keywords": keywords
                        }
                        cleaned_results.append(cleaned_result)
                    
                    # 更新统计
                    with stats_lock:
                        processing_stats["completed_batches"] += 1
                        processing_stats["successful_categories"] += len(cleaned_results)
                        
                        # 显示进度
                        completed = processing_stats["completed_batches"]
                        total = processing_stats["total_batches"]
                        elapsed = time.time() - processing_stats["start_time"]
                        
                        if completed > 0:
                            rate = completed / elapsed
                            eta = (total - completed) / rate if rate > 0 else 0
                            progress = (completed / total) * 100
                            
                            print(f"✅ 批次 {batch_num} 完成 | 进度: {progress:.1f}% ({completed}/{total}) | ETA: {eta/60:.1f}分钟")
                    
                    return batch_num, cleaned_results
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        return batch_num, generate_basic_category_results(categories_batch)
            
        except requests.exceptions.RequestException as e:
            # 统计网络错误
            with stats_lock:
                if "timeout" in str(e).lower():
                    processing_stats["timeout_errors"] += 1
                else:
                    processing_stats["network_errors"] += 1
            
            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                # 智能延迟：超时错误延迟更长
                delay = 3 ** attempt if "timeout" in str(e).lower() else 2 ** attempt
                time.sleep(delay)
        except Exception as e:
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    # 所有尝试失败
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_categories"] += len(categories_batch)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    return batch_num, generate_basic_category_results(categories_batch)

def optimize_category_keywords(keywords):
    """优化分类关键词质量"""
    if not keywords:
        return ""
    
    # 分割并清理关键词
    keyword_list = [kw.strip() for kw in keywords.split(",") if kw.strip()]
    
    # 限制数量和长度
    optimized_keywords = []
    for keyword in keyword_list[:2]:  # 最多2个
        # 限制单个关键词长度
        if len(keyword) <= 30:
            optimized_keywords.append(keyword)
    
    result = ", ".join(optimized_keywords)
    
    # 总长度控制
    if len(result) > 40:
        result = optimized_keywords[0] if optimized_keywords else ""
    
    return result

def generate_basic_category_results(categories_batch):
    """生成基础分类结果（备用方案）"""
    results = []
    
    for category in categories_batch:
        # 简单的分类分析
        if " > " in category:
            # 已有层级结构
            optimized_hierarchy = category
            level_count = len(category.split(" > "))
            optimization_type = "preserved"
        else:
            # 单级分类，添加通用父级
            optimized_hierarchy = f"General > {category}"
            level_count = 2
            optimization_type = "basic_completion"
        
        # 生成基础关键词
        category_name = category.split(" > ")[-1] if " > " in category else category
        keywords = f"{category_name.lower()}, {category_name.lower()} products"
        
        results.append({
            "original_category": category,
            "optimized_hierarchy": optimized_hierarchy,
            "level_count": level_count,
            "optimization_type": optimization_type,
            "keywords": keywords
        })
    
    return results

def analyze_category_optimization(results):
    """分析分类优化统计"""
    if not results:
        return {"avg_levels": 0, "optimization_types": {}, "keyword_quality": 0}

    total_levels = 0
    optimization_types = {}
    valid_keywords = 0

    for result in results:
        # 统计层级
        level_count = result.get("level_count", 0)
        total_levels += level_count

        # 统计优化类型
        opt_type = result.get("optimization_type", "unknown")
        optimization_types[opt_type] = optimization_types.get(opt_type, 0) + 1

        # 统计关键词质量
        keywords = result.get("keywords", "")
        if keywords and len(keywords) > 5:
            valid_keywords += 1

    return {
        "avg_levels": total_levels / len(results) if results else 0,
        "optimization_types": optimization_types,
        "keyword_quality": (valid_keywords / len(results)) * 100 if results else 0
    }

def process_category_file(input_path, output_path, max_workers, batch_size, output_format, target_lang="auto", simple_output=True):
    """处理单个分类文件"""
    print(f"📁 输入文件: {input_path}")
    print(f"📄 输出文件: {output_path}")

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            categories = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 分类数量: {len(categories):,} 个")

    # 分类文件处理提示
    if len(categories) >= 5000:
        print(f"🔍 检测到大分类文件 ({len(categories):,} 个分类)")
        estimated_time = len(categories) / 100  # 按100分类/分钟估算
        print(f"⏱️ 预估处理时间: {estimated_time:.1f} 分钟")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(categories), batch_size):
        batch_num = (i // batch_size) + 1
        batch = categories[i:i+batch_size]
        batch_data.append((batch_num, batch))

    # 初始化统计
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["completed_batches"] = 0
    processing_stats["successful_categories"] = 0
    processing_stats["failed_categories"] = 0
    processing_stats["network_errors"] = 0
    processing_stats["timeout_errors"] = 0

    print(f"🔄 开始分类优化处理 ({len(batch_data)} 个批次，{max_workers} 个线程)...")

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_categories, batch, target_lang): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                print(f"❌ 批次 {batch_num} 执行异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    # 保存结果
    try:
        if output_format == "csv":
            with open(output_path, 'w', encoding='utf-8-sig') as f:
                if simple_output:
                    f.write("optimized_hierarchy,keywords\n")
                    for result in final_results:
                        hierarchy = result.get("optimized_hierarchy", "").replace('"', '""')
                        keywords = result.get("keywords", "").replace('"', '""')
                        f.write(f'"{hierarchy}","{keywords}"\n')
                else:
                    f.write("original_category,optimized_hierarchy,level_count,optimization_type,keywords\n")
                    for result in final_results:
                        original = result.get("original_category", "").replace('"', '""')
                        hierarchy = result.get("optimized_hierarchy", "").replace('"', '""')
                        level_count = result.get("level_count", 0)
                        opt_type = result.get("optimization_type", "").replace('"', '""')
                        keywords = result.get("keywords", "").replace('"', '""')
                        f.write(f'"{original}","{hierarchy}",{level_count},"{opt_type}","{keywords}"\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                if simple_output:
                    f.write("优化后层级 | 关键词\n")
                    f.write("="*100 + "\n")

                    for result in final_results:
                        hierarchy = result.get("optimized_hierarchy", "")
                        keywords = result.get("keywords", "")
                        f.write(f"{hierarchy} | {keywords}\n")
                else:
                    f.write("原始分类 | 优化后层级 | 层级数 | 优化类型 | 关键词\n")
                    f.write("="*150 + "\n")

                    for result in final_results:
                        original = result.get("original_category", "")
                        hierarchy = result.get("optimized_hierarchy", "")
                        level_count = result.get("level_count", 0)
                        opt_type = result.get("optimization_type", "")
                        keywords = result.get("keywords", "")
                        f.write(f"{original} | {hierarchy} | {level_count} | {opt_type} | {keywords}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_categories"]
        failed = processing_stats["failed_categories"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        # 分析优化质量
        optimization_stats = analyze_category_optimization(final_results)

        print(f"✅ 分类优化完成!")
        print(f"📊 成功: {successful:,} | 失败: {failed:,} | 成功率: {success_rate:.1f}%")
        print(f"⏱️ 耗时: {elapsed_time/60:.1f} 分钟")
        print(f"🚀 速度: {len(categories)/(elapsed_time/60):.0f} 分类/分钟")

        # 显示网络统计
        network_errors = processing_stats.get("network_errors", 0)
        timeout_errors = processing_stats.get("timeout_errors", 0)
        if network_errors > 0 or timeout_errors > 0:
            print(f"🌐 网络统计: 超时错误 {timeout_errors} 次, 其他网络错误 {network_errors} 次")

        # 显示优化统计
        print(f"\n📊 分类优化统计:")
        print(f"   平均层级数: {optimization_stats['avg_levels']:.1f} 级")
        print(f"   关键词质量: {optimization_stats['keyword_quality']:.1f}%")

        print(f"\n🔧 优化类型分布:")
        for opt_type, count in optimization_stats['optimization_types'].items():
            percentage = (count / len(final_results)) * 100
            print(f"   {opt_type}: {count} 个 ({percentage:.1f}%)")

        return True

    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def detect_optimal_workers_for_categories():
    """为分类处理检测最优线程数"""
    import multiprocessing

    cpu_count = multiprocessing.cpu_count()

    print(f"🖥️ 系统资源检测:")
    print(f"   CPU核心数: {cpu_count}")

    # 分类处理相对简单，使用较少线程
    if cpu_count >= 16:
        recommended = 4
        level = "高性能服务器"
    elif cpu_count >= 8:
        recommended = 3
        level = "标准服务器"
    elif cpu_count >= 4:
        recommended = 2
        level = "基础服务器"
    else:
        recommended = 1
        level = "低配置"

    print(f"   推荐配置: {level} ({recommended} 线程)")
    print(f"   💡 分类处理策略: 稳定性优先")

    return recommended

def process_category_directory():
    """处理category_input目录下的所有分类文件"""
    print("🏷️ 分类优化器")
    print("="*60)
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"📋 支持格式: .txt, .csv, .tsv, .dat, .text, 无扩展名文件")

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找分类文件
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    # 查找有扩展名的文件
    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    # 查找无扩展名的文件
    for item in os.listdir(INPUT_DIR):
        item_path = os.path.join(INPUT_DIR, item)
        if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
            all_files.append(item_path)

    category_files = sorted(list(set(all_files)))

    if not category_files:
        print(f"❌ 在 {INPUT_DIR} 目录中未找到支持的文件")
        print(f"💡 请将要处理的分类文件放入category_input目录")
        print(f"📝 文件格式: 每行一个分类名称")
        return False

    print(f"\n📋 发现 {len(category_files)} 个分类文件:")
    total_size = 0
    for i, file_path in enumerate(category_files, 1):
        file_size = os.path.getsize(file_path) / (1024*1024)  # MB
        total_size += file_size
        print(f"   {i}. {os.path.basename(file_path)} ({file_size:.1f} MB)")

    print(f"📊 总大小: {total_size:.1f} MB")

    # 智能检测配置
    recommended_workers = detect_optimal_workers_for_categories()

    # 选择处理模式
    print(f"\n📋 处理模式:")
    print(f"1. 处理所有文件 (推荐)")
    print(f"2. 选择特定文件")

    mode_choice = input("请选择模式 (1/2): ").strip()

    if mode_choice == "2":
        print(f"\n请选择要处理的文件 (输入文件编号，多个文件用空格分隔):")
        file_choice = input("文件编号: ").strip()
        try:
            indices = [int(x) - 1 for x in file_choice.split()]
            selected_files = [category_files[i] for i in indices if 0 <= i < len(category_files)]
            if not selected_files:
                print("❌ 无效的文件选择")
                return False
            category_files = selected_files
        except:
            print("❌ 输入格式错误")
            return False

    # 并发配置
    print(f"\n🚀 并发配置:")
    print(f"1. 智能推荐 ({recommended_workers} 线程) - 稳定优先")
    print(f"2. 自定义配置")

    config_choice = input("请选择 (1/2): ").strip()
    if config_choice == "2":
        try:
            max_workers = int(input("请输入线程数 (1-6): "))
            batch_size = int(input("请输入批次大小 (10-30): "))
            max_workers = max(1, min(6, max_workers))
            batch_size = max(10, min(30, batch_size))
            print(f"🔧 自定义配置: {max_workers} 线程, {batch_size} 分类/批次")
        except:
            max_workers = recommended_workers
            batch_size = BATCH_SIZE
    else:
        max_workers = recommended_workers
        batch_size = BATCH_SIZE

    # 输出格式
    print(f"\n📋 选择输出格式:")
    print(f"1. 文本格式 (.txt)")
    print(f"2. CSV格式 (.csv)")

    format_choice = input("请选择格式 (1/2): ").strip()
    output_format = "csv" if format_choice == "2" else "txt"

    # 输出内容选择
    print(f"\n📋 选择输出内容:")
    print(f"1. 简洁模式 - 只输出优化后层级和关键词 (推荐)")
    print(f"2. 完整模式 - 包含原始分类、层级数、优化类型等")

    content_choice = input("请选择 (1/2): ").strip()
    simple_output = content_choice != "2"

    # 处理每个文件
    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(category_files, 1):
        print(f"\n{'='*60}")
        print(f"🔄 处理文件 {i}/{len(category_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        if output_format == "csv":
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.csv")
        else:
            output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

        # 处理单个文件
        success = process_category_file(input_file, output_file, max_workers, batch_size, output_format, "auto", simple_output)

        if success:
            success_count += 1
            print(f"✅ 文件 {i} 处理完成")
        else:
            print(f"❌ 文件 {i} 处理失败")

    # 总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print(f"🎉 分类优化处理完成!")
    print(f"📊 成功处理: {success_count}/{len(category_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"{'='*60}")

    return success_count == len(category_files)

def main():
    """主函数"""
    print("🏷️ 产品分类优化器")
    print("="*60)
    print(f"🎯 核心功能:")
    print(f"   ✅ 分析分类层级完整性")
    print(f"   ✅ 优化分类层级结构 (2-4级)")
    print(f"   ✅ 生成分类相关长尾关键词")
    print(f"   ✅ 支持批量并发处理")
    print(f"   ✅ 参考Amazon/eBay标准")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")

    # 命令行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "auto":
            print("\n🤖 自动模式: 处理所有分类文件")
            # 使用默认配置自动处理
            recommended_workers = detect_optimal_workers_for_categories()

            # 查找文件并自动处理
            os.makedirs(INPUT_DIR, exist_ok=True)
            os.makedirs(OUTPUT_DIR, exist_ok=True)

            supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
            all_files = []

            for pattern in supported_extensions:
                file_pattern = os.path.join(INPUT_DIR, pattern)
                found_files = glob.glob(file_pattern)
                all_files.extend(found_files)

            for item in os.listdir(INPUT_DIR):
                item_path = os.path.join(INPUT_DIR, item)
                if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
                    all_files.append(item_path)

            category_files = sorted(list(set(all_files)))

            if not category_files:
                print(f"❌ 在 {INPUT_DIR} 目录中未找到分类文件")
                return

            print(f"📋 发现 {len(category_files)} 个文件，开始自动处理...")

            for i, input_file in enumerate(category_files, 1):
                print(f"\n🔄 处理文件 {i}/{len(category_files)}: {os.path.basename(input_file)}")

                base_name = os.path.splitext(os.path.basename(input_file))[0]
                output_file = os.path.join(OUTPUT_DIR, f"{base_name}_optimized.txt")

                process_category_file(input_file, output_file, recommended_workers, BATCH_SIZE, "txt", "auto", True)
        else:
            print("❌ 无效参数")
            print("💡 支持的参数: auto - 自动处理所有文件")
            return
    else:
        # 交互模式
        process_category_directory()

if __name__ == "__main__":
    main()
