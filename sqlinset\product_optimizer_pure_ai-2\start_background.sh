#!/bin/bash
# 后台启动脚本 - 支持nohup运行
# 使用方法: ./start_background.sh

echo "🚀 启动产品优化器后台处理"
echo "="*50

# 检查Python版本
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 未找到Python，请先安装Python"
    exit 1
fi

echo "🐍 使用Python: $PYTHON_CMD"

# 检查脚本文件
if [ ! -f "product_optimizer_input.py" ]; then
    echo "❌ 未找到 product_optimizer_input.py 文件"
    echo "💡 请在脚本所在目录运行此命令"
    exit 1
fi

# 创建输出目录
mkdir -p output

# 启动后台进程
echo "🔄 启动后台进程..."
nohup $PYTHON_CMD product_optimizer_input.py auto > output/processing.log 2>&1 &

# 获取进程ID
PID=$!
echo $PID > output/process.pid

echo "✅ 后台进程已启动"
echo "🆔 进程ID: $PID"
echo "📝 日志文件: output/processing.log"
echo "🔍 进程ID文件: output/process.pid"
echo ""
echo "📋 管理命令:"
echo "   查看日志: tail -f output/processing.log"
echo "   查看进程: ps aux | grep $PID"
echo "   停止进程: kill -15 $PID"
echo "   强制停止: kill -9 $PID"
echo ""
echo "💡 提示: 可以安全关闭终端，进程将继续在后台运行"
