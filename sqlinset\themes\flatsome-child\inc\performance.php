<?php
/**
 * Performance related functions
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}



function flatsome_child_enable_gzip() {
 
}

function flatsome_child_performance_optimizations() {
    if (!is_admin()) {
        wp_dequeue_style('wp-block-library');
        wp_dequeue_style('wp-block-library-theme');
        wp_dequeue_style('wc-block-style'); // Remove WooCommerce block CSS
    }
}
add_action('wp_enqueue_scripts', 'flatsome_child_performance_optimizations', 100);

