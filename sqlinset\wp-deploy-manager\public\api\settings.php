<?php
/**
 * 系统设置API接口
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 安全检查
checkApiAccess();

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get':
            handleGetSettings();
            break;
            
        case 'update':
            handleUpdateSettings();
            break;
            
        case 'reset':
            handleResetSettings();
            break;
            
        case 'test_connection':
            handleTestConnection();
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Settings API error: ' . $e->getMessage());
    errorResponse($e->getMessage(), 500);
}

/**
 * 获取系统设置
 */
function handleGetSettings() {
    try {
        $db = getDatabase();
        
        // 获取数据库中的配置
        $stmt = $db->query("SELECT config_key, config_value, type FROM system_config");
        $dbSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $settings = [];
        foreach ($dbSettings as $setting) {
            $value = $setting['config_value'];
            
            // 根据类型转换值
            switch ($setting['type']) {
                case 'boolean':
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;
                case 'integer':
                    $value = intval($value);
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            $settings[$setting['config_key']] = $value;
        }
        
        // 获取app.json中的配置
        $appConfigFile = ROOT_PATH . '/config/app.json';
        if (file_exists($appConfigFile)) {
            $appConfig = json_decode(file_get_contents($appConfigFile), true);
            if ($appConfig) {
                $settings = array_merge($settings, $appConfig);
            }
        }
        
        // 添加系统信息
        $settings['system_info'] = getSystemInfo();
        $settings['requirements'] = checkSystemRequirements();
        
        successResponse($settings);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 更新系统设置
 */
function handleUpdateSettings() {
    $settings = $_POST;
    
    if (empty($settings)) {
        errorResponse('No settings provided');
    }
    
    try {
        $db = getDatabase();
        $db->beginTransaction();
        
        // 定义可更新的设置
        $allowedSettings = [
            'max_concurrent_jobs' => 'integer',
            'default_timeout' => 'integer',
            'auto_cleanup_logs' => 'boolean',
            'log_retention_days' => 'integer',
            'health_check_interval' => 'integer',
            'enable_notifications' => 'boolean',
            'notification_email' => 'string',
            'backup_before_deploy' => 'boolean',
            'auto_ssl_setup' => 'boolean'
        ];
        
        $stmt = $db->prepare("
            INSERT INTO system_config (config_key, config_value, type) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            config_value = VALUES(config_value),
            type = VALUES(type)
        ");
        
        foreach ($settings as $key => $value) {
            if (isset($allowedSettings[$key])) {
                $type = $allowedSettings[$key];
                
                // 验证和转换值
                switch ($type) {
                    case 'integer':
                        $value = intval($value);
                        if ($key === 'max_concurrent_jobs' && ($value < 1 || $value > 10)) {
                            throw new Exception('Max concurrent jobs must be between 1 and 10');
                        }
                        if ($key === 'default_timeout' && ($value < 300 || $value > 7200)) {
                            throw new Exception('Default timeout must be between 300 and 7200 seconds');
                        }
                        break;
                        
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN) ? 'true' : 'false';
                        break;
                        
                    case 'string':
                        $value = sanitizeInput($value);
                        if ($key === 'notification_email' && !empty($value) && !validateEmail($value)) {
                            throw new Exception('Invalid email format');
                        }
                        break;
                }
                
                $stmt->execute([$key, $value, $type]);
            }
        }
        
        // 更新app.json配置
        $appSettings = [
            'mysql_root_password' => $settings['mysql_root_password'] ?? '',
            'max_concurrent_jobs' => intval($settings['max_concurrent_jobs'] ?? 3),
            'default_timeout' => intval($settings['default_timeout'] ?? 1800),
            'enable_notifications' => filter_var($settings['enable_notifications'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'notification_email' => sanitizeInput($settings['notification_email'] ?? ''),
            'backup_before_deploy' => filter_var($settings['backup_before_deploy'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'auto_ssl_setup' => filter_var($settings['auto_ssl_setup'] ?? true, FILTER_VALIDATE_BOOLEAN)
        ];
        
        $appConfigFile = ROOT_PATH . '/config/app.json';
        if (file_exists($appConfigFile)) {
            $existingConfig = json_decode(file_get_contents($appConfigFile), true) ?: [];
            $appSettings = array_merge($existingConfig, $appSettings);
        }
        
        file_put_contents($appConfigFile, json_encode($appSettings, JSON_PRETTY_PRINT));
        
        $db->commit();
        
        logMessage('INFO', 'System settings updated', ['updated_keys' => array_keys($settings)]);
        
        successResponse(['updated_count' => count($settings)], 'Settings updated successfully');
        
    } catch (Exception $e) {
        $db->rollBack();
        errorResponse($e->getMessage());
    }
}

/**
 * 重置设置到默认值
 */
function handleResetSettings() {
    try {
        $db = getDatabase();
        
        // 删除所有自定义配置
        $db->exec("DELETE FROM system_config WHERE config_key NOT IN (
            'system_name'
        )");
        
        // 重新插入默认配置
        $defaultSettings = [
            ['max_concurrent_jobs', '3', 'integer'],
            ['default_timeout', '1800', 'integer'],
            ['auto_cleanup_logs', 'true', 'boolean'],
            ['log_retention_days', '30', 'integer'],
            ['health_check_interval', '300', 'integer'],
            ['enable_notifications', 'false', 'boolean'],
            ['notification_email', '', 'string'],
            ['backup_before_deploy', 'true', 'boolean'],
            ['auto_ssl_setup', 'true', 'boolean']
        ];
        
        $stmt = $db->prepare("
            INSERT INTO system_config (config_key, config_value, type) 
            VALUES (?, ?, ?)
        ");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
        logMessage('INFO', 'System settings reset to defaults');
        
        successResponse(null, 'Settings reset to defaults');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 测试数据库连接
 */
function handleTestConnection() {
    try {
        $db = getDatabase();
        
        // 测试查询
        $stmt = $db->query("SELECT COUNT(*) as count FROM system_config");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $connectionInfo = [
            'status' => 'connected',
            'host' => DB_HOST,
            'database' => DB_NAME,
            'user' => DB_USER,
            'config_count' => $result['count'],
            'server_version' => $db->getAttribute(PDO::ATTR_SERVER_VERSION),
            'connection_time' => date(DATE_FORMAT)
        ];
        
        successResponse($connectionInfo, 'Database connection successful');
        
    } catch (Exception $e) {
        errorResponse('Database connection failed: ' . $e->getMessage());
    }
}

/**
 * 获取系统要求检查结果
 */
function getSystemRequirements() {
    return [
        'php_version' => [
            'status' => version_compare(PHP_VERSION, '8.0.0', '>='),
            'current' => PHP_VERSION,
            'required' => '8.0.0+'
        ],
        'mysql_extension' => [
            'status' => extension_loaded('pdo_mysql'),
            'current' => extension_loaded('pdo_mysql') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'json_extension' => [
            'status' => extension_loaded('json'),
            'current' => extension_loaded('json') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'curl_extension' => [
            'status' => extension_loaded('curl'),
            'current' => extension_loaded('curl') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'zip_extension' => [
            'status' => extension_loaded('zip'),
            'current' => extension_loaded('zip') ? '已安装' : '未安装',
            'required' => '必需'
        ],
        'directory_writable' => [
            'status' => is_writable(ROOT_PATH),
            'current' => is_writable(ROOT_PATH) ? '可写' : '不可写',
            'required' => '可写'
        ],
        'original_script' => [
            'status' => file_exists(ORIGINAL_DEPLOY_SCRIPT) && is_executable(ORIGINAL_DEPLOY_SCRIPT),
            'current' => file_exists(ORIGINAL_DEPLOY_SCRIPT) ? 
                (is_executable(ORIGINAL_DEPLOY_SCRIPT) ? '存在且可执行' : '存在但不可执行') : '不存在',
            'required' => '存在且可执行'
        ]
    ];
}
