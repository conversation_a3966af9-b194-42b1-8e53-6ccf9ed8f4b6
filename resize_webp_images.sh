#!/bin/bash

# 设置 WebP 图片文件夹路径和修改后图片保存路径
WEBP_FOLDER="/www/imagerdown/imagedownload"
RESIZED_FOLDER="/www/imagerdown/imageresized"

# 创建目标文件夹，如果不存在
mkdir -p "$RESIZED_FOLDER"

# 定义调整图片尺寸的函数
resize_image() {
    local image="$1"
    local resized_image="$RESIZED_FOLDER/$(basename "$image")"

    # 获取图片的当前尺寸
    local image_size=$(identify -format "%wx%h" "$image")
    local width=$(echo "$image_size" | cut -dx -f1)
    local height=$(echo "$image_size" | cut -dx -f2)

    # 判断图片尺寸是否大于 1000x1000
    if [[ "$width" -le 1000 && "$height" -le 1000 ]]; then
        # 图片尺寸小于等于 1000x1000，直接转移
        echo "Image $(basename "$image") is already smaller than or equal to 1000x1000. Moving without resizing..."
        cp "$image" "$resized_image"
    else
        # 图片尺寸大于 1000x1000，调整大小
        echo "Resizing image $(basename "$image") to 1000x1000..."
        convert "$image" -resize 1000x1000 "$resized_image"
        echo "Resized and saved as $resized_image"
    fi
}

export -f resize_image
export RESIZED_FOLDER

# 使用 GNU Parallel 实现多线程
find "$WEBP_FOLDER" \( -name "*.webp" -o -name "*.jpg" -o -name "*.png" \) | parallel -j 8 resize_image
