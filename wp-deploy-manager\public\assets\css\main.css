/* WordPress Deploy Manager - 主样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 导航栏 */
.navbar {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    background: #667eea;
}

.nav-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.nav-menu {
    display: flex;
    gap: 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #333;
}

.nav-item.active {
    background: #007bff;
    color: white;
}

.nav-actions {
    display: flex;
    gap: 10px;
}

/* 主内容区 */
.main-content {
    margin-top: 60px;
    padding: 20px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 8px;
}

.page-header p {
    color: #666;
    font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.dashboard-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

/* 状态项 */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-value {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-running {
    background: #d4edda;
    color: #155724;
}

.status-connected {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

/* 活动项 */
.activity-item {
    display: flex;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #666;
    font-size: 12px;
    min-width: 60px;
}

/* 按钮 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-icon {
    padding: 8px;
    background: transparent;
    border: 1px solid #ddd;
}

.btn-icon:hover {
    background: #f8f9fa;
}

/* 表单 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* 部署项 */
.deploy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.deploy-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.deploy-time {
    color: #666;
    font-size: 12px;
}

/* 模板项 */
.template-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.template-info h4 {
    margin-bottom: 5px;
    color: #2c3e50;
}

.template-info p {
    color: #666;
    margin-bottom: 5px;
}

.template-info small {
    color: #999;
}

.template-actions {
    display: flex;
    gap: 10px;
}

/* 监控网格 */
.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.monitor-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.monitor-card h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.resource-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.resource-item:last-child {
    border-bottom: none;
}

/* 日志项 */
.log-item {
    display: flex;
    gap: 10px;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.log-info {
    background: #f8f9fa;
}

.log-debug {
    background: #e9ecef;
}

.log-error {
    background: #f8d7da;
}

.log-time {
    color: #666;
    min-width: 140px;
}

.log-level {
    font-weight: bold;
    min-width: 60px;
}

.log-message {
    flex: 1;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .deploy-item,
    .template-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* 图标字体（简单实现） */
.icon-dashboard::before { content: "📊"; }
.icon-deploy::before { content: "🚀"; }
.icon-template::before { content: "📋"; }
.icon-monitor::before { content: "📈"; }
.icon-logs::before { content: "📝"; }
.icon-settings::before { content: "⚙️"; }
.icon-refresh::before { content: "🔄"; }
