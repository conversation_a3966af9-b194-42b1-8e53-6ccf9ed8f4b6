<?php

/* RichnessCredit Payment Gateway Class */

class richness_credit extends WC_Payment_Gateway
{

    // Setup our Gateway's id, description and other values
    function __construct()
    {

        // The global ID for this Payment method
        $this->id = "richness_credit";

        // The Title shown on the top of the Payment Gateways Page next to all the other Payment Gateways
        $this->method_title = __("Richness Pay", 'richness_credit');

        // The description for this Payment Gateway, shown on the actual Payment options page on the backend
        $this->method_description = __("RichnessCredit Card Payment Gateway Plug-in for WooCommerce", 'richness_credit');

        // The title to be used for the vertical tabs that can be ordered top to bottom
        $this->title = __("Credit Card", 'richness_credit');

        // If you want to show an image next to the gateway's name on the frontend, enter a URL to an image.
        if($this->get_option('type')=='Credit Card'){
           // $this->icon = get_site_url(null, 'wp-content/uploads/wp-card-richnesscredit/stripe_logo.png');
            $this->icon = plugins_url( "/images/stripe_logo.png", __FILE__ );
        }else{
            $this->icon = plugins_url( "/images/stripe_logo.png", __FILE__ );
        }    

        // Bool. Can be set to true if you want payment fields to show on the checkout
        // if doing a direct integration, which we are doing in this case
        $this->has_fields = true;

        // Supports the default credit card form
        //$this->supports = array( 'default_credit_card_form' );

        // This basically defines your settings which are then loaded with init_settings()
        $this->init_form_fields();

        // After init_settings() is called, you can get the settings and load them into variables, e.g:
        // $this->title = $this->get_option( 'title' );
        $this->init_settings();

        // Turn these settings into variables we can use
        foreach ($this->settings as $setting_key => $value) {
            $this->$setting_key = $value;
        }

        // Lets check for SSL
        //add_action( 'admin_notices', array( $this,    'do_ssl_check' ) );
        add_action( 'woocommerce_api_wc_gateway_RichnessCreditcard_emb', array( $this, 'check_ipn_response' ) );
        add_action('woocommerce_receipt_' . $this->id, [$this, 'pay_for_order']);
        
        // Save settings
        if (is_admin()) {
            // Versions over 2.0
            // Save our administration options. Since we are not going to be doing anything special
            // we have not defined 'process_admin_options' in this class so the method in the parent
            // class will be used instead
            
            add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        }
        add_action('woocommerce_api_stripe-return', [$this, 'stripereturn']);//同步
    } // End __construct()

    // Build the administration fields for this specific Gateway
    public function init_form_fields()
    {
        $this->form_fields = array(
            'enabled' => array(
                'title' => __('Enable / Disable', 'RichnessCredit Card'),
                'label' => __('Enable this payment gateway', 'RichnessCredit Card'),
                'type' => 'checkbox',
                'default' => 'yes',
            ),
         
            'title' => array(
                'title' => __('Title', 'RichnessCredit Card'),
                'type' => 'text',
                'desc_tip' => __('Payment title the customer will see during the checkout process.', 'RichnessCredit Card'),
                'default' => __('Credit Card', 'RichnessCredit Card'),
            ),
            'description' => array(
                'title' => __('Description', 'RichnessCredit Card'),
                'type' => 'textarea',
                'desc_tip' => __('Payment description the customer will see during the checkout process.', 'RichnessCredit Card'),
                'default' => __('', 'RichnessCredit Card'),
                'css' => 'max-width:350px;'
            ),

            'type' => array(
                'title' => __('Type', 'Credit Card'),
                'label' => __('Type', 'Credit Card'),
                'type' => 'select',
                'default' => 'Credit Card',
                 'options'=>array(
                   'Credit Card'=>'Credit Card',
                  
                  )
            ),


            'mechant_user' => array(
                'title' => __('系统用户名', 'RichnessCredit'),
                'type' => 'text',
                'desc_tip' => __('系统用户名', 'RichnessCredit'),
            ),

            'mechant_my' => array(
                'title' => __('系统秘钥', 'RichnessCredit'),
                'type' => 'text',
                'desc_tip' => __('系统秘钥', 'RichnessCredit'),

            ),
            'dispatch' => array(
                'title' => __('Payment Url', 'RichnessCredit'),
                'type' => 'text',
                'desc_tip' => __('Payment Url will redirect to payment', 'RichnessCredit'),
                'default' => 'https://easycartvasco.shop/richnesscredit/',//改成自己的b 站url
            ),

        );
    }

 public function payment_fields(){
       //include __DIR__ . "/tpl/form.php";
      //if ($description = $this->get_description()) {
        //    echo wpautop(wptexturize($description));
        //}

        //TODO
    }

    // Submit payment and handle response
    public function process_payment($order_id)
    {
        global $woocommerce;
        global $wpdb;

        // Get this Order's information so that we know
        // who to charge and how much
        $order = new WC_Order($order_id);
        if (!$order) {
            wp_redirect(get_site_url());
        }
        $shipping_address = $order->get_address('shipping');
		$billing_address  = $order->get_address();	
        $url = rtrim($this->get_option('dispatch'),'/');
        $returnUrl = WC()->api_request_url('stripe-return'); //同步
        $carts = $order->get_items();
        $products = [];
        foreach ( $carts as $item){
                $_products = $item->get_product();
                $skuinfo = $_products->get_sku();

                $attachment_id = $_products->get_image_id();
                $ImageUrl = wp_get_attachment_image_src( $attachment_id, 'full' );//full thumbnail medium 
                if(is_array($ImageUrl)){ $ImageUrl = $ImageUrl[0]; }
                $option_data=array();
                $attrlist = $_products->is_type('variation')? $_products->get_attributes() : array();
                if(sizeof($attrlist) > 0){
                    foreach ($attrlist as $key => $valsize){
                         if(is_array($valsize)){ 
                            $option_data[] = array(
                            'name' =>$valsize["key"],
                            'value' =>$valsize["value"]
                            );
                        }else { 
                            $option_data[] = array(
                            'name' =>$key,
                            'value' =>$valsize
                            );
                        }
                        
                    }
                }
               
                $order_products[] = array(
                    'name'     => htmlspecialchars($item->get_name()),
                    'price'    => $_products->get_price(),
                    'qty' => $item->get_quantity(),
                    'model'    => $skuinfo,
                    'link'   => '/'.str_replace(' ','-',strtolower(htmlspecialchars($item->get_name()))),
                    'image'  => $ImageUrl,
                    'attr'   => [],
                    
                ); 
                
                    
         }
        $product_name = json_encode($order_products);
        
        $data = [
			'order_no'      => $order_id,
			'currency'      => $order->get_currency(),
			'subject'       => $product_name,
			'body'          => $product_name,
			
			'success_uri'   => $order->get_checkout_order_received_url(),//成功
			'return_uri'    => 'https://'.$_SERVER['HTTP_HOST'].'/checkout/',//esc_url_raw($order->get_cancel_order_url_raw()),//取消
			'notify_url'    => $returnUrl,

			'first_name'    => ucfirst(strtolower($billing_address['first_name'])),
			'last_name'     => ucfirst(strtolower($billing_address['last_name'])),
			'email'         => $billing_address['email'],
			'telephone'     => $billing_address['phone'],
			'address'       => $billing_address['address_1'].(empty($billing_address['address_2'])?'':' '.$billing_address['address_2']),
			'city'          => $billing_address['city'],
			'country'       => $billing_address['country'],
			'zip_code'      => $billing_address['postcode'],
			'zone'          => $billing_address['state'],
			'amount'        => $order->get_total(),
            'invoice_id'    => $this->get_invoice_id(),
            'client_ip'     => $this->get_ip_address(),
            'client_agent'  => $_SERVER['HTTP_USER_AGENT'],
            'client_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'],
			'payment_method'=> 'redirect_pay',
			'pay_model'     => '',//$this->get_option('pay_model'),
			'bn'            => 'wordpress',
		];
        
       $token = json_encode($data);
       $token = urlencode(base64_encode($token));
       $redirect = $url.'/?token='.$token;
       return array(
            'result'   => 'success',
            'redirect' => $redirect
        );
       
       

    }
    function get_invoice_id(){
        // Create random token
        $string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        $max = strlen($string) - 1;
        $token = '';
        for ($i = 0; $i < 18; $i++) {
            $token .= $string[mt_rand(0, $max)];
        }
        $invoice_id = md5(uniqid(md5(microtime(true)), true)) . $token;
        return $invoice_id;
    }
    public function check_ipn_response(){
        return false;

    }

    public function validate_fields() {
        
        return false;

    }    

   public function stripereturn(){

            $paymentData = $_POST;
            if(count($paymentData)> 1 && isset($paymentData["is_success"])){
                if($paymentData["is_success"] == 1){
                        // 成功
                        if(isset($paymentData['order_id'])) {
                            $orderID = $paymentData['order_id'];
                        }else{
                            echo 'errors';
                            exit;
                        }
                        $order = new WC_Order( $orderID );
                        //新增修改2023-10-7//
                        $order->add_order_note( "payment completed" );
                        //$order->update_status('processing');
                        $order->payment_complete($orderID); // 交易Id
                        echo 'success';
                        exit;
                }
                
            }
   }

/*
    public function PostRichnessCredit($data,$url){
			$json = json_encode($data);
            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => $url,
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 60,
              CURLOPT_FOLLOWLOCATION => false,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_POSTFIELDS => $json,
            ));
            
            $response = curl_exec($curl);
            if (curl_errno($ch)) {
                $log_err = curl_error($ch);
                $str_log = json_encode($log_err);
                error_log($str_log."\n",3,"rs_error.log");
                exit();
            }
            curl_close($curl);
            return json_decode($response,true);
    }
*/
    function get_ip_address()
    {
        if (isset($_SERVER)) {
      if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
      } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
      } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
      } elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
      } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
      } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
      } else {
        $ip = $_SERVER['REMOTE_ADDR'];
      }
    } else {
      if (getenv('HTTP_X_FORWARDED_FOR')) {
        $ip = getenv('HTTP_X_FORWARDED_FOR');
      } elseif (getenv('HTTP_CLIENT_IP')) {
        $ip = getenv('HTTP_CLIENT_IP');
      } else {
        $ip = getenv('REMOTE_ADDR');
      }
    }
    $ips = explode(", ", $ip);
    return $ips[0];
    }


} // End of SPYR_offline