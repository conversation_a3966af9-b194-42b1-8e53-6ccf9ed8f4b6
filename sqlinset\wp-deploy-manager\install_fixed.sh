#!/bin/bash

# WordPress 部署管理系统 - 修正版安装脚本
# 使用方法: ./install_fixed.sh wpd.cloudcheckout.shop your_mysql_password

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置参数
DOMAIN="${1:-wpd.cloudcheckout.shop}"
MYSQL_ROOT_PASS="$2"
PROJECT_DIR="/www/wwwroot/$DOMAIN"

echo -e "${BLUE}=================================================="
echo "  WordPress 部署管理系统 - 修正版安装"
echo "  域名: $DOMAIN"
echo "  项目目录: $PROJECT_DIR"
echo "==================================================${NC}"

# 检查参数
if [ -z "$MYSQL_ROOT_PASS" ]; then
    echo -e "${RED}错误: 请提供MySQL root密码${NC}"
    echo "使用方法: $0 域名 MySQL密码"
    echo "例如: $0 wpd.cloudcheckout.shop your_mysql_password"
    exit 1
fi

# 步骤1: 检查环境
echo -e "${BLUE}[1/7]${NC} 检查安装环境..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用root用户运行此脚本${NC}"
    exit 1
fi

# 检查项目目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录不存在: $PROJECT_DIR${NC}"
    echo "请先在宝塔面板创建站点: $DOMAIN"
    exit 1
fi

# 检查当前目录是否包含项目文件
if [ ! -f "config/config.php" ] && [ ! -f "public/index.html" ]; then
    echo -e "${RED}错误: 当前目录不包含项目文件${NC}"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

echo -e "${GREEN}环境检查通过${NC}"

# 步骤2: 复制文件到项目目录
echo -e "${BLUE}[2/7]${NC} 准备项目文件..."

# 如果当前目录不是项目目录，则复制文件
if [ "$(pwd)" != "$PROJECT_DIR" ]; then
    echo "复制文件到项目目录..."
    cp -r * "$PROJECT_DIR/"
    cd "$PROJECT_DIR"
else
    echo "已在项目目录中"
fi

# 步骤3: 设置权限
echo -e "${BLUE}[3/7]${NC} 设置文件权限..."
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 设置脚本执行权限
if [ -d "scripts" ]; then
    chmod +x scripts/*.sh 2>/dev/null || echo "警告: 无法设置脚本执行权限"
fi

# 创建必要目录
mkdir -p logs uploads/temp templates/uploads templates/configs config

echo -e "${GREEN}权限设置完成${NC}"

# 步骤4: 测试MySQL连接
echo -e "${BLUE}[4/7]${NC} 测试MySQL连接..."
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${RED}错误: MySQL连接失败${NC}"
    echo "请检查MySQL root密码是否正确"
    echo "可以在宝塔面板 → 数据库 → root密码 中查看"
    exit 1
fi
echo -e "${GREEN}MySQL连接成功${NC}"

# 步骤5: 创建数据库
echo -e "${BLUE}[5/7]${NC} 创建数据库和用户..."
DB_NAME="wp_deploy_manager"
DB_USER="wp_deploy"
DB_PASS=$(openssl rand -base64 12 | tr -d '=+/' | cut -c1-12)

mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
DROP DATABASE IF EXISTS $DB_NAME;
CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
DROP USER IF EXISTS '$DB_USER'@'localhost';
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

echo -e "${GREEN}数据库创建成功${NC}"

# 步骤6: 创建配置文件
echo -e "${BLUE}[6/7]${NC} 创建配置文件..."

# 创建数据库配置文件
cat > config/database.php << EOF
<?php
// MySQL数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', '$DB_NAME');
define('DB_USER', '$DB_USER');
define('DB_PASS', '$DB_PASS');
define('DB_CHARSET', 'utf8mb4');

// 创建PDO连接
function getDatabase() {
    static \$pdo = null;
    if (\$pdo === null) {
        \$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
    }
    return \$pdo;
}
?>
EOF

# 创建应用配置文件
cat > config/app.json << EOF
{
    "mysql_root_password": "$MYSQL_ROOT_PASS",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "",
    "backup_before_deploy": true,
    "auto_ssl_setup": true,
    "domain": "$DOMAIN"
}
EOF

echo -e "${GREEN}配置文件创建成功${NC}"

# 步骤7: 初始化数据库表
echo -e "${BLUE}[7/7]${NC} 初始化数据库表..."

# 检查SQL文件是否存在
if [ ! -f "database/init_mysql.sql" ]; then
    echo -e "${RED}错误: 数据库初始化文件不存在${NC}"
    echo "请确保 database/init_mysql.sql 文件存在"
    exit 1
fi

# 执行SQL文件
mysql -u root -p"$MYSQL_ROOT_PASS" "$DB_NAME" < database/init_mysql.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库表初始化成功${NC}"
else
    echo -e "${RED}数据库表初始化失败${NC}"
    exit 1
fi

# 完成安装
echo -e "${GREEN}=================================================="
echo "  🎉 安装完成！"
echo "=================================================="
echo "访问地址: http://$DOMAIN"
echo "数据库名: $DB_NAME"
echo "数据库用户: $DB_USER"
echo "数据库密码: $DB_PASS"
echo "项目目录: $PROJECT_DIR"
echo "==================================================${NC}"

echo -e "${YELLOW}重要信息:${NC}"
echo "1. 请保存数据库密码: $DB_PASS"
echo "2. 配置文件位置: $PROJECT_DIR/config/"
echo "3. 日志目录: $PROJECT_DIR/logs/"
echo ""

echo -e "${YELLOW}下一步操作:${NC}"
echo "1. 在宝塔面板设置网站运行目录为: /public"
echo "2. 访问 http://$DOMAIN 开始使用"
echo "3. 上传WordPress模板开始部署"
echo ""

# 检查网站访问
echo -e "${BLUE}检查网站访问状态...${NC}"
sleep 2
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" --max-time 10 2>/dev/null || echo "000")

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "301" ] || [ "$HTTP_CODE" = "302" ]; then
    echo -e "${GREEN}✅ 网站访问正常 (HTTP $HTTP_CODE)${NC}"
elif [ "$HTTP_CODE" = "403" ]; then
    echo -e "${YELLOW}⚠️  网站返回403，请检查运行目录设置${NC}"
    echo "   在宝塔面板 → 网站设置 → 网站目录 → 运行目录设为 /public"
else
    echo -e "${YELLOW}⚠️  网站可能需要几分钟才能访问 (HTTP $HTTP_CODE)${NC}"
fi

echo ""
echo -e "${GREEN}🚀 WordPress部署管理系统安装成功！${NC}"
echo -e "${BLUE}访问 http://$DOMAIN 开始使用吧！${NC}"
