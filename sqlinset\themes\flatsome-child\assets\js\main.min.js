 jQuery(document).ready(function($){$('.nav-wrapper').on('mouseenter','.nav > li',function(){$(this).addClass('hover');}).on('mouseleave','.nav > li',function(){$(this).removeClass('hover');});$('.product-small').on('mouseenter',function(){$(this).addClass('hover');}).on('mouseleave',function(){$(this).removeClass('hover');});$(window).scroll(function(){if($(this).scrollTop()>100){$('.header-wrapper').addClass('scrolled');}else{$('.header-wrapper').removeClass('scrolled');}});if($.fn.owlCarousel){$('.related.products .products').owlCarousel({items:4,margin:30,nav:true,dots:false,responsive:{0:{items:1},480:{items:2},768:{items:3},992:{items:4}}});}$('form.checkout').on('checkout_place_order',function(){var isValid=true;$('.required').each(function(){if(!$(this).val()){isValid=false;$(this).addClass('error');}else{$(this).removeClass('error');}});return isValid;});if($.fn.lazy){$('img.lazy').lazy({effect:'fadeIn',effectTime:500});}$('.mobile-menu-toggle').on('click',function(){$('.mobile-menu').toggleClass('active');});$('.search-toggle').on('click',function(){$('.search-form').toggleClass('active');});$('.back-to-top').on('click',function(){$('html, body').animate({scrollTop:0},'slow');return false;});$('.product-gallery img').on('click',function(){var src=$(this).attr('src');$('.product-image-zoom').attr('src',src).fadeIn();});});(function($,api){'use strict';api.bind('ready',function(){var placeholder=$('#customize-control-apply_preset_style_button_placeholder');if(placeholder.length){var applyButtonHtml='<button id=\"apply-preset-style-button\" class=\"button button-primary\" style=\"margin-top: 5px;\">'+wp.customize.control('apply_preset_style_selector').params.label.replace('Apply','Apply Selected')+'</button>';placeholder.after(applyButtonHtml);$('#apply-preset-style-button').on('click',function(e){e.preventDefault();var selectedPreset=api('apply_preset_style_selector').get();if(!selectedPreset){alert(flatsomeChildI18n.selectPreset);return;}if(typeof flatsomeChildPresets!=='undefined'&&flatsomeChildPresets[selectedPreset]){var presetValues=flatsomeChildPresets[selectedPreset];$.each(presetValues,function(settingKey,value){var setting=api(settingKey);if(setting){setting.set(value);}else{console.warn(flatsomeChildI18n.notFoundKey+settingKey);}if(settingKey==='text_color'){document.body.style.setProperty('--text-color',value);}});}else{alert(flatsomeChildI18n.errorPreset+selectedPreset);}});}else{console.error(flatsomeChildI18n.notFound);}});})(jQuery,wp.customize);