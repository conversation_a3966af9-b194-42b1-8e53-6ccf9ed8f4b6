# WordPress 批量部署优化系统

## 系统概述

这是一个专为宝塔面板环境设计的WordPress批量部署系统，支持模板化、差异化内容生成和智能监控管理。

## 主要特性

### 🚀 核心功能
- **模板化部署**: 支持多种行业模板（企业官网、电商、博客等）
- **差异化内容**: 自动生成个性化内容和SEO优化
- **宝塔集成**: 深度集成宝塔面板API，自动化SSL、备份等
- **并行部署**: 支持多站点并行部署，提高效率
- **智能监控**: 全面的健康检查和性能监控

### 📊 优化亮点
- **配置驱动**: JSON配置文件，灵活可扩展
- **内容智能化**: 基于关键词和行业自动生成差异化内容
- **安全加固**: 自动安全配置和定期安全扫描
- **运维自动化**: 批量更新、备份、监控一体化

## 快速开始

### 1. 环境准备

```bash
# 确保已安装必要工具
yum install -y jq curl bc
# 或 Ubuntu/Debian
apt-get install -y jq curl bc

# 安装WP-CLI（如果未安装）
curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
chmod +x wp-cli.phar
mv wp-cli.phar /usr/local/bin/wp
```

### 2. 配置系统

```bash
# 1. 复制配置模板
cp config/enhanced_config.json config/production.json

# 2. 编辑配置文件
vim config/production.json

# 3. 设置环境变量
export MYSQL_ROOT_PASS="your_mysql_password"
export BT_PANEL_URL="http://your-panel-url:port"
export BT_API_KEY="your_api_key"
```

### 3. 准备域名列表

编辑 `data/enhanced_domains.csv`:

```csv
domain,template_type,industry,language,region,custom_config,priority,target_keywords
example1.com,corporate,tools,de,germany,"{""company_type"":""hardware""}",high,"werkzeug,profi"
example2.com,ecommerce,garden,de,germany,"{""seasonal_products"":true}",high,"garten,pflanzen"
```

### 4. 执行部署

```bash
# 单站点部署
./scripts/core/enhanced_deploy.sh single example.com

# 批量部署
./scripts/core/enhanced_deploy.sh batch

# 监控模式部署
./scripts/core/enhanced_deploy.sh monitor
```

## 详细配置说明

### 配置文件结构

```json
{
  "global": {
    "mother_domain": "template.example.com",
    "mysql_root_pass": "${MYSQL_ROOT_PASS}",
    "wp_cli_path": "/usr/local/bin/wp"
  },
  "templates": {
    "industry_templates": {
      "ecommerce": {
        "theme": "storefront",
        "plugins": ["woocommerce"],
        "content_type": "product_focused"
      }
    }
  },
  "deployment": {
    "parallel_limit": 3,
    "retry_attempts": 3,
    "timeout_seconds": 300
  }
}
```

### 域名配置说明

| 字段 | 说明 | 示例 |
|------|------|------|
| domain | 目标域名 | example.com |
| template_type | 模板类型 | corporate/ecommerce/blog |
| industry | 行业分类 | tools/garden/tech |
| language | 语言代码 | de/en/fr |
| custom_config | 自定义配置JSON | {"company_type":"hardware"} |
| priority | 优先级 | high/medium/low |
| target_keywords | 目标关键词 | "工具,专业,质量" |

## 使用场景和最佳实践

### 场景1: 批量企业官网部署

```bash
# 1. 配置企业模板
vim templates/industry/corporate/

# 2. 准备域名列表（企业类型）
cat > data/corporate_sites.csv << EOF
domain,template_type,industry,language,region,custom_config,priority,target_keywords
company1.com,corporate,consulting,en,usa,"{""services"":[""strategy"",""management""]}",high,"consulting,strategy,business"
company2.de,corporate,manufacturing,de,germany,"{""products"":[""machinery"",""automation""]}",high,"fertigung,maschinen,automation"
EOF

# 3. 执行部署
./scripts/core/enhanced_deploy.sh batch corporate_sites.csv
```

### 场景2: 电商站点批量部署

```bash
# 1. 配置电商模板
vim config/enhanced_config.json
# 设置 WooCommerce 相关配置

# 2. 准备产品数据
mkdir -p content/ecommerce/products/
# 添加产品CSV文件

# 3. 执行部署
./scripts/core/enhanced_deploy.sh batch ecommerce_sites.csv
```

### 场景3: 多语言站点部署

```bash
# 1. 配置多语言支持
vim config/enhanced_config.json
# 设置语言包和本地化选项

# 2. 准备多语言内容
mkdir -p content/multilingual/{de,en,fr}/

# 3. 执行部署
./scripts/core/enhanced_deploy.sh batch multilingual_sites.csv
```

## 监控和维护

### 健康检查

```bash
# 单站点健康检查
./scripts/utils/monitoring_system.sh health example.com

# 批量健康检查
./scripts/utils/monitoring_system.sh health

# 生成健康报告
./scripts/utils/monitoring_system.sh dashboard
```

### 性能监控

```bash
# 性能监控
./scripts/utils/monitoring_system.sh performance example.com

# 批量性能检查
for domain in $(cat data/domains.txt); do
    ./scripts/utils/monitoring_system.sh performance $domain
done
```

### 安全管理

```bash
# 安全扫描
./scripts/utils/monitoring_system.sh security example.com

# 批量更新
./scripts/utils/monitoring_system.sh update
```

## 故障排除

### 常见问题

1. **宝塔API连接失败**
   ```bash
   # 检查API配置
   curl -X POST "$BT_PANEL_URL/api/system/get_system_total" -d "access_key=$BT_API_KEY"
   ```

2. **MySQL连接问题**
   ```bash
   # 测试数据库连接
   mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW DATABASES;"
   ```

3. **WP-CLI权限问题**
   ```bash
   # 检查WP-CLI权限
   sudo -u www wp --info
   ```

### 日志分析

```bash
# 查看部署日志
tail -f logs/deployment/deploy_*.log

# 查看错误日志
tail -f logs/errors/deploy_*_errors.log

# 查看监控日志
tail -f logs/monitoring/monitoring_*.log
```

## 性能优化建议

### 1. 并行部署优化
- 根据服务器性能调整 `parallel_limit`
- 监控系统资源使用情况
- 使用SSD存储提高I/O性能

### 2. 数据库优化
- 定期优化数据库表
- 配置MySQL缓存
- 使用数据库连接池

### 3. 内容优化
- 启用CDN加速
- 配置图片压缩
- 使用缓存插件

## 扩展开发

### 添加新模板类型

1. 创建模板目录
   ```bash
   mkdir -p templates/industry/new_template/
   ```

2. 配置模板参数
   ```json
   "new_template": {
     "theme": "custom-theme",
     "plugins": ["custom-plugin"],
     "content_type": "specialized"
   }
   ```

3. 实现内容生成逻辑
   ```bash
   vim scripts/core/content_generator.sh
   # 添加新模板的内容生成函数
   ```

### 集成第三方服务

```bash
# 示例：集成邮件服务
vim scripts/utils/notification.sh
# 添加邮件通知功能

# 示例：集成备份服务
vim scripts/bt_integration/backup_manager.sh
# 添加云备份功能
```

## 安全注意事项

1. **敏感信息管理**
   - 使用环境变量存储密码
   - 定期轮换API密钥
   - 限制脚本执行权限

2. **网络安全**
   - 配置防火墙规则
   - 启用SSL证书
   - 定期安全扫描

3. **备份策略**
   - 自动化备份
   - 异地备份存储
   - 定期恢复测试

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 支持

如有问题，请查看文档或提交 Issue。
