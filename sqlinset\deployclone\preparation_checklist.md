# 批量部署准备清单

## 🎯 必需准备的内容

### 1. 域名和基础信息
创建文件: `data/your_domains.csv`

```csv
domain,template_type,industry,language,region,custom_config,priority,target_keywords
hauswerkpro.com,corporate,tools,de,germany,"{""company_type"":""hardware"",""target_audience"":""professionals""}",high,"werkzeug,profi,handwerk"
werkstark.com,ecommerce,tools,de,germany,"{""product_focus"":""power_tools"",""price_range"":""premium""}",high,"stark,werkzeug,online"
```

**字段说明:**
- `domain`: 新站点域名
- `template_type`: 模板类型 (corporate/ecommerce/blog)
- `industry`: 行业分类 (tools/garden/tech/medical等)
- `language`: 语言代码 (de/en/fr/es)
- `region`: 地区代码 (germany/usa/france)
- `custom_config`: JSON格式的自定义配置
- `priority`: 优先级 (high/medium/low)
- `target_keywords`: 目标关键词，逗号分隔

### 2. 关键词库准备
创建文件: `content/keywords/industry_keywords.json`

```json
{
  "tools": {
    "primary": ["werkzeug", "tool", "equipment", "instrument"],
    "secondary": ["profi", "professional", "quality", "durable"],
    "long_tail": ["professionelle werkzeuge", "hochwertige tools", "langlebige ausrüstung"]
  },
  "garden": {
    "primary": ["garten", "garden", "plant", "flower"],
    "secondary": ["pflanze", "blume", "grün", "natur"],
    "long_tail": ["garten pflege", "pflanzen züchten", "blumen gießen"]
  }
}
```

### 3. 行业特定内容模板
创建目录: `content/templates/`

#### 工具行业模板 (`content/templates/tools.json`)
```json
{
  "company_suffixes": ["Pro", "Expert", "Master", "Kraft", "Stark"],
  "business_types": ["Werkzeughandel", "Profi-Ausrüstung", "Industriewerkzeuge"],
  "service_descriptions": [
    "Professionelle Beratung für Werkzeugauswahl",
    "Schnelle Lieferung hochwertiger Tools",
    "Umfassender Service für Profi-Ausrüstung"
  ],
  "about_templates": [
    "{{company_name}} ist Ihr vertrauensvoller Partner für {{industry_focus}}. Mit über {{years_experience}} Jahren Erfahrung bieten wir {{main_services}} für {{target_audience}}.",
    "Bei {{company_name}} finden Sie {{product_range}}. Unser Expertenteam berät Sie gerne bei der Auswahl der richtigen {{industry_focus}}."
  ]
}
```

### 4. 本地化内容
创建目录: `content/localization/`

#### 德国本地化 (`content/localization/germany.json`)
```json
{
  "contact_info": {
    "phone_format": "+49 (0) {{area_code}} {{number}}",
    "address_format": "{{street}} {{number}}\n{{postal_code}} {{city}}\nDeutschland",
    "business_hours": "Mo-Fr: 8:00-18:00 Uhr\nSa: 9:00-14:00 Uhr"
  },
  "legal_pages": {
    "impressum_required": true,
    "datenschutz_required": true,
    "agb_required": true
  },
  "currency": "EUR",
  "language_code": "de-DE",
  "date_format": "d.m.Y"
}
```

### 5. SEO内容模板
创建文件: `content/seo/meta_templates.json`

```json
{
  "title_templates": {
    "corporate": "{{company_name}} - {{industry}} {{primary_keyword}} | {{location}}",
    "ecommerce": "{{primary_keyword}} kaufen | {{company_name}} Online Shop",
    "blog": "{{company_name}} Blog - {{industry}} Ratgeber | {{primary_keyword}}"
  },
  "description_templates": {
    "corporate": "✓ {{company_name}} - Ihr {{industry}} Spezialist. {{services}} ✓ Professionelle Beratung ✓ Schneller Service. {{primary_keyword}} Angebot anfordern!",
    "ecommerce": "🛒 {{primary_keyword}} online kaufen bei {{company_name}}. ✓ Große Auswahl ✓ Faire Preise ✓ Schnelle Lieferung. Jetzt bestellen!",
    "blog": "📖 {{company_name}} Blog: {{industry}} Expertentipps. Ratgeber, Tests & Anleitungen für {{keywords}}. Jetzt lesen!"
  }
}
```

## 🔧 技术准备

### 1. 环境配置
```bash
# 创建配置文件
cp config/enhanced_config.json config/production.json

# 设置环境变量
export MYSQL_ROOT_PASS="your_mysql_password"
export BT_PANEL_URL="http://your-panel:port"
export BT_API_KEY="your_api_key"
```

### 2. 权限设置
```bash
# 设置脚本执行权限
chmod +x scripts/core/*.sh
chmod +x scripts/bt_integration/*.sh
chmod +x scripts/utils/*.sh

# 设置日志目录权限
mkdir -p logs/{deployment,errors,monitoring}
chmod 755 logs/
```

### 3. 依赖检查
```bash
# 检查必需工具
command -v jq >/dev/null || echo "需要安装 jq"
command -v wp >/dev/null || echo "需要安装 WP-CLI"
command -v curl >/dev/null || echo "需要安装 curl"
```

## 📋 可选准备内容

### 1. 自定义图片资源
```
content/media/
├── logos/           # 不同行业的Logo模板
├── banners/         # 横幅图片
├── products/        # 产品图片
└── backgrounds/     # 背景图片
```

### 2. 邮件模板
创建文件: `content/email_templates.json`

```json
{
  "welcome": {
    "subject": "Willkommen bei {{company_name}}",
    "body": "Vielen Dank für Ihr Interesse an {{company_name}}..."
  },
  "contact_form": {
    "subject": "Neue Anfrage von {{company_name}} Website",
    "body": "Eine neue Kontaktanfrage ist eingegangen..."
  }
}
```

### 3. 社交媒体配置
创建文件: `content/social_media.json`

```json
{
  "platforms": {
    "facebook": "https://facebook.com/{{company_slug}}",
    "instagram": "https://instagram.com/{{company_slug}}",
    "linkedin": "https://linkedin.com/company/{{company_slug}}"
  },
  "sharing_texts": {
    "default": "Entdecken Sie {{company_name}} - Ihr {{industry}} Spezialist!"
  }
}
```

## 🚀 快速开始步骤

### 第一步：最小化准备
```bash
# 1. 只准备域名列表
echo "domain,template_type,industry,language,region,custom_config,priority,target_keywords
hauswerkpro.com,corporate,tools,de,germany,\"{}\",high,\"werkzeug,profi,handwerk\"" > data/test_domains.csv

# 2. 测试单个站点部署
./scripts/core/enhanced_deploy.sh single hauswerkpro.com
```

### 第二步：扩展准备
```bash
# 1. 添加更多域名
vim data/test_domains.csv

# 2. 准备关键词库
mkdir -p content/keywords/
echo '{"tools":{"primary":["werkzeug","tool"]}}' > content/keywords/industry_keywords.json

# 3. 批量部署测试
./scripts/core/enhanced_deploy.sh batch data/test_domains.csv
```

### 第三步：完整部署
```bash
# 1. 完善所有配置文件
# 2. 准备完整的内容库
# 3. 执行大规模部署
./scripts/core/enhanced_deploy.sh batch data/production_domains.csv
```

## ⚠️ 重要注意事项

### 1. 备份原始模板
```bash
# 在开始前备份您的成熟模板站点
tar -czf backup/mother_site_$(date +%Y%m%d).tar.gz /www/wwwroot/astra.nestlyalli.shop/
mysqldump -u root -p astra_nestlyalli > backup/mother_db_$(date +%Y%m%d).sql
```

### 2. 测试环境验证
- 先在测试域名上验证差异化效果
- 检查生成内容的质量和一致性
- 确认SEO设置正确

### 3. 逐步扩展
- 从少量域名开始测试
- 验证效果后再大规模部署
- 持续优化内容模板

## 📊 预期效果

通过这套准备，您可以实现：
- **内容差异化率**: 80%以上的内容看起来完全不同
- **SEO优化**: 每个站点都有针对性的SEO设置
- **本地化程度**: 完全适应目标地区和语言
- **部署效率**: 相比手动操作提升10倍以上
