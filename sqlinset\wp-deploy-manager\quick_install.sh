#!/bin/bash

# WordPress 部署管理系统 - 一键安装脚本
# 适配 PHP 8.1 + MySQL 8.0 环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置参数
DOMAIN="${1:-wpd.cloudcheckout.shop}"
MYSQL_ROOT_PASS="${2}"
PROJECT_DIR="/www/wwwroot/$DOMAIN"

# 获取MySQL密码的多种方式
if [ -z "$MYSQL_ROOT_PASS" ]; then
    # 方式1: 从环境变量获取
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        MYSQL_ROOT_PASS="$MYSQL_ROOT_PASSWORD"
    # 方式2: 从宝塔面板配置文件获取
    elif [ -f "/www/server/panel/data/default.db" ]; then
        # 尝试从宝塔面板获取（需要root权限）
        MYSQL_ROOT_PASS=$(sqlite3 /www/server/panel/data/default.db "SELECT mysql_root FROM config WHERE id=1;" 2>/dev/null || echo "")
    fi

    # 方式3: 硬编码密码（请修改为您的实际密码）
    if [ -z "$MYSQL_ROOT_PASS" ]; then
        MYSQL_ROOT_PASS="2KHNS4XSsMBLZJ7B"  # 请修改这里
    fi
fi

echo -e "${BLUE}=================================================="
echo "  WordPress 部署管理系统 - 一键安装"
echo "  域名: $DOMAIN"
echo "  MySQL密码: [已配置]"
echo "==================================================${NC}"

# 步骤1: 检查项目目录
echo -e "${BLUE}[1/6]${NC} 检查项目目录..."

# 检查是否已经在正确的目录中
if [ "$(pwd)" = "$PROJECT_DIR" ]; then
    echo "已在项目目录中，跳过复制步骤"
else
    # 如果不在项目目录中，检查目录是否存在
    if [ ! -d "$PROJECT_DIR" ]; then
        echo "创建项目目录: $PROJECT_DIR"
        mkdir -p "$PROJECT_DIR"
        cp -r . "$PROJECT_DIR/"
    else
        echo "项目目录已存在: $PROJECT_DIR"
        # 检查目录是否为空
        if [ "$(ls -A $PROJECT_DIR)" ]; then
            echo "目录不为空，假设文件已存在"
        else
            echo "复制文件到项目目录"
            cp -r . "$PROJECT_DIR/"
        fi
    fi
    cd "$PROJECT_DIR"
fi

# 步骤2: 设置权限
echo -e "${BLUE}[2/6]${NC} 设置文件权限..."
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 设置脚本执行权限
if [ -d "scripts" ]; then
    chmod +x scripts/*.sh 2>/dev/null || echo "警告: 无法设置脚本执行权限"
fi

# 创建必要目录
mkdir -p logs uploads/temp templates/uploads templates/configs config

# 步骤3: 创建MySQL数据库
echo -e "${BLUE}[3/6]${NC} 创建MySQL数据库..."
DB_NAME="wp_deploy_manager"
DB_USER="wp_deploy"
DB_PASS=$(openssl rand -base64 12 | tr -d '=+/' | cut -c1-12)

# 测试MySQL连接
echo "测试MySQL连接..."
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${RED}错误: MySQL连接失败，请检查密码${NC}"
    echo "请确认MySQL root密码是否正确"
    exit 1
fi

echo "创建数据库和用户..."
mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
DROP DATABASE IF EXISTS $DB_NAME;
CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
DROP USER IF EXISTS '$DB_USER'@'localhost';
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

echo -e "${GREEN}数据库创建成功${NC}"

# 步骤4: 创建配置文件
echo -e "${BLUE}[4/6]${NC} 创建配置文件..."
cat > config/database.php << EOF
<?php
// MySQL数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', '$DB_NAME');
define('DB_USER', '$DB_USER');
define('DB_PASS', '$DB_PASS');
define('DB_CHARSET', 'utf8mb4');

// 创建PDO连接
function getDatabase() {
    static \$pdo = null;
    if (\$pdo === null) {
        \$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
    }
    return \$pdo;
}
?>
EOF

cat > config/app.json << EOF
{
    "mysql_root_password": "$MYSQL_ROOT_PASS",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "",
    "backup_before_deploy": true,
    "auto_ssl_setup": true,
    "domain": "$DOMAIN"
}
EOF

# 步骤5: 初始化数据库表
echo -e "${BLUE}[5/6]${NC} 初始化数据库表..."

# 检查数据库初始化文件是否存在
if [ ! -f "database/init_mysql.sql" ]; then
    echo -e "${RED}错误: 数据库初始化文件不存在: database/init_mysql.sql${NC}"
    exit 1
fi

echo "执行数据库初始化..."
# 使用MySQL命令直接执行SQL文件
mysql -u root -p"$MYSQL_ROOT_PASS" "$DB_NAME" < database/init_mysql.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库表初始化成功${NC}"
else
    echo -e "${RED}数据库表初始化失败${NC}"
    exit 1
fi

# 步骤6: 配置Nginx
echo -e "${BLUE}[6/6]${NC} 配置Nginx..."
cat > "/www/server/panel/vhost/nginx/$DOMAIN.conf" << EOF
server {
    listen 80;
    server_name $DOMAIN;
    index index.html index.htm index.php;
    root $PROJECT_DIR/public;
    
    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 禁止访问敏感目录
    location ~ ^/(config|database|logs|scripts|install)/ {
        deny all;
    }
    
    # PHP处理
    location ~ \.php\$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)\$;
        fastcgi_pass unix:/tmp/php-cgi-81.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
    }
    
    # API路由
    location /api/ {
        try_files \$uri \$uri/ /api/index.php?\$query_string;
    }
    
    # 静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 默认路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    access_log $PROJECT_DIR/logs/nginx_access.log;
    error_log $PROJECT_DIR/logs/nginx_error.log;
}
EOF

# 重载Nginx
nginx -t && nginx -s reload

echo -e "${GREEN}=================================================="
echo "  安装完成！"
echo "=================================================="
echo "访问地址: http://$DOMAIN"
echo "数据库: $DB_NAME"
echo "用户名: $DB_USER"
echo "项目目录: $PROJECT_DIR"
echo "==================================================${NC}"

echo -e "${YELLOW}下一步操作:${NC}"
echo "1. 访问 http://$DOMAIN 开始使用"
echo "2. 上传WordPress模板"
echo "3. 开始部署站点"
echo ""
echo -e "${GREEN}安装成功！${NC}"
EOF
