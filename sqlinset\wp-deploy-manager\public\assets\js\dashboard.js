/**
 * 仪表板管理模块
 */

class Dashboard {
    constructor() {
        this.charts = {};
        this.refreshInterval = null;
        this.init();
    }

    /**
     * 初始化仪表板
     */
    init() {
        this.loadStatistics();
        this.loadDeploymentChart();
        this.loadRecentActivity();
        this.setupAutoRefresh();
        
        // 设置图表周期选择器
        const chartPeriod = document.getElementById('chart-period');
        if (chartPeriod) {
            chartPeriod.addEventListener('change', () => {
                this.loadDeploymentChart();
            });
        }
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const response = await api.getDeployStats();
            const stats = response.data;

            // 更新统计卡片
            this.updateStatCard('total-sites', stats.total_sites || 0);
            this.updateStatCard('successful-deployments', stats.successful_deployments || 0);
            this.updateStatCard('pending-jobs', stats.pending_jobs || 0);
            this.updateStatCard('active-templates', stats.active_templates || 0);

        } catch (error) {
            console.error('Failed to load statistics:', error);
            app.showNotification('加载统计数据失败', 'error');
        }
    }

    /**
     * 更新统计卡片
     */
    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // 添加动画效果
            element.style.opacity = '0.5';
            setTimeout(() => {
                element.textContent = this.formatNumber(value);
                element.style.opacity = '1';
            }, 200);
        }
    }

    /**
     * 加载部署趋势图表
     */
    async loadDeploymentChart() {
        try {
            const chartPeriod = document.getElementById('chart-period');
            const days = chartPeriod ? parseInt(chartPeriod.value) : 30;
            
            const response = await api.getDeployStats(days);
            const data = response.data.chart_data || [];

            this.renderDeploymentChart(data);

        } catch (error) {
            console.error('Failed to load deployment chart:', error);
            app.showNotification('加载图表数据失败', 'error');
        }
    }

    /**
     * 渲染部署趋势图表
     */
    renderDeploymentChart(data) {
        const canvas = document.getElementById('deployment-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // 清除之前的图表
        if (this.charts.deployment) {
            this.charts.deployment.destroy();
        }

        // 准备图表数据
        const labels = data.map(item => item.date);
        const successData = data.map(item => item.successful || 0);
        const failedData = data.map(item => item.failed || 0);

        // 创建图表
        this.charts.deployment = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '成功部署',
                        data: successData,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '失败部署',
                        data: failedData,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    }

    /**
     * 加载最近活动
     */
    async loadRecentActivity() {
        try {
            const response = await api.getDeployHistory({ limit: 10 });
            const activities = response.data.jobs || [];

            this.renderRecentActivity(activities);

        } catch (error) {
            console.error('Failed to load recent activity:', error);
            app.showNotification('加载最近活动失败', 'error');
        }
    }

    /**
     * 渲染最近活动
     */
    renderRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<div class="no-activity">暂无活动记录</div>';
            return;
        }

        const html = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="icon-${this.getActivityIcon(activity.status)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">
                        ${activity.type === 'batch' ? '批量部署' : '单域名部署'}: ${activity.domain || '多个域名'}
                    </div>
                    <div class="activity-meta">
                        <span class="activity-status status-${activity.status}">${this.getStatusText(activity.status)}</span>
                        <span class="activity-time">${app.formatDateTime(activity.created_at)}</span>
                    </div>
                </div>
                <div class="activity-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${activity.progress || 0}%"></div>
                    </div>
                    <span class="progress-text">${activity.progress || 0}%</span>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 获取活动图标
     */
    getActivityIcon(status) {
        const icons = {
            'completed': 'check',
            'failed': 'x',
            'running': 'clock',
            'pending': 'clock',
            'cancelled': 'x'
        };
        return icons[status] || 'clock';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const texts = {
            'completed': '已完成',
            'failed': '失败',
            'running': '运行中',
            'pending': '待处理',
            'cancelled': '已取消'
        };
        return texts[status] || status;
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒刷新一次数据
        this.refreshInterval = setInterval(() => {
            if (!document.hidden) {
                this.loadStatistics();
                this.loadRecentActivity();
            }
        }, 30000);
    }

    /**
     * 刷新仪表板
     */
    refresh() {
        this.loadStatistics();
        this.loadDeploymentChart();
        this.loadRecentActivity();
    }

    /**
     * 格式化数字
     */
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * 销毁仪表板
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        // 销毁图表
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
    }
}

/**
 * 刷新最近活动
 */
function refreshRecentActivity() {
    if (window.dashboard) {
        window.dashboard.loadRecentActivity();
    }
}

// 创建全局仪表板实例
window.dashboard = new Dashboard();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dashboard;
}
