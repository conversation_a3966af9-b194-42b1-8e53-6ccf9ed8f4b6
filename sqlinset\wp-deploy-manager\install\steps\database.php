<?php
$dbInitialized = false;
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $dbInitialized = initializeDatabase();
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
    }
}
?>

<div class="database-content">
    <h2>数据库初始化</h2>
    <p>正在初始化数据库和创建必要的表结构...</p>
    
    <?php if ($errorMessage): ?>
        <div class="alert alert-error">
            <strong>初始化失败：</strong><?php echo htmlspecialchars($errorMessage); ?>
        </div>
    <?php elseif ($dbInitialized): ?>
        <div class="alert alert-success">
            <strong>初始化成功！</strong>数据库已成功创建并初始化。
        </div>
    <?php endif; ?>
    
    <div class="database-progress">
        <div class="progress-steps">
            <div class="progress-step completed">
                <div class="step-icon">✓</div>
                <div class="step-content">
                    <h4>创建数据库</h4>
                    <p>wp_deploy_manager</p>
                </div>
            </div>
            
            <div class="progress-step completed">
                <div class="step-icon">✓</div>
                <div class="step-content">
                    <h4>创建用户</h4>
                    <p>wp_deploy@localhost</p>
                </div>
            </div>
            
            <div class="progress-step <?php echo $dbInitialized ? 'completed' : 'pending'; ?>">
                <div class="step-icon"><?php echo $dbInitialized ? '✓' : '⏳'; ?></div>
                <div class="step-content">
                    <h4>创建表结构</h4>
                    <p>初始化数据表</p>
                </div>
            </div>
            
            <div class="progress-step <?php echo $dbInitialized ? 'completed' : 'pending'; ?>">
                <div class="step-icon"><?php echo $dbInitialized ? '✓' : '⏳'; ?></div>
                <div class="step-content">
                    <h4>插入初始数据</h4>
                    <p>系统配置和默认数据</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="database-details">
        <h4>数据库信息</h4>
        <div class="details-grid">
            <div class="detail-row">
                <span class="detail-label">数据库名：</span>
                <span class="detail-value">wp_deploy_manager</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">字符集：</span>
                <span class="detail-value">utf8mb4</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">排序规则：</span>
                <span class="detail-value">utf8mb4_unicode_ci</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">表数量：</span>
                <span class="detail-value">8个核心表</span>
            </div>
        </div>
    </div>
    
    <div class="table-list">
        <h4>创建的数据表</h4>
        <div class="tables-grid">
            <div class="table-item">
                <div class="table-icon">📋</div>
                <div class="table-info">
                    <h5>templates</h5>
                    <p>模板管理</p>
                </div>
            </div>
            
            <div class="table-item">
                <div class="table-icon">🚀</div>
                <div class="table-info">
                    <h5>deploy_jobs</h5>
                    <p>部署任务</p>
                </div>
            </div>
            
            <div class="table-item">
                <div class="table-icon">📊</div>
                <div class="table-info">
                    <h5>deploy_history</h5>
                    <p>部署历史</p>
                </div>
            </div>
            
            <div class="table-item">
                <div class="table-icon">📝</div>
                <div class="table-info">
                    <h5>system_logs</h5>
                    <p>系统日志</p>
                </div>
            </div>
            
            <div class="table-item">
                <div class="table-icon">⚙️</div>
                <div class="table-info">
                    <h5>system_config</h5>
                    <p>系统配置</p>
                </div>
            </div>
            
            <div class="table-item">
                <div class="table-icon">❤️</div>
                <div class="table-info">
                    <h5>site_health</h5>
                    <p>站点健康</p>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!$dbInitialized && !$errorMessage): ?>
        <div class="init-actions">
            <button type="button" class="btn btn-primary" onclick="initializeDatabase()">
                开始初始化数据库
            </button>
        </div>
    <?php endif; ?>
</div>

<style>
.database-content {
    max-width: 700px;
    margin: 0 auto;
}

.database-progress {
    margin: 30px 0;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.progress-step {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.progress-step.completed {
    background: #d4edda;
    border-left-color: #28a745;
}

.progress-step.pending {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    background: white;
    border: 2px solid #dee2e6;
}

.progress-step.completed .step-icon {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.progress-step.pending .step-icon {
    background: #ffc107;
    color: white;
    border-color: #ffc107;
}

.step-content h4 {
    margin-bottom: 5px;
    color: #2c3e50;
}

.step-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.database-details {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.database-details h4 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.details-grid {
    display: grid;
    gap: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #dee2e6;
}

.detail-label {
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #2c3e50;
    font-family: monospace;
}

.table-list {
    margin: 30px 0;
}

.table-list h4 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.table-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: transform 0.2s, box-shadow 0.2s;
}

.table-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.table-icon {
    font-size: 24px;
}

.table-info h5 {
    margin-bottom: 4px;
    color: #2c3e50;
    font-size: 14px;
}

.table-info p {
    margin: 0;
    color: #666;
    font-size: 12px;
}

.init-actions {
    text-align: center;
    margin: 30px 0;
}
</style>

<script>
function initializeDatabase() {
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<span class="spinner"></span> 初始化中...';
    
    // 提交表单
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    document.body.appendChild(form);
    form.submit();
}

// 如果是POST请求且成功，自动跳转到下一步
<?php if ($dbInitialized): ?>
setTimeout(function() {
    window.location.href = 'install.php?step=completion';
}, 2000);
<?php endif; ?>
</script>
