-- WordPress 部署管理系统数据库初始化脚本

-- 模板表
CREATE TABLE IF NOT EXISTS templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_hash TEXT NOT NULL,
    mother_domain TEXT,
    mother_db TEXT,
    mysql_root_pass TEXT,
    config_data TEXT, -- JSON格式的配置数据
    status TEXT DEFAULT 'active', -- active, inactive, deleted
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 部署任务表
CREATE TABLE IF NOT EXISTS deploy_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL, -- single, batch
    status TEXT DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    priority INTEGER DEFAULT 5, -- 1-10, 1最高优先级
    template_id INTEGER,
    domain TEXT,
    config_data TEXT, -- JSON格式的部署配置
    progress INTEGER DEFAULT 0, -- 0-100
    current_step TEXT,
    total_steps INTEGER DEFAULT 0,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    log_file TEXT,
    created_by TEXT DEFAULT 'system',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates(id)
);

-- 批量部署子任务表
CREATE TABLE IF NOT EXISTS deploy_sub_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parent_job_id INTEGER NOT NULL,
    domain TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, running, completed, failed, skipped
    template_id INTEGER,
    config_data TEXT, -- JSON格式的配置
    progress INTEGER DEFAULT 0,
    current_step TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    log_file TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_job_id) REFERENCES deploy_jobs(id),
    FOREIGN KEY (template_id) REFERENCES templates(id)
);

-- 部署历史表
CREATE TABLE IF NOT EXISTS deploy_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id INTEGER NOT NULL,
    domain TEXT NOT NULL,
    template_id INTEGER,
    status TEXT NOT NULL,
    duration INTEGER, -- 部署耗时（秒）
    file_size INTEGER, -- 部署文件大小
    db_size INTEGER, -- 数据库大小
    success_rate REAL, -- 成功率（批量部署时）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES deploy_jobs(id),
    FOREIGN KEY (template_id) REFERENCES templates(id)
);

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL, -- DEBUG, INFO, WARNING, ERROR, CRITICAL
    message TEXT NOT NULL,
    context TEXT, -- JSON格式的上下文数据
    source TEXT, -- 日志来源
    user_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    type TEXT DEFAULT 'string', -- string, integer, boolean, json
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 站点健康检查表
CREATE TABLE IF NOT EXISTS site_health (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain TEXT NOT NULL,
    status TEXT NOT NULL, -- healthy, warning, critical, unknown
    http_status INTEGER,
    https_status INTEGER,
    response_time REAL,
    ssl_expiry_date DATETIME,
    disk_usage REAL,
    db_status TEXT,
    wp_version TEXT,
    last_check_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    health_score INTEGER DEFAULT 0, -- 0-100
    issues TEXT, -- JSON格式的问题列表
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表（简单的会话管理）
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL,
    user_id TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_deploy_jobs_status ON deploy_jobs(status);
CREATE INDEX IF NOT EXISTS idx_deploy_jobs_type ON deploy_jobs(type);
CREATE INDEX IF NOT EXISTS idx_deploy_jobs_created_at ON deploy_jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_deploy_sub_jobs_parent ON deploy_sub_jobs(parent_job_id);
CREATE INDEX IF NOT EXISTS idx_deploy_sub_jobs_status ON deploy_sub_jobs(status);
CREATE INDEX IF NOT EXISTS idx_deploy_history_domain ON deploy_history(domain);
CREATE INDEX IF NOT EXISTS idx_deploy_history_created_at ON deploy_history(created_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_site_health_domain ON site_health(domain);
CREATE INDEX IF NOT EXISTS idx_site_health_status ON site_health(status);
CREATE INDEX IF NOT EXISTS idx_templates_status ON templates(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (key, value, description, type) VALUES
('system_name', 'WordPress部署管理系统', '系统名称', 'string'),
('max_concurrent_jobs', '3', '最大并发部署任务数', 'integer'),
('default_timeout', '1800', '默认超时时间（秒）', 'integer'),
('auto_cleanup_logs', 'true', '自动清理日志', 'boolean'),
('log_retention_days', '30', '日志保留天数', 'integer'),
('health_check_interval', '300', '健康检查间隔（秒）', 'integer'),
('enable_notifications', 'false', '启用通知', 'boolean'),
('notification_email', '', '通知邮箱', 'string'),
('backup_before_deploy', 'true', '部署前备份', 'boolean'),
('auto_ssl_setup', 'true', '自动SSL设置', 'boolean');

-- 创建触发器：自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_templates_timestamp 
    AFTER UPDATE ON templates
    BEGIN
        UPDATE templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_deploy_jobs_timestamp 
    AFTER UPDATE ON deploy_jobs
    BEGIN
        UPDATE deploy_jobs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_deploy_sub_jobs_timestamp 
    AFTER UPDATE ON deploy_sub_jobs
    BEGIN
        UPDATE deploy_sub_jobs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_system_config_timestamp 
    AFTER UPDATE ON system_config
    BEGIN
        UPDATE system_config SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 创建视图：部署统计
CREATE VIEW IF NOT EXISTS deploy_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_jobs,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_jobs,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_jobs,
    ROUND(AVG(progress), 2) as avg_progress
FROM deploy_jobs 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建视图：模板使用统计
CREATE VIEW IF NOT EXISTS template_usage_stats AS
SELECT 
    t.id,
    t.name,
    t.uuid,
    COUNT(dj.id) as usage_count,
    SUM(CASE WHEN dj.status = 'completed' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN dj.status = 'failed' THEN 1 ELSE 0 END) as failure_count,
    ROUND(
        CASE 
            WHEN COUNT(dj.id) > 0 
            THEN (SUM(CASE WHEN dj.status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(dj.id))
            ELSE 0 
        END, 2
    ) as success_rate,
    MAX(dj.created_at) as last_used_at
FROM templates t
LEFT JOIN deploy_jobs dj ON t.id = dj.template_id
WHERE t.status = 'active'
GROUP BY t.id, t.name, t.uuid
ORDER BY usage_count DESC;

-- 创建视图：系统健康概览
CREATE VIEW IF NOT EXISTS system_health_overview AS
SELECT 
    COUNT(*) as total_sites,
    SUM(CASE WHEN status = 'healthy' THEN 1 ELSE 0 END) as healthy_sites,
    SUM(CASE WHEN status = 'warning' THEN 1 ELSE 0 END) as warning_sites,
    SUM(CASE WHEN status = 'critical' THEN 1 ELSE 0 END) as critical_sites,
    SUM(CASE WHEN status = 'unknown' THEN 1 ELSE 0 END) as unknown_sites,
    ROUND(AVG(health_score), 2) as avg_health_score,
    ROUND(AVG(response_time), 3) as avg_response_time,
    MAX(last_check_at) as last_check_time
FROM site_health
WHERE last_check_at > datetime('now', '-1 day');
