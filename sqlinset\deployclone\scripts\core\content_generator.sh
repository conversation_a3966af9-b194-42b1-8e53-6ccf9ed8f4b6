#!/bin/bash

# 差异化内容生成系统
# 根据行业、地区、关键词自动生成个性化内容

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$SCRIPT_DIR/../../config"

# 加载配置文件（如果存在）
[ -f "$CONFIG_DIR/global.conf" ] && source "$CONFIG_DIR/global.conf"
[ -f "$SCRIPT_DIR/../utils/logger.sh" ] && source "$SCRIPT_DIR/../utils/logger.sh"
[ -f "$SCRIPT_DIR/../utils/json_parser.sh" ] && source "$SCRIPT_DIR/../utils/json_parser.sh"

# 如果logger函数不存在，创建简单版本
if ! command -v log_info >/dev/null 2>&1; then
    log_info() { echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_success() { echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_warning() { echo "[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
    log_error() { echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"; }
fi

# 内容模板库
declare -A CONTENT_TEMPLATES

# 初始化内容模板
init_content_templates() {
    # 企业官网模板
    CONTENT_TEMPLATES["corporate_about"]="Willkommen bei {{company_name}} - Ihrem vertrauenswürdigen Partner für {{industry_focus}}. Mit über {{years_experience}} Jahren Erfahrung bieten wir {{main_services}} für {{target_audience}}."
    
    CONTENT_TEMPLATES["corporate_services"]="Unsere Dienstleistungen umfassen {{service_list}}. Wir sind spezialisiert auf {{specialization}} und bieten {{unique_value_proposition}}."
    
    # 电商模板
    CONTENT_TEMPLATES["ecommerce_home"]="Entdecken Sie unser umfangreiches Sortiment an {{product_category}}. Von {{product_range_start}} bis {{product_range_end}} - bei uns finden Sie alles für {{customer_needs}}."
    
    CONTENT_TEMPLATES["ecommerce_category"]="{{category_name}} in Premium-Qualität. Über {{product_count}} Artikel verfügbar. {{special_offers}} und schnelle Lieferung garantiert."
    
    # 博客模板
    CONTENT_TEMPLATES["blog_about"]="{{blog_name}} ist Ihr Ratgeber für {{topic_focus}}. Hier finden Sie {{content_types}} zu {{main_topics}}. Unser Expertenteam teilt {{expertise_areas}} mit Ihnen."
    
    CONTENT_TEMPLATES["blog_post_intro"]="In diesem Artikel erfahren Sie alles über {{post_topic}}. Wir zeigen Ihnen {{what_you_learn}} und geben praktische Tipps für {{practical_application}}."
}

# 生成SEO优化的标题
generate_seo_title() {
    local domain="$1"
    local industry="$2"
    local keywords="$3"
    local template_type="$4"
    
    local company_base=$(echo "$domain" | sed 's/\..*//' | sed 's/[0-9]*$//')
    local primary_keyword=$(echo "$keywords" | cut -d',' -f1)
    
    case "$template_type" in
        "corporate")
            echo "${company_base^} - Professionelle ${industry^} Lösungen | ${primary_keyword^} Experte"
            ;;
        "ecommerce")
            echo "${primary_keyword^} Online Shop | ${company_base^} - Qualität & Service"
            ;;
        "blog")
            echo "${company_base^} Blog - Tipps & Ratgeber für ${industry^} | ${primary_keyword^}"
            ;;
        *)
            echo "${company_base^} - ${industry^} ${primary_keyword^}"
            ;;
    esac
}

# 生成SEO描述
generate_seo_description() {
    local domain="$1"
    local industry="$2"
    local keywords="$3"
    local template_type="$4"
    
    local company_base=$(echo "$domain" | sed 's/\..*//' | sed 's/[0-9]*$//')
    local keyword_array=($(echo "$keywords" | tr ',' ' '))
    
    case "$template_type" in
        "corporate")
            echo "✓ ${company_base^} - Ihr Partner für ${industry}. Professionelle Beratung ✓ Hochwertige Produkte ✓ Schneller Service. Jetzt ${keyword_array[0]} Angebot anfordern!"
            ;;
        "ecommerce")
            echo "🛒 ${keyword_array[0]^} online kaufen bei ${company_base^}. ✓ Große Auswahl ✓ Faire Preise ✓ Schnelle Lieferung. ${keyword_array[1]^} & ${keyword_array[2]^} im Shop!"
            ;;
        "blog")
            echo "📖 ${company_base^} Blog: Expertentipps zu ${industry}. Ratgeber, Tests & Anleitungen für ${keyword_array[0]}, ${keyword_array[1]} und mehr. Jetzt lesen!"
            ;;
        *)
            echo "${company_base^} - Spezialist für ${industry}. ${keyword_array[0]}, ${keyword_array[1]} und ${keyword_array[2]} in Top-Qualität."
            ;;
    esac
}

# 生成个性化内容
generate_personalized_content() {
    local domain="$1"
    local template_type="$2"
    local industry="$3"
    local custom_config="$4"
    local keywords="$5"
    
    local company_base=$(echo "$domain" | sed 's/\..*//' | sed 's/[0-9]*$//')
    local company_name="${company_base^}"
    
    # 解析自定义配置
    local target_audience=$(echo "$custom_config" | jq -r '.target_audience // "Kunden"')
    local brand_positioning=$(echo "$custom_config" | jq -r '.brand_positioning // "Qualität"')
    
    init_content_templates
    
    case "$template_type" in
        "corporate")
            generate_corporate_content "$company_name" "$industry" "$target_audience" "$brand_positioning" "$keywords"
            ;;
        "ecommerce")
            generate_ecommerce_content "$company_name" "$industry" "$custom_config" "$keywords"
            ;;
        "blog")
            generate_blog_content "$company_name" "$industry" "$custom_config" "$keywords"
            ;;
    esac
}

# 生成企业官网内容
generate_corporate_content() {
    local company_name="$1"
    local industry="$2"
    local target_audience="$3"
    local brand_positioning="$4"
    local keywords="$5"
    
    # 关于我们页面
    local about_content="${CONTENT_TEMPLATES["corporate_about"]}"
    about_content="${about_content//\{\{company_name\}\}/$company_name}"
    about_content="${about_content//\{\{industry_focus\}\}/$industry}"
    about_content="${about_content//\{\{years_experience\}\}/$(shuf -i 5-25 -n 1)}"
    about_content="${about_content//\{\{target_audience\}\}/$target_audience}"
    
    # 服务页面
    local services_content="${CONTENT_TEMPLATES["corporate_services"]}"
    local keyword_array=($(echo "$keywords" | tr ',' ' '))
    local service_list="${keyword_array[0]}, ${keyword_array[1]} und ${keyword_array[2]}"
    services_content="${services_content//\{\{service_list\}\}/$service_list}"
    services_content="${services_content//\{\{specialization\}\}/${keyword_array[0]}"
    services_content="${services_content//\{\{unique_value_proposition\}\}/$brand_positioning und erstklassigen Service}"
    
    # 输出内容
    cat << EOF
{
  "pages": {
    "about": {
      "title": "Über ${company_name} - Ihr ${industry} Spezialist",
      "content": "$about_content"
    },
    "services": {
      "title": "Unsere ${industry} Dienstleistungen",
      "content": "$services_content"
    },
    "contact": {
      "title": "Kontakt zu ${company_name}",
      "content": "Nehmen Sie Kontakt mit uns auf für eine persönliche Beratung zu ${keyword_array[0]} und ${keyword_array[1]}."
    }
  }
}
EOF
}

# 生成电商内容
generate_ecommerce_content() {
    local company_name="$1"
    local industry="$2"
    local custom_config="$3"
    local keywords="$4"
    
    local product_focus=$(echo "$custom_config" | jq -r '.product_focus // "Qualitätsprodukte"')
    local price_range=$(echo "$custom_config" | jq -r '.price_range // "fair"')
    
    local home_content="${CONTENT_TEMPLATES["ecommerce_home"]}"
    home_content="${home_content//\{\{product_category\}\}/$industry}"
    home_content="${home_content//\{\{product_range_start\}\}/$(echo "$keywords" | cut -d',' -f1)}"
    home_content="${home_content//\{\{product_range_end\}\}/$(echo "$keywords" | cut -d',' -f3)}"
    home_content="${home_content//\{\{customer_needs\}\}/Ihre $industry Bedürfnisse}"
    
    cat << EOF
{
  "pages": {
    "home": {
      "title": "${company_name} Online Shop - ${industry} in Top-Qualität",
      "content": "$home_content"
    },
    "shop": {
      "title": "${industry^} Shop - Große Auswahl bei ${company_name}",
      "content": "Entdecken Sie unser ${price_range}es Sortiment an ${product_focus}."
    }
  }
}
EOF
}

# 生成博客内容
generate_blog_content() {
    local company_name="$1"
    local industry="$2"
    local custom_config="$3"
    local keywords="$4"
    
    local content_focus=$(echo "$custom_config" | jq -r '.content_focus // "Ratgeber"')
    local posting_frequency=$(echo "$custom_config" | jq -r '.posting_frequency // "weekly"')
    
    local about_content="${CONTENT_TEMPLATES["blog_about"]}"
    about_content="${about_content//\{\{blog_name\}\}/$company_name Blog}"
    about_content="${about_content//\{\{topic_focus\}\}/$industry}"
    about_content="${about_content//\{\{content_types\}\}/$content_focus und Expertentipps}"
    about_content="${about_content//\{\{main_topics\}\}/$keywords}"
    
    cat << EOF
{
  "pages": {
    "about": {
      "title": "Über den ${company_name} ${industry} Blog",
      "content": "$about_content"
    },
    "blog": {
      "title": "${industry^} Ratgeber & Tipps | ${company_name} Blog",
      "content": "Hier finden Sie $posting_frequency neue Artikel zu $keywords und vielen weiteren Themen."
    }
  }
}
EOF
}

# 生成标准页面内容
generate_standard_pages() {
    local domain="$1"
    local company_name="$2"
    local industry="$3"
    
    cat << EOF
{
  "legal_pages": {
    "impressum": {
      "title": "Impressum - ${company_name}",
      "content": "Angaben gemäß § 5 TMG\\n\\n${company_name}\\nMusterstraße 123\\n12345 Musterstadt\\n\\nE-Mail: info@${domain}\\nTelefon: +49 (0) **********"
    },
    "datenschutz": {
      "title": "Datenschutzerklärung - ${company_name}",
      "content": "Diese Datenschutzerklärung klärt Sie über die Art, den Umfang und Zweck der Verarbeitung von personenbezogenen Daten auf unserer Website ${domain} auf."
    },
    "agb": {
      "title": "Allgemeine Geschäftsbedingungen - ${company_name}",
      "content": "Diese Allgemeinen Geschäftsbedingungen gelten für alle Verträge zwischen ${company_name} und unseren Kunden."
    }
  }
}
EOF
}

# 主函数
main() {
    local domain="$1"
    local template_type="$2"
    local industry="$3"
    local custom_config="$4"
    local keywords="$5"
    
    if [ -z "$domain" ] || [ -z "$template_type" ]; then
        echo "用法: $0 <domain> <template_type> [industry] [custom_config] [keywords]"
        exit 1
    fi
    
    log_info "生成差异化内容: $domain ($template_type)"
    
    # 生成SEO元数据
    local seo_title=$(generate_seo_title "$domain" "$industry" "$keywords" "$template_type")
    local seo_description=$(generate_seo_description "$domain" "$industry" "$keywords" "$template_type")
    
    # 生成个性化内容
    local personalized_content=$(generate_personalized_content "$domain" "$template_type" "$industry" "$custom_config" "$keywords")
    
    # 生成标准页面
    local company_name=$(echo "$domain" | sed 's/\..*//' | sed 's/[0-9]*$//' | sed 's/.*/\u&/')
    local standard_pages=$(generate_standard_pages "$domain" "$company_name" "$industry")
    
    # 输出完整内容配置
    cat << EOF
{
  "domain": "$domain",
  "seo": {
    "title": "$seo_title",
    "description": "$seo_description",
    "keywords": "$keywords"
  },
  "content": $personalized_content,
  "standard": $standard_pages
}
EOF
    
    log_success "内容生成完成: $domain"
}

# 如果直接执行此脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
