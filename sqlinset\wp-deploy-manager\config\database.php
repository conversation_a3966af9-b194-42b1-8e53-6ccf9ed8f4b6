<?php
// MySQL数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'wp_deploy_manager');
define('DB_USER', 'wp_deploy');
define('DB_PASS', '0yXdlf2AUiMo');
define('DB_CHARSET', 'utf8mb4');

// 创建PDO连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
    }
    return $pdo;
}
?>
