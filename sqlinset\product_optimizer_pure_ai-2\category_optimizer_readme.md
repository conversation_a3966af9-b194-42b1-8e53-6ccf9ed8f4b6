# 🏷️ 产品分类优化器使用说明

## 📋 功能概述

产品分类优化器专门用于优化电商产品分类层级结构，并生成相关的长尾关键词。

### 🎯 核心功能
1. **分类层级分析**: 判断提供的分类是否完整
2. **层级结构优化**: 补全缺失的父级或子级分类
3. **标准化处理**: 参考Amazon/eBay分类标准
4. **关键词生成**: 为每个分类生成1-2个相关长尾关键词

## 📂 目录结构

```
/www/imagerdown/sqlinset/product_optimizer_pure_ai/
├── category_input/          # 放置分类文件
├── category_output/         # 优化后的结果
└── category_optimizer.py    # 主脚本
```

## 📝 输入文件格式

### 支持的文件格式
- `.txt`, `.csv`, `.tsv`, `.dat`, `.text`
- 无扩展名文件

### 文件内容格式
每行一个分类名称，例如：
```
Air Filters
Living Room > Sofas
Electronics > Computers > Laptops > Gaming
Automotive Parts
Home & Garden > Furniture
Tools
```

## 🚀 使用方法

### 1. 准备分类文件
```bash
# 创建输入目录
mkdir -p /www/imagerdown/sqlinset/product_optimizer_pure_ai/category_input

# 将分类文件放入输入目录
cp your_categories.txt /www/imagerdown/sqlinset/product_optimizer_pure_ai/category_input/
```

### 2. 运行脚本

#### 交互模式 (推荐)
```bash
python3 category_optimizer.py
```

#### 自动模式
```bash
python3 category_optimizer.py auto
```

#### 后台运行
```bash
nohup python3 category_optimizer.py auto > category_processing.log 2>&1 &
```

## 📊 处理示例

### 输入分类示例
```
Air Filters
Living Room > Sofas
Gaming Laptops
Tools
Skincare
```

### 输出结果示例
```
原始分类 | 优化后层级 | 层级数 | 优化类型 | 关键词
================================================================
Air Filters | Automotive > Engine > Air Filters | 3 | completed | car air filter, engine filtration
Living Room > Sofas | Home & Garden > Furniture > Living Room > Sofas | 4 | validated | living room sofa, sectional couch
Gaming Laptops | Electronics > Computers > Laptops > Gaming | 4 | completed | gaming laptop, high performance notebook
Tools | Hardware > Tools | 2 | completed | hand tools, power tools
Skincare | Beauty > Skincare | 2 | completed | skincare products, facial care
```

## 🔧 配置选项

### 并发配置
- **智能推荐**: 根据CPU自动配置 (1-4线程)
- **自定义配置**: 手动设置线程数和批次大小

### 输出格式
- **文本格式**: 便于阅读的表格格式
- **CSV格式**: 便于导入其他系统

## 📈 性能参考

### 处理速度
- 小文件 (100-1000分类): 2-5分钟
- 中文件 (1000-5000分类): 10-30分钟
- 大文件 (5000+分类): 30-60分钟

### 优化类型说明
- **completed**: 补全了缺失的层级
- **validated**: 验证并保持原有结构
- **restructured**: 重新组织了分类结构
- **basic_completion**: 使用基础方案补全

## 🎯 使用场景

### 适用情况
1. **单级分类补全**: `Air Filters` → `Automotive > Engine > Air Filters`
2. **不完整层级**: `Living Room > Sofas` → `Home & Garden > Furniture > Living Room > Sofas`
3. **分类标准化**: 统一分类命名和层级结构
4. **关键词生成**: 为分类生成搜索相关的长尾关键词

### 典型应用
- 电商平台分类整理
- 产品目录标准化
- SEO关键词规划
- 分类层级优化

## 🔍 监控和调试

### 查看处理进度
```bash
# 实时查看日志
tail -f category_processing.log

# 检查进程状态
ps aux | grep category_optimizer

# 查看输出文件
ls -la /www/imagerdown/sqlinset/product_optimizer_pure_ai/category_output/
```

### 常见问题
1. **网络超时**: 降低线程数，增加重试次数
2. **分类识别错误**: 检查输入格式，确保每行一个分类
3. **关键词质量低**: 可能是分类名称过于简单或模糊

## 💡 最佳实践

### 输入文件准备
1. 确保分类名称清晰明确
2. 避免特殊字符和格式问题
3. 大文件建议分批处理

### 处理配置
1. 首次使用建议用智能推荐配置
2. 网络不稳定时降低并发线程数
3. 大文件处理建议使用后台模式

### 结果验证
1. 检查优化后的层级是否合理
2. 验证关键词与分类的相关性
3. 根据需要进行人工调整

## 🔄 与产品优化器的区别

| 特性 | 产品优化器 | 分类优化器 |
|------|------------|------------|
| **输入** | 产品名称 | 分类名称 |
| **输出** | SEO标题+分类+关键词 | 优化层级+关键词 |
| **重点** | 产品信息优化 | 分类结构优化 |
| **应用** | 产品上架 | 分类管理 |

## 📞 技术支持

如有问题，请检查：
1. 输入文件格式是否正确
2. 网络连接是否稳定
3. API配额是否充足
4. 系统资源是否足够
