#!/bin/bash

# 脚本路径和域名列表文件
CORE_CLONE_SCRIPT="./auto_clone_wp_core_tasks.sh" # 确保指向上面修改后的脚本
DOMAIN_LIST_FILE="domains.txt"

LOG_FILE="batch_clone_manual_nginx_$(date +%Y%m%d_%H%M%S).log"
ERROR_LOG_FILE="batch_clone_manual_nginx_error_$(date +%Y%m%d_%H%M%S).log"

# 检查核心克隆脚本
if [ ! -f "${CORE_CLONE_SCRIPT}" ]; then
    echo "错误: 核心克隆脚本 ${CORE_CLONE_SCRIPT} 未找到！" | tee -a "$ERROR_LOG_FILE"
    exit 1
fi
if [ ! -x "${CORE_CLONE_SCRIPT}" ]; then
    echo "错误: 核心克隆脚本 ${CORE_CLONE_SCRIPT} 没有执行权限！请使用 chmod +x ${CORE_CLONE_SCRIPT}" | tee -a "$ERROR_LOG_FILE"
    exit 1
fi

if [ ! -f "${DOMAIN_LIST_FILE}" ]; then
    echo "错误: 域名列表文件 ${DOMAIN_LIST_FILE} 未找到！" | tee -a "$ERROR_LOG_FILE"
    exit 1
fi

# 不再需要获取 root 密码，因为已硬编码到核心脚本中

echo "开始批量执行核心WordPress克隆任务..." | tee -a "$LOG_FILE"
# ... (日志和循环部分与之前相同) ...

SUCCESS_COUNT=0
FAIL_COUNT=0
TOTAL_DOMAINS=0

while IFS= read -r DOMAIN || [[ -n "$DOMAIN" ]]; do
    if [[ -z "$DOMAIN" || "$DOMAIN" =~ ^# ]]; then
        continue
    fi

    TOTAL_DOMAINS=$((TOTAL_DOMAINS + 1))
    echo "正在处理域名: $DOMAIN ..." | tee -a "$LOG_FILE"

    if bash "${CORE_CLONE_SCRIPT}" "$DOMAIN" >> "$LOG_FILE" 2>> "$LOG_FILE"; then
        echo "域名 $DOMAIN 核心克隆任务成功。" | tee -a "$LOG_FILE"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "错误: 域名 $DOMAIN 核心克隆任务失败。详情请查看日志。" | tee -a "$LOG_FILE"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 域名 $DOMAIN 核心克隆任务失败。" >> "$ERROR_LOG_FILE"
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
    echo "------------------------------------" | tee -a "$LOG_FILE"
    sleep 1
done < "${DOMAIN_LIST_FILE}"

# ... (日志总结部分与之前相同) ...
echo "" | tee -a "$LOG_FILE"
echo "批量核心克隆操作完成。" | tee -a "$LOG_FILE"
echo "总共处理域名: $TOTAL_DOMAINS" | tee -a "$LOG_FILE"
echo "成功数量: $SUCCESS_COUNT" | tee -a "$LOG_FILE"
echo "失败数量: $FAIL_COUNT" | tee -a "$LOG_FILE"
if [ "$FAIL_COUNT" -gt 0 ]; then
    echo "请检查错误日志: ${ERROR_LOG_FILE}" | tee -a "$LOG_FILE"
fi