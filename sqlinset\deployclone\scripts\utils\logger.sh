#!/bin/bash

# 统一日志记录工具
# 提供标准化的日志输出格式

# 日志级别
LOG_LEVEL_DEBUG=0
LOG_LEVEL_INFO=1
LOG_LEVEL_WARNING=2
LOG_LEVEL_ERROR=3
LOG_LEVEL_CRITICAL=4

# 当前日志级别（默认INFO）
CURRENT_LOG_LEVEL=${LOG_LEVEL:-$LOG_LEVEL_INFO}

# 颜色定义
COLOR_RESET='\033[0m'
COLOR_DEBUG='\033[0;36m'    # 青色
COLOR_INFO='\033[0;32m'     # 绿色
COLOR_WARNING='\033[0;33m'  # 黄色
COLOR_ERROR='\033[0;31m'    # 红色
COLOR_CRITICAL='\033[1;31m' # 粗体红色

# 日志文件路径
LOG_FILE="${LOG_FILE:-/tmp/deployment.log}"

# 内部日志函数
_log() {
    local level="$1"
    local level_num="$2"
    local color="$3"
    local message="$4"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 检查日志级别
    if [ "$level_num" -lt "$CURRENT_LOG_LEVEL" ]; then
        return 0
    fi
    
    # 格式化消息
    local formatted_message="[$level] $timestamp - $message"
    
    # 输出到控制台（带颜色）
    if [ -t 1 ]; then  # 检查是否为终端
        echo -e "${color}${formatted_message}${COLOR_RESET}"
    else
        echo "$formatted_message"
    fi
    
    # 输出到日志文件（不带颜色）
    if [ -n "$LOG_FILE" ]; then
        echo "$formatted_message" >> "$LOG_FILE"
    fi
}

# 公共日志函数
log_debug() {
    _log "DEBUG" "$LOG_LEVEL_DEBUG" "$COLOR_DEBUG" "$1"
}

log_info() {
    _log "INFO" "$LOG_LEVEL_INFO" "$COLOR_INFO" "$1"
}

log_success() {
    _log "SUCCESS" "$LOG_LEVEL_INFO" "$COLOR_INFO" "✓ $1"
}

log_warning() {
    _log "WARNING" "$LOG_LEVEL_WARNING" "$COLOR_WARNING" "⚠ $1"
}

log_error() {
    _log "ERROR" "$LOG_LEVEL_ERROR" "$COLOR_ERROR" "✗ $1"
}

log_critical() {
    _log "CRITICAL" "$LOG_LEVEL_CRITICAL" "$COLOR_CRITICAL" "🚨 $1"
}

# 设置日志级别
set_log_level() {
    case "$1" in
        "debug"|"DEBUG")
            CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG
            ;;
        "info"|"INFO")
            CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO
            ;;
        "warning"|"WARNING")
            CURRENT_LOG_LEVEL=$LOG_LEVEL_WARNING
            ;;
        "error"|"ERROR")
            CURRENT_LOG_LEVEL=$LOG_LEVEL_ERROR
            ;;
        "critical"|"CRITICAL")
            CURRENT_LOG_LEVEL=$LOG_LEVEL_CRITICAL
            ;;
        *)
            log_warning "未知日志级别: $1，使用默认级别 INFO"
            CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO
            ;;
    esac
}

# 设置日志文件
set_log_file() {
    LOG_FILE="$1"
    # 确保日志目录存在
    local log_dir=$(dirname "$LOG_FILE")
    mkdir -p "$log_dir"
}

# 日志轮转
rotate_log() {
    if [ -f "$LOG_FILE" ]; then
        local backup_file="${LOG_FILE}.$(date +%Y%m%d_%H%M%S)"
        mv "$LOG_FILE" "$backup_file"
        log_info "日志文件已轮转: $backup_file"
    fi
}

# 清理旧日志
cleanup_old_logs() {
    local log_dir=$(dirname "$LOG_FILE")
    local days="${1:-7}"  # 默认保留7天
    
    find "$log_dir" -name "*.log.*" -mtime +$days -delete 2>/dev/null
    log_info "已清理 $days 天前的旧日志文件"
}

# 进度条显示
show_progress() {
    local current="$1"
    local total="$2"
    local message="${3:-Processing}"
    local width=50
    
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r$message: ["
    printf "%${filled}s" | tr ' ' '='
    printf "%${empty}s" | tr ' ' '-'
    printf "] %d%% (%d/%d)" "$percentage" "$current" "$total"
    
    if [ "$current" -eq "$total" ]; then
        echo ""  # 换行
    fi
}

# 计时器
start_timer() {
    TIMER_START=$(date +%s)
}

end_timer() {
    local timer_end=$(date +%s)
    local duration=$((timer_end - TIMER_START))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    
    if [ $hours -gt 0 ]; then
        echo "${hours}h ${minutes}m ${seconds}s"
    elif [ $minutes -gt 0 ]; then
        echo "${minutes}m ${seconds}s"
    else
        echo "${seconds}s"
    fi
}

# 错误处理
handle_error() {
    local exit_code="$1"
    local line_number="$2"
    local command="$3"
    
    log_error "脚本执行失败 (退出码: $exit_code, 行号: $line_number)"
    log_error "失败命令: $command"
    
    # 可选：发送告警通知
    if command -v mail >/dev/null 2>&1 && [ -n "$ALERT_EMAIL" ]; then
        echo "脚本执行失败: $command (行号: $line_number)" | mail -s "部署脚本错误" "$ALERT_EMAIL"
    fi
    
    exit "$exit_code"
}

# 设置错误陷阱
set_error_trap() {
    set -eE  # 启用错误退出和ERR陷阱继承
    trap 'handle_error $? $LINENO "$BASH_COMMAND"' ERR
}

# 禁用错误陷阱
unset_error_trap() {
    set +eE
    trap - ERR
}

# 示例用法函数
demo_logger() {
    echo "=== 日志系统演示 ==="
    
    log_debug "这是调试信息"
    log_info "这是普通信息"
    log_success "这是成功信息"
    log_warning "这是警告信息"
    log_error "这是错误信息"
    log_critical "这是严重错误信息"
    
    echo ""
    echo "=== 进度条演示 ==="
    for i in $(seq 1 10); do
        show_progress $i 10 "处理中"
        sleep 0.1
    done
    
    echo ""
    echo "=== 计时器演示 ==="
    start_timer
    sleep 2
    local elapsed=$(end_timer)
    log_info "操作耗时: $elapsed"
}

# 如果直接执行此脚本，运行演示
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    demo_logger
fi
