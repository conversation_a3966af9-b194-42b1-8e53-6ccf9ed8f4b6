#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer.py Requests版本 - 服务器优化版
使用requests库，增强错误处理，更适合服务器环境
核心功能保持不变：AI智能分析，无硬编码
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# DeepSeek API配置
API_KEY = "***********************************"
API_URL = "https://api.deepseek.com/v1/chat/completions"

# 并发配置 - 服务器优化
MAX_WORKERS = 6  # 服务器可以支持更多线程
BATCH_SIZE = 20  # 服务器处理能力更强
REQUEST_DELAY = 0.1  # 服务器网络更稳定

# 语言配置
LANGUAGE_CONFIG = {
    "en": {"locale": "en-US", "name": "英语(美式)"},
    "fr": {"locale": "fr-FR", "name": "法语"},
    "es": {"locale": "es-ES", "name": "西班牙语"},
    "de": {"locale": "de-DE", "name": "德语"},
    "auto": {"locale": "auto", "name": "自动检测"}
}

# 统计锁
stats_lock = threading.Lock()
processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_products": 0,
    "failed_products": 0
}

def create_session():
    """创建带重试策略的requests会话"""
    session = requests.Session()

    try:
        # 配置重试策略 - 兼容新旧版本
        try:
            # 新版本urllib3使用allowed_methods
            retry_strategy = Retry(
                total=3,  # 总重试次数
                backoff_factor=1,  # 退避因子
                status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
                allowed_methods=["HEAD", "GET", "POST"]
            )
        except TypeError:
            # 旧版本urllib3使用method_whitelist
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
                method_whitelist=["HEAD", "GET", "POST"]
            )

        # 配置适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

    except Exception as e:
        print(f"⚠️ 重试策略配置失败，使用基础配置: {e}")
        # 使用基础适配器
        adapter = HTTPAdapter()
        session.mount("http://", adapter)
        session.mount("https://", adapter)

    # 设置默认超时
    session.timeout = 60

    return session

def ai_optimize_products_requests(batch_data, target_lang="auto", max_retries=3):
    """使用requests的AI产品优化"""
    batch_num, products_batch = batch_data
    lang_config = LANGUAGE_CONFIG.get(target_lang, LANGUAGE_CONFIG["auto"])
    lang_name = lang_config["name"]
    
    # 英文提示词 - 更好的AI理解和处理效果
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: {lang_name}
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    # 创建会话
    session = create_session()
    
    for attempt in range(max_retries):
        try:
            # 随机延迟避免并发冲突
            time.sleep(random.uniform(0, REQUEST_DELAY * 2))
            
            # 请求数据
            data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.8
            }
            
            # 请求头
            headers = {
                'Authorization': f'Bearer {API_KEY}',
                'Content-Type': 'application/json'
            }
            
            # 发送请求 - 使用requests简洁语法
            response = session.post(
                API_URL,
                json=data,  # 自动处理JSON编码
                headers=headers,
                timeout=60
            )
            
            # 检查响应状态
            response.raise_for_status()  # 自动抛出HTTP错误
            
            # 解析响应
            result = response.json()  # 自动解析JSON
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()
                
                try:
                    # 清理JSON内容
                    clean_content = content.strip()
                    
                    # 移除markdown标记
                    if "```json" in clean_content:
                        start_idx = clean_content.find("```json") + 7
                        end_idx = clean_content.find("```", start_idx)
                        if end_idx > start_idx:
                            clean_content = clean_content[start_idx:end_idx].strip()
                    
                    # 解析JSON
                    parsed_result = json.loads(clean_content)
                    
                    # 提取结果数组
                    if isinstance(parsed_result, dict) and "results" in parsed_result:
                        results_array = parsed_result["results"]
                    elif isinstance(parsed_result, list):
                        results_array = parsed_result
                    else:
                        results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                    
                    # 清理和优化结果
                    cleaned_results = []
                    for result in results_array:
                        # 清理SEO标题中的管道符
                        seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                        
                        # 获取分类
                        category = result.get("category", "")
                        
                        # 优化关键词
                        tags = result.get("tags", "")
                        tags = optimize_tags(tags)
                        
                        cleaned_result = {
                            "seo_name": seo_name,
                            "category": category,
                            "tags": tags
                        }
                        cleaned_results.append(cleaned_result)
                    
                    # 更新统计
                    with stats_lock:
                        processing_stats["completed_batches"] += 1
                        processing_stats["successful_products"] += len(cleaned_results)
                        
                        # 显示进度
                        completed = processing_stats["completed_batches"]
                        total = processing_stats["total_batches"]
                        elapsed = time.time() - processing_stats["start_time"]
                        
                        if completed > 0:
                            rate = completed / elapsed
                            eta = (total - completed) / rate if rate > 0 else 0
                            progress = (completed / total) * 100
                            
                            print(f"✅ 批次 {batch_num} 完成 | 进度: {progress:.1f}% ({completed}/{total}) | ETA: {eta/60:.1f}分钟")
                    
                    return batch_num, cleaned_results
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ 批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        return batch_num, generate_basic_results(products_batch)
            
        except requests.exceptions.RequestException as e:
            print(f"⚠️ 批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
        except Exception as e:
            print(f"⚠️ 批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    # 所有尝试失败
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_products"] += len(products_batch)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用基础结果")
    return batch_num, generate_basic_results(products_batch)

def optimize_tags(tags):
    """优化关键词质量"""
    if not tags:
        return ""
    
    # 分割并清理关键词
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    
    # 限制数量和长度
    optimized_tags = []
    for tag in tag_list[:2]:  # 最多2个
        # 限制单个关键词长度
        if len(tag) <= 25:
            optimized_tags.append(tag)
    
    result = ", ".join(optimized_tags)
    
    # 总长度控制
    if len(result) > 30:
        result = optimized_tags[0] if optimized_tags else ""
    
    return result

def generate_basic_results(products_batch):
    """生成基础结果（备用方案）"""
    results = []
    
    for product in products_batch:
        # 简单的产品分析
        words = product.split()
        brand = words[0] if words else "Unknown"
        
        results.append({
            "seo_name": product.replace("|", "-"),
            "category": "General > Products",
            "tags": f"{brand} products"
        })
    
    return results

def analyze_results_quality(results):
    """分析结果质量"""
    if not results:
        return {"avg_category_levels": 0, "avg_tags_length": 0, "success_rate": 0}
    
    total_levels = 0
    total_tags_length = 0
    successful_count = 0
    
    for result in results:
        category = result.get("category", "")
        tags = result.get("tags", "")
        
        if category and category != "General > Products":
            successful_count += 1
            levels = len(category.split(" > ")) if " > " in category else 1
            total_levels += levels
        
        if tags:
            total_tags_length += len(tags)
    
    return {
        "avg_category_levels": total_levels / len(results) if results else 0,
        "avg_tags_length": total_tags_length / len(results) if results else 0,
        "success_rate": (successful_count / len(results)) * 100 if results else 0
    }

def check_dependencies():
    """检查依赖库"""
    try:
        import requests
        print(f"✅ requests版本: {requests.__version__}")
        return True
    except ImportError:
        print("❌ 错误: 未安装requests库")
        print("请运行: pip install requests")
        return False

def detect_optimal_workers():
    """智能检测最优线程数"""
    import multiprocessing

    # 获取CPU信息
    cpu_count = multiprocessing.cpu_count()

    print(f"\n🖥️ 系统资源检测:")
    print(f"   CPU核心数: {cpu_count}")

    # 基于CPU核心数计算推荐线程数
    if cpu_count >= 8:
        recommended = min(8, cpu_count)
        level = "高性能服务器"
    elif cpu_count >= 4:
        recommended = min(6, cpu_count)
        level = "标准服务器"
    else:
        recommended = min(4, cpu_count)
        level = "基础配置"

    print(f"   推荐配置: {level} ({recommended} 线程)")
    print(f"   ⚠️ 注意: API并发限制通常为5-10个/秒")
    print(f"   💡 建议: 从较少线程开始测试，逐步增加")

    return recommended

def test_api_performance():
    """测试API响应性能"""
    print(f"\n🔬 API性能测试...")

    try:
        start_time = time.time()

        # 简单测试请求 - 不使用复杂的重试策略
        test_data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }

        headers = {
            'Authorization': f'Bearer {API_KEY}',
            'Content-Type': 'application/json'
        }

        # 使用简单的requests.post
        response = requests.post(API_URL, json=test_data, headers=headers, timeout=30)
        response.raise_for_status()

        elapsed = time.time() - start_time
        print(f"   ✅ API响应时间: {elapsed:.2f} 秒")

        if elapsed < 2:
            return "快速"
        elif elapsed < 5:
            return "正常"
        else:
            return "较慢"

    except Exception as e:
        print(f"   ⚠️ API测试失败: {e}")
        print(f"   💡 这不影响正常使用，继续处理...")
        return "未知"

def process_file_requests_server(input_path, output_path=None, target_lang=None, max_workers=MAX_WORKERS, batch_size=BATCH_SIZE, output_format="txt"):
    """使用requests的服务器优化版文件处理"""
    print(f"🚀 Requests版产品优化器 - 服务器版")

    # 处理绝对路径
    if not os.path.isabs(input_path):
        # 如果是相对路径，转换为绝对路径
        input_path = os.path.abspath(input_path)

    print(f"📁 输入文件: {input_path}")
    print(f"🔧 并发配置: {max_workers} 个线程，{batch_size} 个产品/批次")
    print(f"🖥️ 运行环境: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {platform.python_version()}")

    # 检查依赖
    if not check_dependencies():
        return False

    # 检查文件
    if not os.path.exists(input_path):
        print(f"❌ 错误: 文件不存在 - {input_path}")
        print(f"💡 提示: 请检查文件路径是否正确")
        print(f"📂 当前工作目录: {os.getcwd()}")
        return False

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 文件大小: {len(products):,} 行")

    # 语言设置
    if target_lang is None:
        target_lang = "en"

    # 设置输出文件
    if output_path is None:
        base_name = os.path.splitext(input_path)[0]
        if output_format == "csv":
            output_path = f"{base_name}_requests.csv"
        else:
            output_path = f"{base_name}_requests.txt"

    print(f"📄 输出文件: {output_path}")

    # 计算性能预估
    total_batches = (len(products) + batch_size - 1) // batch_size
    serial_time_estimate = total_batches * 3  # requests更快，预估3秒/批次
    concurrent_time_estimate = (total_batches / max_workers) * 3
    speedup = serial_time_estimate / concurrent_time_estimate

    print(f"\n📈 性能预估:")
    print(f"   总批次数: {total_batches}")
    print(f"   串行预估: {serial_time_estimate/60:.1f} 分钟")
    print(f"   并发预估: {concurrent_time_estimate/60:.1f} 分钟")
    print(f"   性能提升: {speedup:.1f}x 倍")

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), batch_size):
        batch_num = (i // batch_size) + 1
        batch = products[i:i+batch_size]
        batch_data.append((batch_num, batch))

    # 初始化统计
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0

    print(f"\n🔄 开始Requests并发处理 ({len(batch_data)} 个批次，{max_workers} 个线程)...")

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_requests, batch, target_lang): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                print(f"❌ 批次 {batch_num} 执行异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    # 保存结果
    try:
        if output_format == "csv":
            with open(output_path, 'w', encoding='utf-8-sig') as f:
                f.write("seo_name,category,tags\n")
                for result in final_results:
                    seo_name = result.get("seo_name", "").replace('"', '""')
                    category = result.get("category", "").replace('"', '""')
                    tags = result.get("tags", "").replace('"', '""')
                    f.write(f'"{seo_name}","{category}","{tags}"\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in final_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_products"]
        failed = processing_stats["failed_products"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        # 分析结果质量
        quality_stats = analyze_results_quality(final_results)

        print(f"\n✅ Requests处理完成!")
        print(f"📊 成功处理: {successful:,} 个产品")
        print(f"📊 失败产品: {failed:,} 个产品")
        print(f"📈 成功率: {success_rate:.1f}%")
        print(f"⏱️ 总时间: {elapsed_time/60:.1f} 分钟")
        print(f"🚀 处理速度: {len(products)/(elapsed_time/60):.0f} 产品/分钟")

        print(f"\n📊 AI分析质量:")
        print(f"   平均分类层级: {quality_stats['avg_category_levels']:.1f} 级")
        print(f"   平均关键词长度: {quality_stats['avg_tags_length']:.1f} 字符")
        print(f"   AI成功率: {quality_stats['success_rate']:.1f}%")

        print(f"💾 结果保存到: {output_path}")

        return True

    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def process_multiple_files(file_patterns, output_dir=None, target_lang=None, max_workers=MAX_WORKERS, batch_size=BATCH_SIZE, output_format="txt"):
    """处理多个文件"""
    import glob

    print(f"📁 多文件处理模式")
    print(f"🔍 文件模式: {file_patterns}")

    # 查找匹配的文件
    all_files = []
    for pattern in file_patterns:
        if not os.path.isabs(pattern):
            pattern = os.path.abspath(pattern)
        matched_files = glob.glob(pattern)
        all_files.extend(matched_files)

    # 去重并排序
    all_files = sorted(list(set(all_files)))

    if not all_files:
        print(f"❌ 错误: 未找到匹配的文件")
        print(f"📂 当前工作目录: {os.getcwd()}")
        return False

    print(f"📊 找到 {len(all_files)} 个文件:")
    for i, file_path in enumerate(all_files, 1):
        file_size = os.path.getsize(file_path) / (1024*1024)  # MB
        print(f"   {i}. {os.path.basename(file_path)} ({file_size:.1f} MB)")

    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.dirname(all_files[0]) if all_files else os.getcwd()

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"📄 输出目录: {output_dir}")

    # 处理每个文件
    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(all_files, 1):
        print(f"\n{'='*60}")
        print(f"🔄 处理文件 {i}/{len(all_files)}: {os.path.basename(input_file)}")
        print(f"{'='*60}")

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        if output_format == "csv":
            output_file = os.path.join(output_dir, f"{base_name}_requests.csv")
        else:
            output_file = os.path.join(output_dir, f"{base_name}_requests.txt")

        # 处理单个文件
        success = process_file_requests_server(
            input_file,
            output_file,
            target_lang,
            max_workers,
            batch_size,
            output_format
        )

        if success:
            success_count += 1
            print(f"✅ 文件 {i} 处理完成")
        else:
            print(f"❌ 文件 {i} 处理失败")

    # 总结
    total_elapsed = time.time() - total_start_time
    print(f"\n{'='*60}")
    print(f"🎉 多文件处理完成!")
    print(f"📊 成功处理: {success_count}/{len(all_files)} 个文件")
    print(f"⏱️ 总耗时: {total_elapsed/60:.1f} 分钟")
    print(f"📁 输出目录: {output_dir}")
    print(f"{'='*60}")

    return success_count == len(all_files)

def main():
    """主函数"""
    print("🚀 Requests版产品优化器 - 服务器优化版")
    print("="*60)
    print(f"🎯 核心特色:")
    print(f"   ✅ 使用requests库 (更强大的HTTP功能)")
    print(f"   ✅ 完全AI智能分析 (无硬编码)")
    print(f"   ✅ 支持任何产品类型")
    print(f"   ✅ 智能分类生成 (1-4级)")
    print(f"   ✅ SEO标题优化")
    print(f"   ✅ 精准关键词生成")
    print(f"   ✅ 并发处理提速")
    print(f"   ✅ 服务器环境优化")
    print(f"   ✅ 增强错误处理和重试机制")

    # 检查依赖
    if not check_dependencies():
        return

    # 智能检测最优配置
    recommended_workers = detect_optimal_workers()
    api_performance = test_api_performance()

    print(f"\n📊 性能评估:")
    print(f"   API响应速度: {api_performance}")
    print(f"   推荐线程数: {recommended_workers}")

    # 命令行模式
    if len(sys.argv) > 1:
        input_arg = sys.argv[1]
        output_format = sys.argv[2] if len(sys.argv) > 2 else "txt"
        max_workers = int(sys.argv[3]) if len(sys.argv) > 3 else MAX_WORKERS

        print(f"\n📋 命令行模式:")
        print(f"   输入参数: {input_arg}")
        print(f"   输出格式: {output_format}")
        print(f"   并发线程: {max_workers}")

        # 检查是否为多文件模式
        if '*' in input_arg or '?' in input_arg:
            # 通配符模式 - 多文件处理
            process_multiple_files([input_arg], max_workers=max_workers, output_format=output_format)
        elif os.path.isdir(input_arg):
            # 目录模式 - 处理目录中的所有txt文件
            import glob
            pattern = os.path.join(input_arg, "*.txt")
            process_multiple_files([pattern], max_workers=max_workers, output_format=output_format)
        else:
            # 单文件模式
            process_file_requests_server(input_arg, max_workers=max_workers, output_format=output_format)
        return

    # 交互模式 - 显示当前目录文件
    print(f"\n� 当前目录: {os.getcwd()}")

    # 查找当前目录的txt文件
    txt_files = glob.glob("*.txt")
    if txt_files:
        print(f"\n📋 发现的txt文件:")
        for i, file in enumerate(txt_files, 1):
            file_size = os.path.getsize(file) / (1024*1024)  # MB
            print(f"   {i}. {file} ({file_size:.1f} MB)")

    print(f"\n📋 请选择处理模式:")
    if txt_files:
        print(f"1-{len(txt_files)}. 选择上述文件 (输入对应数字)")
    print(f"📁 指定文件路径 (输入完整路径)")
    print(f"📂 处理目录 (输入目录路径)")
    print(f"🔍 通配符模式 (如: /path/*.txt)")

    # 服务器环境提示
    print(f"\n⚡ 服务器优化配置: {MAX_WORKERS} 线程, {BATCH_SIZE} 产品/批次")
    print(f"📈 预估速度提升: 4-6倍 (相比串行处理)")

    choice = input(f"\n请选择 (1-{len(txt_files) if txt_files else 0} 或输入路径): ").strip()

    # 处理用户选择
    try:
        # 尝试解析为数字 (选择文件列表中的文件)
        file_index = int(choice)
        if 1 <= file_index <= len(txt_files):
            file_path = txt_files[file_index - 1]
            is_multi_file = False
            print(f"✅ 选择文件: {file_path}")
        else:
            print("❌ 文件编号超出范围")
            return
    except ValueError:
        # 不是数字，当作路径处理
        file_path = choice.strip()

        if os.path.isdir(file_path):
            # 目录模式
            is_multi_file = True
            print(f"📂 将处理目录: {file_path}")
        elif '*' in file_path or '?' in file_path:
            # 通配符模式
            is_multi_file = True
            print(f"🔍 将处理模式: {file_path}")
        elif os.path.isfile(file_path):
            # 单文件模式
            is_multi_file = False
            print(f"� 将处理文件: {file_path}")
        else:
            print(f"❌ 文件或路径不存在: {file_path}")
            print(f"💡 提示: 请检查路径是否正确")
            return

    # 并发配置选择
    print(f"\n🚀 并发配置:")
    print(f"1. 智能推荐配置 ({recommended_workers} 线程) - 基于系统检测")
    print(f"2. 服务器标准配置 ({MAX_WORKERS} 线程)")
    print(f"3. 高性能配置 (8 线程) - 需要良好网络")
    print(f"4. 自定义配置")

    speed_choice = input("请选择 (1/2/3/4): ").strip()

    if speed_choice == "1":
        max_workers = recommended_workers
        batch_size = BATCH_SIZE
        print(f"🤖 使用智能推荐配置: {max_workers} 线程, {batch_size} 产品/批次")
    elif speed_choice == "2":
        max_workers = MAX_WORKERS
        batch_size = BATCH_SIZE
        print(f"📊 使用服务器标准配置: {max_workers} 线程, {batch_size} 产品/批次")
    elif speed_choice == "3":
        max_workers = 8
        batch_size = 25
        print(f"⚡ 使用高性能配置: {max_workers} 线程, {batch_size} 产品/批次")
        print(f"⚠️ 注意: 高性能配置需要稳定网络和足够的API配额")
    elif speed_choice == "4":
        try:
            max_workers = int(input("请输入线程数 (1-12): "))
            batch_size = int(input("请输入批次大小 (10-50): "))
            max_workers = max(1, min(12, max_workers))
            batch_size = max(10, min(50, batch_size))
            print(f"🔧 自定义配置: {max_workers} 线程, {batch_size} 产品/批次")
        except:
            max_workers = recommended_workers
            batch_size = BATCH_SIZE
            print(f"⚠️ 输入无效，使用智能推荐配置")
    else:
        max_workers = recommended_workers
        batch_size = BATCH_SIZE

    # 输出格式选择
    print(f"\n📋 选择输出格式:")
    print(f"1. 文本格式 (.txt)")
    print(f"2. CSV格式 (.csv)")

    format_choice = input("请选择格式 (1/2): ").strip()
    output_format = "csv" if format_choice == "2" else "txt"

    # 开始处理
    if is_multi_file:
        if choice == "5":
            # 目录模式
            import glob
            pattern = os.path.join(file_path, "*.txt")
            process_multiple_files([pattern], max_workers=max_workers, batch_size=batch_size, output_format=output_format)
        else:
            # 通配符模式
            process_multiple_files([file_path], max_workers=max_workers, batch_size=batch_size, output_format=output_format)
    else:
        # 单文件模式
        process_file_requests_server(file_path, max_workers=max_workers, batch_size=batch_size, output_format=output_format)

if __name__ == "__main__":
    main()
