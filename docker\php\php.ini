[PHP]
; 基础配置
engine = On
short_open_tag = Off
precision = 14
output_buffering = 4096
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1
disable_functions =
disable_classes =
zend.enable_gc = On
zend.exception_ignore_args = On

; 内存和执行时间
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

; 文件上传
file_uploads = On
upload_max_filesize = 500M
max_file_uploads = 20
post_max_size = 500M

; 错误报告
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = On
display_startup_errors = On
log_errors = On
log_errors_max_len = 1024
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
html_errors = On
error_log = /var/log/php_errors.log

; 数据处理
variables_order = "GPCS"
request_order = "GP"
register_argc_argv = Off
auto_globals_jit = On

; 路径和目录
include_path = ".:/usr/local/lib/php"
doc_root =
user_dir =
enable_dl = Off

; 会话配置
session.save_handler = files
session.save_path = "/tmp"
session.use_strict_mode = 1
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly = 1
session.cookie_secure = 0
session.cookie_samesite = "Lax"
session.serialize_handler = php
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.sid_length = 26
session.trans_sid_tags = "a=href,area=href,frame=src,form="
session.sid_bits_per_character = 5

; 安全配置
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; 时区
date.timezone = "Asia/Shanghai"

; 扩展配置
[curl]
curl.cainfo = "/etc/ssl/certs/ca-certificates.crt"

[openssl]
openssl.cafile = "/etc/ssl/certs/ca-certificates.crt"
openssl.capath = "/etc/ssl/certs"

[mysql]
mysql.allow_local_infile = On
mysql.allow_persistent = On
mysql.cache_size = 2000
mysql.max_persistent = -1
mysql.max_links = -1
mysql.default_port =
mysql.default_socket =
mysql.default_host =
mysql.default_user =
mysql.default_password =
mysql.connect_timeout = 60
mysql.trace_mode = Off

[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.cache_size = 2000
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

[PDO]
pdo_mysql.cache_size = 2000
pdo_mysql.default_socket =

[mbstring]
mbstring.language = UTF-8
mbstring.internal_encoding = UTF-8
mbstring.http_input = UTF-8
mbstring.http_output = UTF-8
mbstring.encoding_translation = Off
mbstring.detect_order = auto
mbstring.substitute_character = none

[gd]
gd.jpeg_ignore_warning = 1

[opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.save_comments = 1

[redis]
redis.session.locking_enabled = 1
redis.session.lock_expire = 30
redis.session.lock_wait_time = 50000
