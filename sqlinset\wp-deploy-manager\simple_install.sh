#!/bin/bash

# WordPress 部署管理系统 - 简化安装脚本
# 使用方法: ./simple_install.sh wpd.cloudcheckout.shop your_mysql_password

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置参数
DOMAIN="$1"
MYSQL_ROOT_PASS="$2"
PROJECT_DIR="/www/wwwroot/$DOMAIN"

# 检查参数
if [ -z "$DOMAIN" ]; then
    echo -e "${RED}错误: 请提供域名${NC}"
    echo "使用方法: $0 域名 MySQL密码"
    echo "例如: $0 wpd.cloudcheckout.shop your_mysql_password"
    exit 1
fi

if [ -z "$MYSQL_ROOT_PASS" ]; then
    echo -e "${RED}错误: 请提供MySQL root密码${NC}"
    echo "使用方法: $0 域名 MySQL密码"
    echo "例如: $0 wpd.cloudcheckout.shop your_mysql_password"
    exit 1
fi

echo -e "${BLUE}=================================================="
echo "  WordPress 部署管理系统 - 简化安装"
echo "  域名: $DOMAIN"
echo "  项目目录: $PROJECT_DIR"
echo "==================================================${NC}"

# 步骤1: 检查目录
echo -e "${BLUE}[1/5]${NC} 检查项目目录..."
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录不存在: $PROJECT_DIR${NC}"
    echo "请先在宝塔面板创建站点: $DOMAIN"
    exit 1
fi

cd "$PROJECT_DIR"

# 步骤2: 设置权限
echo -e "${BLUE}[2/5]${NC} 设置文件权限..."
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod +x scripts/*.sh 2>/dev/null || true

# 创建必要目录
mkdir -p logs uploads/temp templates/uploads templates/configs

# 步骤3: 创建MySQL数据库
echo -e "${BLUE}[3/5]${NC} 创建MySQL数据库..."
DB_NAME="wp_deploy_manager"
DB_USER="wp_deploy"
DB_PASS=$(openssl rand -base64 12 | tr -d '=+/' | cut -c1-12)

# 测试MySQL连接
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${RED}错误: MySQL连接失败，请检查密码${NC}"
    exit 1
fi

mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
DROP DATABASE IF EXISTS $DB_NAME;
CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
DROP USER IF EXISTS '$DB_USER'@'localhost';
CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

echo -e "${GREEN}数据库创建成功${NC}"

# 步骤4: 创建配置文件
echo -e "${BLUE}[4/5]${NC} 创建配置文件..."

# 创建数据库配置
cat > config/database.php << EOF
<?php
// MySQL数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', '$DB_NAME');
define('DB_USER', '$DB_USER');
define('DB_PASS', '$DB_PASS');
define('DB_CHARSET', 'utf8mb4');

// 创建PDO连接
function getDatabase() {
    static \$pdo = null;
    if (\$pdo === null) {
        \$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
    }
    return \$pdo;
}
?>
EOF

# 创建应用配置
cat > config/app.json << EOF
{
    "mysql_root_password": "$MYSQL_ROOT_PASS",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "",
    "backup_before_deploy": true,
    "auto_ssl_setup": true,
    "domain": "$DOMAIN"
}
EOF

echo -e "${GREEN}配置文件创建成功${NC}"

# 步骤5: 初始化数据库表
echo -e "${BLUE}[5/5]${NC} 初始化数据库表..."

if [ -f "database/init_mysql.sql" ]; then
    mysql -u root -p"$MYSQL_ROOT_PASS" "$DB_NAME" < database/init_mysql.sql
    echo -e "${GREEN}数据库表初始化成功${NC}"
else
    echo -e "${YELLOW}警告: 数据库初始化文件不存在，请手动初始化${NC}"
fi

# 完成安装
echo -e "${GREEN}=================================================="
echo "  安装完成！"
echo "=================================================="
echo "访问地址: http://$DOMAIN"
echo "数据库名: $DB_NAME"
echo "数据库用户: $DB_USER"
echo "数据库密码: $DB_PASS"
echo "项目目录: $PROJECT_DIR"
echo "==================================================${NC}"

echo -e "${YELLOW}下一步操作:${NC}"
echo "1. 访问 http://$DOMAIN 开始使用"
echo "2. 上传WordPress模板"
echo "3. 开始部署站点"
echo ""

# 检查网站是否可访问
echo -e "${BLUE}正在检查网站访问...${NC}"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" --max-time 10 || echo "000")

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "301" ] || [ "$HTTP_CODE" = "302" ]; then
    echo -e "${GREEN}✅ 网站访问正常 (HTTP $HTTP_CODE)${NC}"
else
    echo -e "${YELLOW}⚠️  网站可能需要几分钟才能访问 (HTTP $HTTP_CODE)${NC}"
fi

echo -e "${GREEN}🎉 安装成功！${NC}"
