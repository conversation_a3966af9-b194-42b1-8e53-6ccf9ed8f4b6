#!/bin/bash

# 定义图片目录和删除列表文件
image_dir="imagewebp"
delete_list="delete_list.txt"

# 检查删除列表文件是否存在
if [ ! -f "$delete_list" ]; then
  echo "删除列表文件 $delete_list 不存在。"
  exit 1
fi

# 逐行读取删除列表文件并删除对应的文件
while IFS= read -r file; do
  echo "正在处理文件: $file"
  if [ -f "$image_dir/$file" ]; then
    rm "$image_dir/$file"
    echo "已删除 $image_dir/$file"
  else
    echo "文件 $image_dir/$file 不存在。"
  fi
done < "$delete_list"
