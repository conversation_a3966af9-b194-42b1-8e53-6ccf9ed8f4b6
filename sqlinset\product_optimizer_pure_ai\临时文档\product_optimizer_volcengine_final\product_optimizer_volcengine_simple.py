#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer_volcengine_simple.py
基于原版product_optimizer_input.py的单文件火山引擎版本
保持原版的简洁性，只需要一个文件即可运行
"""

import requests
import json
import time
import os
import random
import sys
import signal
import threading
import glob
import argparse
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==================== 配置区域 ====================
# 火山引擎API配置 - 请修改为您的配置
VOLC_API_KEY = "3a0ee26f-a392-40fc-9265-335b261942b0"  # 请替换为您的火山引擎API密钥
VOLC_API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
VOLC_MODEL = "deepseek-r1-250528"

# 路径配置 - 自动检测当前目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
INPUT_DIR = os.path.join(CURRENT_DIR, "input")
OUTPUT_DIR = os.path.join(CURRENT_DIR, "output")

# 性能配置
MAX_WORKERS = 6
BATCH_SIZE = 20
REQUEST_DELAY = 0.2
TIMEOUT_SECONDS = 120
MAX_RETRIES = 4

# Auto模式配置 - 修复逻辑：大文件用更多线程
AUTO_MODE_CONFIGS = {
    "small": {"workers": 4, "batch_size": 10, "delay": 0.5},    # < 1000行：保守配置
    "medium": {"workers": 8, "batch_size": 20, "delay": 0.3},   # 1000-10000行：平衡配置
    "large": {"workers": 12, "batch_size": 30, "delay": 0.2}    # > 10000行：高性能配置
}

# 语言配置 - 扩展支持更多欧洲语种
LANGUAGE_CONFIG = {
    "auto": {"name": "English", "code": "en"},
    "en": {"name": "English", "code": "en"},
    "zh": {"name": "Chinese", "code": "zh"},
    "es": {"name": "Spanish", "code": "es"},
    "fr": {"name": "French", "code": "fr"},
    "de": {"name": "German", "code": "de"},
    "it": {"name": "Italian", "code": "it"},
    "pt": {"name": "Portuguese", "code": "pt"},
    "nl": {"name": "Dutch", "code": "nl"},
    "pl": {"name": "Polish", "code": "pl"},
    "ru": {"name": "Russian", "code": "ru"},
    "sv": {"name": "Swedish", "code": "sv"},
    "da": {"name": "Danish", "code": "da"},
    "no": {"name": "Norwegian", "code": "no"},
    "fi": {"name": "Finnish", "code": "fi"},
    "cs": {"name": "Czech", "code": "cs"},
    "hu": {"name": "Hungarian", "code": "hu"},
    "ro": {"name": "Romanian", "code": "ro"},
    "bg": {"name": "Bulgarian", "code": "bg"},
    "hr": {"name": "Croatian", "code": "hr"},
    "sk": {"name": "Slovak", "code": "sk"},
    "sl": {"name": "Slovenian", "code": "sl"},
    "et": {"name": "Estonian", "code": "et"},
    "lv": {"name": "Latvian", "code": "lv"},
    "lt": {"name": "Lithuanian", "code": "lt"},
    "mt": {"name": "Maltese", "code": "mt"},
    "ga": {"name": "Irish", "code": "ga"},
    "cy": {"name": "Welsh", "code": "cy"},
    "ja": {"name": "Japanese", "code": "ja"},
    "ko": {"name": "Korean", "code": "ko"},
    "ar": {"name": "Arabic", "code": "ar"},
    "he": {"name": "Hebrew", "code": "he"},
    "tr": {"name": "Turkish", "code": "tr"},
    "hi": {"name": "Hindi", "code": "hi"},
    "th": {"name": "Thai", "code": "th"},
    "vi": {"name": "Vietnamese", "code": "vi"}
}
# ==================== 配置区域结束 ====================

# 全局变量
graceful_shutdown = False
current_results = []
current_output_file = None
stats_lock = threading.Lock()
processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_products": 0,
    "failed_products": 0
}

def signal_handler(signum, frame):
    """优雅终止处理"""
    global graceful_shutdown, current_results, current_output_file
    print(f"\n🛑 接收到终止信号，正在优雅终止...")
    graceful_shutdown = True
    
    if current_results and current_output_file:
        try:
            save_partial_results(current_results, current_output_file)
            print(f"✅ 部分结果已保存")
        except:
            pass

def save_partial_results(results, output_path):
    """保存部分结果"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        if output_path.endswith('.csv'):
            import csv
            with open(output_path, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow(['序号', 'SEO名称', '分类', '标签'])
                for i, result in enumerate(results, 1):
                    writer.writerow([
                        i,
                        result.get('seo_name', ''),
                        result.get('category', ''),
                        result.get('tags', '')
                    ])
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("序号 | SEO名称 | 分类 | 标签\n")
                f.write("="*100 + "\n")
                for i, result in enumerate(results, 1):
                    seo_name = result.get('seo_name', '').replace('|', '-')
                    category = result.get('category', '')
                    tags = result.get('tags', '')
                    f.write(f"{i:4d} | {seo_name} | {category} | {tags}\n")
    except:
        pass

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def get_auto_config(product_count):
    """根据产品数量自动选择配置"""
    if product_count < 1000:
        return AUTO_MODE_CONFIGS["small"]
    elif product_count < 10000:
        return AUTO_MODE_CONFIGS["medium"]
    else:
        return AUTO_MODE_CONFIGS["large"]

def ai_optimize_products_volcengine(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """火山引擎AI产品优化 - 保持原版提示词"""
    global graceful_shutdown
    batch_num, indexed_products_batch = batch_data
    # 提取产品列表用于AI处理
    products_batch = [product for _, product in indexed_products_batch]
    
    if graceful_shutdown:
        return batch_num, []

    # 语言配置 - 恢复原版逻辑
    lang_config = LANGUAGE_CONFIG.get(target_lang, LANGUAGE_CONFIG["auto"])
    lang_name = lang_config["name"]

    # 保持原版提示词 - 完全一致
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: {lang_name}
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    for attempt in range(max_retries):
        try:
            time.sleep(random.uniform(0, REQUEST_DELAY * 2))
            
            data = {
                "model": VOLC_MODEL,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.8
            }
            
            headers = {
                'Authorization': f'Bearer {VOLC_API_KEY}',
                'Content-Type': 'application/json'
            }
            
            print(f"🔄 批次 {batch_num} 处理中... (尝试 {attempt + 1})")
            
            response = requests.post(VOLC_API_URL, json=data, headers=headers, timeout=TIMEOUT_SECONDS)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                try:
                    json_data = json.loads(content)
                    results = json_data.get('results', [])
                    
                    if len(results) != len(products_batch):
                        if attempt == max_retries - 1:
                            return batch_num, generate_basic_results(products_batch)
                        continue
                    
                    cleaned_results = []
                    for i, result in enumerate(results):
                        if isinstance(result, dict):
                            seo_name = result.get('seo_name', products_batch[i]).replace('|', '-')
                            category = result.get('category', 'General > Products')
                            tags = optimize_tags(result.get('tags', ''))
                            
                            cleaned_results.append({
                                "seo_name": seo_name,
                                "category": category,
                                "tags": tags
                            })
                        else:
                            cleaned_results.append({
                                "seo_name": products_batch[i].replace('|', '-'),
                                "category": "General > Products",
                                "tags": ""
                            })
                    
                    with stats_lock:
                        processing_stats["completed_batches"] += 1
                        processing_stats["successful_products"] += len(cleaned_results)
                    
                    return batch_num, cleaned_results
                    
                except json.JSONDecodeError:
                    if attempt == max_retries - 1:
                        return batch_num, generate_basic_results(products_batch)
            else:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
            
        except Exception as e:
            if attempt < max_retries - 1:
                delay = 3 ** attempt if "timeout" in str(e).lower() else 2 ** attempt
                time.sleep(delay)
    
    # 所有尝试失败，使用备用方案
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_products"] += len(products_batch)
    
    print(f"❌ 批次 {batch_num} 处理失败，使用备用方案")
    return batch_num, generate_basic_results(products_batch)

def optimize_tags(tags):
    """优化标签"""
    if not tags:
        return ""
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    return ", ".join(tag_list[:2])

def generate_basic_results(products_batch):
    """生成基础结果 - 保持原版逻辑"""
    results = []
    for product in products_batch:
        results.append({
            "seo_name": product.replace("|", "-"),  # 保留原始产品名
            "category": "",  # 空分类
            "tags": ""  # 空标签
        })
    return results

def process_single_file(input_path, output_path, output_format="csv", config=None, target_lang="auto"):
    """处理单个文件 - 保持原版逻辑"""
    global current_results, current_output_file, graceful_shutdown

    current_output_file = output_path
    graceful_shutdown = False

    print(f"📖 读取文件: {os.path.basename(input_path)}")

    # 读取产品列表
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")
        return False

    print(f"📊 产品数量: {len(products):,} 个")

    if not products:
        print(f"⚠️ 文件为空")
        return False

    # 选择配置 - Auto模式或自定义配置
    if config is None:
        # Auto模式：根据产品数量自动选择配置
        auto_config = get_auto_config(len(products))
        workers = auto_config["workers"]
        batch_size = auto_config["batch_size"]
        delay = auto_config["delay"]

        if len(products) < 1000:
            config_type = "小文件配置"
        elif len(products) < 10000:
            config_type = "中文件配置"
        else:
            config_type = "大文件配置"

        print(f"🤖 Auto模式: {config_type}")
        print(f"   线程数: {workers}, 批次大小: {batch_size}, 延迟: {delay}秒")
    else:
        # 自定义配置
        workers = config["workers"]
        batch_size = config["batch_size"]
        delay = config["delay"]
        print(f"🔧 自定义配置: {workers}线程, {batch_size}产品/批次")

    # 分批处理 - 包含原始索引信息确保排序正确
    batch_data = []
    for i in range(0, len(products), batch_size):
        batch_num = (i // BATCH_SIZE) + 1
        batch = products[i:i+batch_size]
        # 为每个产品添加原始索引信息
        indexed_batch = []
        for j, product in enumerate(batch):
            original_index = i + j
            indexed_batch.append((original_index, product))
        batch_data.append((batch_num, indexed_batch))

    print(f"🔄 分批处理: {len(batch_data)} 个批次")

    # 重置统计
    processing_stats["completed_batches"] = 0
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0

    print(f"🔄 开始处理 ({len(batch_data)} 个批次，{workers} 个线程)...")

    # 并发处理
    results = {}
    completed_batches = 0

    try:
        with ThreadPoolExecutor(max_workers=workers) as executor:
            # 提交任务
            future_to_batch = {}
            for batch_data_item in batch_data:
                if graceful_shutdown:
                    break
                future = executor.submit(ai_optimize_products_volcengine, batch_data_item, target_lang, MAX_RETRIES)
                future_to_batch[future] = batch_data_item

            # 收集结果
            for future in as_completed(future_to_batch):
                if graceful_shutdown:
                    break

                try:
                    batch_num, batch_results = future.result()

                    # 使用原始索引存储结果 - 确保排序正确
                    _, indexed_products_batch = future_to_batch[future]
                    for i, result in enumerate(batch_results):
                        if i < len(indexed_products_batch):
                            original_index, _ = indexed_products_batch[i]
                            results[original_index] = result

                    completed_batches += 1
                    current_results = list(results.values())

                    print(f"✅ 批次 {batch_num} 完成 ({completed_batches}/{len(batch_data)})")

                except Exception as e:
                    batch_num = future_to_batch[future][0]
                    print(f"❌ 批次 {batch_num} 执行异常: {e}")
                    completed_batches += 1

    except KeyboardInterrupt:
        print(f"🛑 用户中断")
        graceful_shutdown = True

    # 整理最终结果 - 确保顺序正确
    final_results = []
    for i in range(len(products)):
        if i in results:
            final_results.append(results[i])
        else:
            # 失败的产品保留原始名称
            final_results.append({
                "seo_name": products[i].replace('|', '-'),
                "category": "",
                "tags": ""
            })

    # 保存结果
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        current_results = final_results

        if output_format == "csv":
            import csv
            with open(output_path, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow(['序号', 'SEO名称', '分类', '标签'])
                for i, result in enumerate(final_results, 1):
                    writer.writerow([
                        i,
                        result.get('seo_name', ''),
                        result.get('category', ''),
                        result.get('tags', '')
                    ])
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("序号 | SEO名称 | 分类 | 标签\n")
                f.write("="*100 + "\n")
                for i, result in enumerate(final_results, 1):
                    seo_name = result.get('seo_name', '').replace('|', '-')
                    category = result.get('category', '')
                    tags = result.get('tags', '')
                    f.write(f"{i:4d} | {seo_name} | {category} | {tags}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_products"]
        failed = processing_stats["failed_products"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        print(f"✅ 处理完成!")
        print(f"📊 成功: {successful:,} | 失败: {failed:,} | 成功率: {success_rate:.1f}%")
        print(f"⏱️ 耗时: {elapsed_time/60:.1f} 分钟")
        print(f"💾 结果保存到: {output_path}")

        return True

    except Exception as e:
        print(f"❌ 保存结果错误: {e}")
        return False

def setup_logging(log_file=None):
    """设置日志"""
    if log_file:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='火山引擎版产品优化器')
    parser.add_argument('--input', '-i', help='输入文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--format', '-f', choices=['csv', 'txt'], default='csv', help='输出格式')
    parser.add_argument('--lang', '-l', choices=list(LANGUAGE_CONFIG.keys()),
                       default='auto', help='输出语言')
    parser.add_argument('--workers', '-w', type=int, help='并发线程数')
    parser.add_argument('--batch-size', '-b', type=int, help='批次大小')
    parser.add_argument('--delay', '-d', type=float, help='请求间隔(秒)')
    parser.add_argument('--log', help='日志文件路径')
    parser.add_argument('--non-interactive', action='store_true', help='非交互模式')
    return parser.parse_args()

def main():
    """主函数 - 支持交互和非交互模式"""
    args = parse_args()

    # 设置日志
    setup_logging(args.log)

    print("🚀 火山引擎版产品优化器 (单文件版)")
    print("="*50)
    print("🔥 基于原版product_optimizer_input.py")
    print("🎯 保持原版功能，使用火山引擎API")
    print("="*50)

    # 检查API密钥
    if not VOLC_API_KEY or VOLC_API_KEY == "your_volcengine_api_key_here":
        print("❌ 请先设置火山引擎API密钥")
        print("💡 在脚本开头修改 VOLC_API_KEY 变量")
        return False

    # 创建目录
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print(f"📁 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")

    # 文件选择 - 支持命令行参数
    if args.input:
        # 非交互模式：使用命令行指定的文件
        if not os.path.exists(args.input):
            print(f"❌ 输入文件不存在: {args.input}")
            return False
        selected_file = args.input
        print(f"📁 使用指定文件: {os.path.basename(selected_file)}")
    else:
        # 交互模式：从input目录选择
        txt_files = glob.glob(os.path.join(INPUT_DIR, "*.txt"))
        if not txt_files:
            print(f"❌ 在 {INPUT_DIR} 中未找到txt文件")
            print(f"💡 请将产品文件放入input目录")
            return False

        print(f"📁 找到 {len(txt_files)} 个txt文件:")
        for i, file_path in enumerate(txt_files, 1):
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"   {i}. {file_name} ({file_size:,} 字节)")

        # 文件选择
        if len(txt_files) == 1:
            selected_file = txt_files[0]
            print(f"🎯 自动选择: {os.path.basename(selected_file)}")
        else:
            if args.non_interactive:
                print("❌ 非交互模式下需要指定输入文件 (--input)")
                return False

            print(f"\n请选择要处理的文件:")
            for i, file_path in enumerate(txt_files, 1):
                print(f"{i}. {os.path.basename(file_path)}")

            try:
                choice = int(input("请输入文件编号: ").strip())
                if 1 <= choice <= len(txt_files):
                    selected_file = txt_files[choice - 1]
                else:
                    print("❌ 无效选择")
                    return False
            except:
                print("❌ 输入错误")
                return False

    # 参数配置 - 支持命令行参数和交互模式
    output_format = args.format
    target_lang = args.lang

    if not args.non_interactive:
        # 交互模式：允许用户选择
        print(f"\n📄 输出格式:")
        print(f"1. CSV格式 (推荐)")
        print(f"2. TXT格式")

        format_choice = input("请选择格式 (1/2): ").strip()
        output_format = "csv" if format_choice != "2" else "txt"

        # 语言选择 - 支持完整的欧洲语种
        print(f"\n🌍 输出语言 (常用语言):")
        common_langs = [
            ("auto", "Auto (自动检测)"),
            ("en", "English (英语)"),
            ("zh", "Chinese (中文)"),
            ("es", "Spanish (西班牙语)"),
            ("fr", "French (法语)"),
            ("de", "German (德语)"),
            ("it", "Italian (意大利语)"),
            ("pt", "Portuguese (葡萄牙语)"),
            ("nl", "Dutch (荷兰语)"),
            ("pl", "Polish (波兰语)")
        ]

        for i, (code, name) in enumerate(common_langs, 1):
            print(f"{i:2d}. {name}")

        print(f"\n💡 更多语言支持:")
        print(f"   输入语言代码: ru, sv, da, no, fi, cs, hu, ro, bg, hr, sk, sl, et, lv, lt, mt, ga, cy, ja, ko, ar, he, tr, hi, th, vi")

        lang_choice = input(f"请选择语言 (1-{len(common_langs)} 或输入语言代码, 默认Auto): ").strip()

        if lang_choice.isdigit() and 1 <= int(lang_choice) <= len(common_langs):
            target_lang = common_langs[int(lang_choice) - 1][0]
        elif lang_choice in LANGUAGE_CONFIG:
            target_lang = lang_choice
        else:
            target_lang = "auto"

    lang_name = LANGUAGE_CONFIG[target_lang]["name"]
    print(f"🌍 选择语言: {lang_name}")

    # 配置选择 - 支持命令行参数
    config = None
    if args.workers or args.batch_size or args.delay:
        # 使用命令行指定的配置
        workers = args.workers or MAX_WORKERS
        batch_size = args.batch_size or BATCH_SIZE
        delay = args.delay or REQUEST_DELAY
        config = {"workers": workers, "batch_size": batch_size, "delay": delay}
        print(f"🔧 命令行配置: {workers}线程, {batch_size}产品/批次, {delay}秒间隔")
    elif not args.non_interactive:
        # 交互模式：配置选择
        print(f"\n⚙️ 处理配置:")
        print(f"1. Auto模式 (根据文件大小自动选择)")
        print(f"2. 自定义配置")

        config_choice = input("请选择配置模式 (1/2): ").strip()

        if config_choice == "2":
            # 自定义配置
            try:
                workers = int(input(f"并发线程数 (1-12, 默认{MAX_WORKERS}): ") or MAX_WORKERS)
                batch_size = int(input(f"批次大小 (10-50, 默认{BATCH_SIZE}): ") or BATCH_SIZE)
                workers = max(1, min(12, workers))
                batch_size = max(10, min(50, batch_size))
                config = {"workers": workers, "batch_size": batch_size, "delay": REQUEST_DELAY}
                print(f"🔧 自定义配置: {workers}线程, {batch_size}产品/批次")
            except:
                config = None  # 使用Auto模式
                print("⚠️ 输入错误，使用Auto模式")

    if config is None:
        print(f"🤖 使用Auto模式 (根据文件大小自动选择配置)")

    # 生成输出文件路径 - 支持命令行参数
    if args.output:
        output_path = args.output
    else:
        base_name = os.path.splitext(os.path.basename(selected_file))[0]
        output_name = f"{base_name}_optimized.{output_format}"
        output_path = os.path.join(OUTPUT_DIR, output_name)

    print(f"\n🚀 开始处理...")
    print(f"📥 输入: {os.path.basename(selected_file)}")
    print(f"📤 输出: {output_name}")

    # 处理文件
    success = process_single_file(selected_file, output_path, output_format, config, target_lang)

    if success:
        print(f"\n🎉 处理完成!")
        print(f"📁 结果文件: {output_path}")
    else:
        print(f"\n❌ 处理失败")

    return success

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
