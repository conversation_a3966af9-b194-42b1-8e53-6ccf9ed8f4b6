#!/bin/bash

# 创建API端点和管理脚本

echo "🔌 创建API路由文件..."

# API路由文件
cat > public/api/index.php << 'EOF'
<?php
/**
 * API路由入口文件
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 加载配置
require_once dirname(__DIR__, 2) . '/config/config.php';

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$endpoint = str_replace('/api/', '', $path);

// 路由映射
$routes = [
    'deploy.php' => '../../api/deploy.php',
    'templates.php' => '../../api/templates.php',
    'status.php' => '../../api/status.php',
    'logs.php' => '../../api/logs.php',
    'settings.php' => '../../api/settings.php'
];

// 检查路由
if (isset($routes[$endpoint])) {
    $targetFile = __DIR__ . '/' . $routes[$endpoint];
    
    if (file_exists($targetFile)) {
        // 设置环境变量
        $_SERVER['ROUTED_REQUEST'] = true;
        
        // 包含目标文件
        require $targetFile;
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint file not found: ' . $endpoint
        ]);
    }
} else {
    // 默认API信息
    if (empty($endpoint) || $endpoint === 'index.php') {
        echo json_encode([
            'success' => true,
            'message' => 'WordPress Deploy Manager API',
            'version' => '1.0.0',
            'endpoints' => [
                'deploy' => '/api/deploy.php',
                'templates' => '/api/templates.php',
                'status' => '/api/status.php',
                'logs' => '/api/logs.php',
                'settings' => '/api/settings.php'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint not found: ' . $endpoint,
            'available_endpoints' => array_keys($routes)
        ]);
    }
}
?>
EOF

echo "✅ API路由文件创建完成"

echo "🔌 创建模板API..."

# 模板API
cat > api/templates.php << 'EOF'
<?php
/**
 * 模板管理API
 */

require_once dirname(__DIR__) . '/config/config.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list':
            handleGetTemplates();
            break;
            
        case 'upload':
            handleUploadTemplate();
            break;
            
        case 'delete':
            handleDeleteTemplate();
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Templates API error: ' . $e->getMessage());
    errorResponse($e->getMessage());
}

/**
 * 获取模板列表
 */
function handleGetTemplates() {
    try {
        $db = getDatabase();
        
        $stmt = $db->prepare("
            SELECT id, uuid, name, description, filename, file_size, status, created_at 
            FROM templates 
            WHERE status != 'deleted' 
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        
        $templates = $stmt->fetchAll();
        
        successResponse($templates, 'Templates retrieved successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to retrieve templates: ' . $e->getMessage());
    }
}

/**
 * 上传模板
 */
function handleUploadTemplate() {
    try {
        // 检查文件上传
        if (!isset($_FILES['template']) || $_FILES['template']['error'] !== UPLOAD_ERR_OK) {
            errorResponse('No file uploaded or upload error');
        }
        
        $file = $_FILES['template'];
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        
        if (empty($name)) {
            errorResponse('Template name is required');
        }
        
        // 验证文件类型
        $allowedTypes = ['application/gzip', 'application/x-gzip', 'application/zip'];
        if (!in_array($file['type'], $allowedTypes)) {
            errorResponse('Invalid file type. Only .tar.gz and .zip files are allowed');
        }
        
        // 生成文件路径
        $uuid = uniqid('template_');
        $filename = $uuid . '_' . $file['name'];
        $uploadPath = UPLOADS_PATH . '/templates/' . $filename;
        
        // 确保上传目录存在
        if (!is_dir(dirname($uploadPath))) {
            mkdir(dirname($uploadPath), 0777, true);
        }
        
        // 移动文件
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            errorResponse('Failed to save uploaded file');
        }
        
        // 保存到数据库
        $db = getDatabase();
        $stmt = $db->prepare("
            INSERT INTO templates (uuid, name, description, filename, file_path, file_size, file_hash) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $uuid,
            $name,
            $description,
            $filename,
            $uploadPath,
            $file['size'],
            md5_file($uploadPath)
        ]);
        
        successResponse([
            'id' => $db->lastInsertId(),
            'uuid' => $uuid,
            'name' => $name
        ], 'Template uploaded successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to upload template: ' . $e->getMessage());
    }
}

/**
 * 删除模板
 */
function handleDeleteTemplate() {
    try {
        $id = intval($_GET['id'] ?? 0);
        
        if ($id <= 0) {
            errorResponse('Invalid template ID');
        }
        
        $db = getDatabase();
        
        // 获取模板信息
        $stmt = $db->prepare("SELECT file_path FROM templates WHERE id = ?");
        $stmt->execute([$id]);
        $template = $stmt->fetch();
        
        if (!$template) {
            errorResponse('Template not found');
        }
        
        // 标记为删除
        $stmt = $db->prepare("UPDATE templates SET status = 'deleted' WHERE id = ?");
        $stmt->execute([$id]);
        
        // 删除文件
        if (file_exists($template['file_path'])) {
            unlink($template['file_path']);
        }
        
        successResponse(null, 'Template deleted successfully');
        
    } catch (Exception $e) {
        errorResponse('Failed to delete template: ' . $e->getMessage());
    }
}
?>
EOF

echo "✅ 模板API创建完成"

echo "🔌 创建部署API..."
