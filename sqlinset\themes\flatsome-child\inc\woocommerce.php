<?php
/**
 * WooCommerce specific functions and customizations
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; 
}

if (class_exists('WooCommerce')) {

    function flatsome_child_custom_product_layout() {
        if (is_product()) {

        }
    }
    add_action('wp', 'flatsome_child_custom_product_layout');

    function flatsome_child_custom_template_loader($template) {
        if (is_product()) {
            $custom_template = locate_template('woocommerce/single-product-custom.php');
            if (!empty($custom_template)) {
                do_action('flatsome_child_before_load_custom_product_template', $custom_template);
                return $custom_template;
            }
        }
        return $template;
    }
    add_filter('template_include', 'flatsome_child_custom_template_loader', 99); // High priority

    function flatsome_child_custom_features() {
        if (is_product()) {
            add_action('woocommerce_before_single_product_summary', function() {
                global $product;
                if ($product && $product->is_on_sale()) {
                    echo '<span class="custom-badge onsale">' . esc_html__('Sale!', 'flatsome-child') . '</span>';
                }
            }, 10);
        }
    }
    add_action('wp', 'flatsome_child_custom_features');

    function flatsome_child_fix_cart_url_fragments($fragments) {
        add_filter('woocommerce_get_cart_url', 'flatsome_child_force_cart_url', 10);
        add_filter('woocommerce_get_breadcrumb', 'flatsome_child_fix_breadcrumb_cart_url', 10);
        return $fragments;
    }
    add_filter('woocommerce_add_to_cart_fragments', 'flatsome_child_fix_cart_url_fragments');

    function flatsome_child_force_cart_url($url) {
        return wc_get_page_permalink('cart');
    }

    function flatsome_child_fix_breadcrumb_cart_url($crumbs) {
        if (is_checkout()) {
            foreach ($crumbs as $key => &$crumb) {
                if ($crumb[0] === __('Shopping Cart', 'woocommerce')) {
                    $crumbs[$key][1] = wc_get_page_permalink('cart');
                    $crumbs[$key][1] = apply_filters('flatsome_child_breadcrumb_cart_url', $crumbs[$key][1]);
                }
            }
        }
        return $crumbs;
    }

    function flatsome_child_fix_header_cart_url($url) {
        if (is_checkout()) {
            return wc_get_page_permalink('cart');
        }
        return $url;
    }
    add_filter('woocommerce_get_cart_url', 'flatsome_child_fix_header_cart_url', 20);


} 