#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定位失败产品在原始文件中的位置
"""

import os
import re

def find_failed_products_positions(input_file, output_file, failed_report_file):
    """找到失败产品在原始文件中的位置"""
    
    print(f"🔍 分析失败产品位置")
    print("="*60)
    
    # 读取原始输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            input_products = [line.strip() for line in f if line.strip()]
        print(f"📁 输入文件: {len(input_products):,} 个产品")
    except Exception as e:
        print(f"❌ 读取输入文件失败: {e}")
        return
    
    # 读取输出文件
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 跳过标题行，提取产品数据
        output_products = []
        for line in lines[2:]:  # 跳过标题和分隔线
            if line.strip() and not line.startswith('='):
                # 提取SEO名称部分（第一个|之前的内容）
                seo_name = line.split('|')[0].strip()
                if seo_name and not seo_name.startswith('[FAILED]'):
                    output_products.append(seo_name)
        
        print(f"📄 输出文件: {len(output_products):,} 个成功产品")
    except Exception as e:
        print(f"❌ 读取输出文件失败: {e}")
        return
    
    # 读取失败产品报告
    failed_products = []
    if os.path.exists(failed_report_file):
        try:
            with open(failed_report_file, 'r', encoding='utf-8') as f:
                failed_products = [line.strip() for line in f if line.strip()]
            print(f"📋 失败报告: {len(failed_products):,} 个失败产品")
        except Exception as e:
            print(f"⚠️ 读取失败报告失败: {e}")
    
    # 分析失败产品位置
    print(f"\n🔍 定位失败产品位置:")
    print("-" * 60)
    
    failed_positions = []
    found_failed = 0
    
    for i, input_product in enumerate(input_products, 1):
        # 检查这个产品是否在失败列表中
        is_failed = False
        
        # 方法1: 直接匹配
        if input_product in failed_products:
            is_failed = True
        
        # 方法2: 模糊匹配（处理可能的格式差异）
        if not is_failed:
            for failed_product in failed_products:
                if input_product.lower().strip() == failed_product.lower().strip():
                    is_failed = True
                    break
        
        # 方法3: 检查是否在输出中找不到对应的成功产品
        if not is_failed:
            found_in_output = False
            for output_product in output_products:
                # 检查输出产品是否基于这个输入产品
                if (input_product.lower() in output_product.lower() or 
                    output_product.lower() in input_product.lower() or
                    similarity_check(input_product, output_product) > 0.7):
                    found_in_output = True
                    break
            
            if not found_in_output:
                is_failed = True
        
        if is_failed:
            failed_positions.append({
                'line': i,
                'product': input_product
            })
            found_failed += 1
            
            # 显示前20个失败产品的位置
            if found_failed <= 20:
                print(f"❌ 第 {i:,} 行: {input_product}")
    
    print(f"\n📊 定位结果:")
    print(f"   找到失败产品: {found_failed:,} 个")
    print(f"   预期失败数量: {len(failed_products):,} 个")
    
    # 保存详细的失败位置报告
    position_report_file = failed_report_file.replace('.txt', '_positions.txt')
    try:
        with open(position_report_file, 'w', encoding='utf-8') as f:
            f.write("失败产品位置报告\n")
            f.write("="*60 + "\n")
            f.write(f"输入文件: {input_file}\n")
            f.write(f"总产品数: {len(input_products):,}\n")
            f.write(f"失败产品数: {found_failed:,}\n")
            f.write(f"生成时间: {os.popen('date').read().strip()}\n")
            f.write("\n失败产品详细位置:\n")
            f.write("-" * 60 + "\n")
            
            for item in failed_positions:
                f.write(f"第 {item['line']:,} 行: {item['product']}\n")
        
        print(f"💾 详细位置报告已保存: {position_report_file}")
    except Exception as e:
        print(f"❌ 保存位置报告失败: {e}")
    
    # 分析失败产品的特征
    analyze_failure_patterns(failed_positions)

def similarity_check(str1, str2):
    """简单的相似度检查"""
    str1_words = set(str1.lower().split())
    str2_words = set(str2.lower().split())
    
    if not str1_words or not str2_words:
        return 0
    
    intersection = str1_words.intersection(str2_words)
    union = str1_words.union(str2_words)
    
    return len(intersection) / len(union)

def analyze_failure_patterns(failed_positions):
    """分析失败产品的模式"""
    
    print(f"\n🔍 失败产品特征分析:")
    print("-" * 60)
    
    if not failed_positions:
        print("没有找到失败产品")
        return
    
    # 分析产品名称长度
    lengths = [len(item['product']) for item in failed_positions]
    avg_length = sum(lengths) / len(lengths)
    max_length = max(lengths)
    min_length = min(lengths)
    
    print(f"📏 产品名称长度:")
    print(f"   平均长度: {avg_length:.1f} 字符")
    print(f"   最长: {max_length} 字符")
    print(f"   最短: {min_length} 字符")
    
    # 分析常见关键词
    all_words = []
    for item in failed_positions:
        words = item['product'].lower().split()
        all_words.extend(words)
    
    word_count = {}
    for word in all_words:
        word_count[word] = word_count.get(word, 0) + 1
    
    # 找出最常见的词
    common_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:10]
    
    print(f"\n🔤 失败产品中最常见的词:")
    for word, count in common_words:
        if len(word) > 2:  # 忽略太短的词
            print(f"   '{word}': {count} 次")
    
    # 分析行号分布
    line_numbers = [item['line'] for item in failed_positions]
    line_ranges = {
        "前25%": sum(1 for line in line_numbers if line <= len(line_numbers) * 0.25),
        "25%-50%": sum(1 for line in line_numbers if len(line_numbers) * 0.25 < line <= len(line_numbers) * 0.5),
        "50%-75%": sum(1 for line in line_numbers if len(line_numbers) * 0.5 < line <= len(line_numbers) * 0.75),
        "后25%": sum(1 for line in line_numbers if line > len(line_numbers) * 0.75)
    }
    
    print(f"\n📍 失败产品在文件中的分布:")
    for range_name, count in line_ranges.items():
        percentage = (count / len(failed_positions)) * 100 if failed_positions else 0
        print(f"   {range_name}: {count} 个 ({percentage:.1f}%)")

def main():
    """主函数"""
    print("🔍 失败产品定位工具")
    print("="*60)
    
    # 根据您的路径设置
    base_dir = "/www/imagerdown/sqlinset/product_optimizer_pure_ai"
    
    # 查找最新的输入和输出文件
    input_dir = os.path.join(base_dir, "input")
    output_dir = os.path.join(base_dir, "output")
    
    # 列出可用的文件
    input_files = []
    output_files = []
    
    if os.path.exists(input_dir):
        input_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
    
    if os.path.exists(output_dir):
        output_files = [f for f in os.listdir(output_dir) if f.endswith('.txt') and not f.startswith('failed_')]
    
    print(f"📁 找到输入文件: {len(input_files)} 个")
    print(f"📄 找到输出文件: {len(output_files)} 个")
    
    if not input_files or not output_files:
        print("❌ 未找到必要的文件")
        return
    
    # 使用最新的文件（或者您可以手动指定）
    input_file = os.path.join(input_dir, input_files[0])  # 第一个输入文件
    output_file = os.path.join(output_dir, output_files[0])  # 第一个输出文件
    failed_report_file = os.path.join(output_dir, "failed_products_report.txt")
    
    print(f"📁 分析文件:")
    print(f"   输入: {input_file}")
    print(f"   输出: {output_file}")
    print(f"   失败报告: {failed_report_file}")
    
    find_failed_products_positions(input_file, output_file, failed_report_file)

if __name__ == "__main__":
    main()
