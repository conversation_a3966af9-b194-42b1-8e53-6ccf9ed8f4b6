# 🚀 产品优化器 - 多API Key智能版本

基于DeepSeek API的产品SEO优化工具，支持多API Key轮询、智能负载均衡和自动延迟优化。

## 📋 功能特性

- ✅ **多API Key轮询** - 支持多个API Key智能负载均衡
- ✅ **智能延迟优化** - 根据线程数和Key数量自动调整延迟
- ✅ **自动错误恢复** - 问题Key自动冷却和恢复
- ✅ **CSV格式优化** - 更快、更准确的数据处理
- ✅ **优雅终止保护** - Ctrl+C自动保存已处理结果
- ✅ **实时统计监控** - API Key使用情况实时显示

## 🔧 配置说明

### API Key配置

编辑 `product_optimizer_input.py` 第21-38行：

```python
# 多API Key配置 - 用户需要配置自己的Key
API_KEYS = [
    "sk-4691a82b5b5041ecbfa7fdc40fdabc71",  # 主Key
    "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",  # 备用Key 1 (取消注释并填入实际Key)
    "sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy",  # 备用Key 2 (取消注释并填入实际Key)
    "sk-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz",  # 备用Key 3 (取消注释并填入实际Key)
]

# API Key信息配置
API_KEY_INFO = [
    {"account": "main_account", "type": "primary", "limit": "high"},
    {"account": "backup_account_1", "type": "primary", "limit": "high"},
    {"account": "backup_account_2", "type": "primary", "limit": "medium"},
    {"account": "backup_account_3", "type": "primary", "limit": "medium"},
]
```

### 手动调整配置

#### 1. 延迟配置调整 (第224行)
```python
# 基础延迟 - 提高到0.2秒保证稳定性
base_delay = 0.2  # 可调整: 0.1-0.5秒
```

#### 2. 线程系数调整 (第227-235行)
```python
# 根据线程数调整
if thread_count <= 10:
    thread_factor = 1.0    # 可调整: 0.8-1.2
elif thread_count <= 20:
    thread_factor = 1.2    # 可调整: 1.0-1.5
elif thread_count <= 50:
    thread_factor = 1.5    # 可调整: 1.2-2.0
else:
    thread_factor = 2.0    # 可调整: 1.5-3.0
```

#### 3. 批次大小配置 (第45-76行)
```python
# 所有配置统一使用20个产品/批次，避免DeepSeek token限制
STANDARD_BATCH_SIZE = 20      # 可调整: 10-25
OPTIMIZED_BATCH_SIZE = 20     # 可调整: 15-25
PAID_LOW_BATCH_SIZE = 20      # 可调整: 15-25
PAID_MEDIUM_BATCH_SIZE = 20   # 可调整: 15-25
PAID_HIGH_BATCH_SIZE = 20     # 可调整: 15-25
```

#### 4. Auto模式默认配置 (第1141-1167行)
```python
# Auto模式根据Key数量自动选择配置
if len(API_KEYS) >= 5:
    max_workers = PAID_HIGH_WORKERS    # 50线程
elif len(API_KEYS) >= 4:
    max_workers = 35                   # 35线程 (可调整: 25-45)
elif len(API_KEYS) >= 3:
    max_workers = PAID_MEDIUM_WORKERS  # 20线程
elif len(API_KEYS) >= 2:
    max_workers = PAID_LOW_WORKERS     # 10线程
```

## 🚀 执行命令

### 基本使用

#### 交互模式 (推荐新手)
```bash
python product_optimizer_input.py
# 或
python3 product_optimizer_input.py
```

#### Auto模式 (推荐日常使用)
```bash
python product_optimizer_input.py auto
# 或
python3 product_optimizer_input.py auto
```

#### 真正的后台模式 (长时间处理)

**方法1: 使用启动脚本 (推荐)**
```bash
# Linux/Mac
chmod +x start_background.sh
./start_background.sh

# Windows
start_background.bat
```

**方法2: 手动nohup命令** ⭐⭐⭐⭐⭐⭐⭐
```bash 
# Linux/Mac - 使用nohup真正后台运行
nohup python3 product_optimizer_input.py auto > output/processing.log 2>&1 &

# Windows - 使用start命令后台运行
start /B python product_optimizer_input.py auto > output\processing.log 2>&1

# 或者使用内置后台模式 (仅添加日志，不是真后台)
python product_optimizer_input.py background
```

**方法3: 使用进程管理脚本**
```bash
# Linux/Mac
chmod +x manage_process.sh
./manage_process.sh start    # 启动
./manage_process.sh status   # 查看状态
./manage_process.sh log      # 查看日志
./manage_process.sh stop     # 停止
```

### 高级使用

#### 指定模式运行
```bash
# 选择模式3 (自动处理所有文件)
python product_optimizer_input.py
# 然后输入: 3

# 选择配置2 (优化配置)
# 然后输入: 2

# 选择格式1 (CSV格式)
# 然后输入: 1
```

#### 后台运行监控
```bash
# 查看后台进程
ps aux | grep product_optimizer

# 查看实时日志
tail -f processing.log

# 停止后台进程
kill -15 <进程ID>  # 优雅停止
kill -9 <进程ID>   # 强制停止
```

#### 批量处理命令
```bash
# Windows批处理
for %%f in (input\*.txt) do python product_optimizer_input.py auto

# Linux/Mac批处理
for file in input/*.txt; do python product_optimizer_input.py auto; done
```

## 📊 配置对照表

### 线程配置建议

| API Key数量 | Auto模式线程数 | 预期性能 | 适用场景 |
|-------------|----------------|----------|----------|
| **1个** | 2-6 (智能检测) | 基准 | 小规模处理 |
| **2个** | 10 | 2倍 | 中等规模 |
| **3个** | 20 | 4倍 | 大规模处理 |
| **4个** | 35 | 7倍 | 超大规模 |
| **5+个** | 50 | 10倍 | 企业级处理 |

### 交互模式选项

| 选项 | 配置名称 | 线程数 | 批次大小 | 适用场景 |
|------|----------|--------|----------|----------|
| **1** | 智能推荐 | 2-6 | 20 | 自动检测 |
| **2** | 优化配置 | 8 | 20 | 推荐日常使用 |
| **3** | 付费API低配 | 10 | 20 | 付费用户入门 |
| **4** | 付费API中配 | 20 | 20 | 大规模处理 |
| **5** | 付费API高配 | 50 | 20 | 超大规模 |
| **6** | 极限配置 | 100 | 20 | 需要监控 |
| **7** | 自定义配置 | 用户输入 | 用户输入 | 灵活配置 |

## 📁 文件结构

```
project/
├── product_optimizer_input.py    # 主程序
├── README.md                     # 说明文档
├── input/                        # 输入文件目录
│   ├── products.txt             # 产品列表文件
│   ├── products.csv             # CSV格式产品文件
│   └── ...                      # 其他支持格式
└── output/                       # 输出文件目录
    ├── products_optimized.csv   # 优化后的产品文件
    ├── failed_products_report.txt  # 失败产品报告
    ├── failed_products_retry.txt   # 失败产品重试文件
    └── processing.log           # 处理日志(后台模式)
```

## 🔍 监控和调试

### API Key统计查看
程序运行结束后会自动显示API Key使用统计：
```
📊 API Key使用统计:
总Key数: 3, 可用Key数: 3
--------------------------------------------------------------------------------
🟢 Key1 (main_account): 请求45 | 成功42 | 错误3 | 错误率6.7% | 平均响应2.1s
🟢 Key2 (backup_account_1): 请求38 | 成功36 | 错误2 | 错误率5.3% | 平均响应1.9s
🟢 Key3 (backup_account_2): 请求41 | 成功39 | 错误2 | 错误率4.9% | 平均响应2.0s
```

### 性能监控
- **成功率**: 建议保持在95%以上
- **错误率**: 单个Key错误率超过30%会自动冷却
- **响应时间**: 正常范围1-5秒
- **处理速度**: 根据配置和网络状况变化

## ⚠️ 注意事项

### DeepSeek API限制
- **API端点**: 使用官方标准端点 `https://api.deepseek.com/chat/completions`
- **输入Token限制**: 批次大小影响输入token，建议不超过25个产品/批次
- **请求频率**: 智能延迟自动控制，避免触发限流
- **并发限制**: 根据API Key数量合理设置线程数

### 最佳实践
1. **使用不同账户的API Key** - 避免共享限制
2. **监控成功率** - 低于95%时降低并发度
3. **定期检查Key状态** - 及时处理问题Key
4. **备份重要数据** - 处理前备份原始文件

## 🆘 常见问题

### Q: 如何提升处理速度？
A: 配置多个不同账户的API Key，使用Auto模式自动优化配置。

### Q: 出现大量错误怎么办？
A: 检查网络连接，降低线程数，增加延迟时间。

### Q: 如何处理失败的产品？
A: 程序会自动生成失败产品重试文件，将其移动到input目录重新处理。

### Q: CSV和TXT格式有什么区别？
A: CSV格式处理更快、更准确，便于Excel处理；TXT格式人眼友好。

---

**🎉 享受高效的产品优化处理！**
