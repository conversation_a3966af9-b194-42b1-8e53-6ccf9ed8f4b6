from selenium import webdriver
import time
import requests
import os

# 配置 Chrome WebDriver
options = webdriver.ChromeOptions()
options.add_argument("--headless")  # 无头模式
driver = webdriver.Chrome(options=options)

def download_image(url, folder):
    try:
        driver.get(url)
        time.sleep(2)  # 等待页面加载

        # 获取图片内容
        response = requests.get(url)
        filename = os.path.join(folder, url.split("/")[-1])

        if response.status_code == 200:
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"Downloaded {filename}")
        else:
            print(f"Failed to download {url}")
    except Exception as e:
        print(f"Error downloading {url}: {e}")

# 从文件读取链接并下载
def main():
    image_links_file = "image_links.txt"
    target_folder = "./imagedownload"
    
    os.makedirs(target_folder, exist_ok=True)
    
    with open(image_links_file, "r") as f:
        urls = f.readlines()
    
    for url in urls:
        url = url.strip()
        if url:
            download_image(url, target_folder)

if __name__ == "__main__":
    main()
    driver.quit()
