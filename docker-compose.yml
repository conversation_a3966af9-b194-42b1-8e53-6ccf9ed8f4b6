version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: wp-deploy-web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/apache/vhost.conf:/etc/apache2/sites-available/000-default.conf
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - ./logs:/var/www/html/logs
      - ./uploads:/var/www/html/uploads
    depends_on:
      - mysql
      - redis
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
    networks:
      - wp-deploy-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: wp-deploy-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: wp_deploy_2024
      MYSQL_DATABASE: wp_deploy_manager
      MYSQL_USER: wp_deploy
      MYSQL_PASSWORD: wp_deploy_pass_2024
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_mysql.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wp-deploy-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: wp-deploy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wp-deploy-network
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-deploy-phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: wp_deploy_2024
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 500M
    depends_on:
      - mysql
    networks:
      - wp-deploy-network
    restart: unless-stopped

  mailhog:
    image: mailhog/mailhog:latest
    container_name: wp-deploy-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - wp-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wp-deploy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
