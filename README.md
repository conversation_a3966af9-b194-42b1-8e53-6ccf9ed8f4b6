# 🚀 WordPress Deploy Manager - 本地开发环境

## 📋 项目概述

WordPress Deploy Manager 是一个强大的WordPress站点批量部署和管理系统。这个项目提供了完整的本地开发环境，包含前端界面、API接口和数据库管理。

## 🎯 主要功能

- **批量部署**: 支持单域名和批量域名WordPress站点部署
- **模板管理**: 上传、管理和使用WordPress模板文件
- **实时监控**: 系统资源监控和健康状态检查
- **日志管理**: 完整的操作日志和错误追踪
- **设置管理**: 灵活的系统配置和参数调整

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）
```bash
# 运行快速安装脚本
quick_setup.bat
```

### 方法二：使用PowerShell
```powershell
# 运行PowerShell安装脚本
.\install.ps1
```

### 方法三：手动安装
```bash
# 1. 创建目录结构
mkdir logs uploads public

# 2. 启动Docker服务
docker-compose up -d

# 3. 访问应用
# 打开浏览器访问 http://localhost
```

## 🌐 访问地址

安装完成后，您可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost | WordPress Deploy Manager 主界面 |
| API接口 | http://localhost/api/ | RESTful API 接口 |
| phpMyAdmin | http://localhost:8080 | 数据库管理界面 |

## 🔑 默认配置

### 数据库配置
```
主机: localhost:3306
数据库: wp_deploy_manager
用户: wp_deploy
密码: wp_deploy_pass_2024
Root密码: wp_deploy_2024
```

### 系统配置
```
最大并发任务: 3
默认超时时间: 1800秒
调试模式: 开启
日志级别: DEBUG
```

## 📁 项目结构

```
wp-deploy-manager/
├── api/                    # API端点文件
├── config/                 # 配置文件
├── database/               # 数据库相关
├── docker/                 # Docker配置
├── logs/                   # 日志文件目录
├── public/                 # Web根目录
│   ├── api/               # API路由
│   ├── assets/            # 静态资源
│   └── index.html         # 主页面
├── uploads/                # 上传文件目录
├── docker-compose.yml      # Docker编排文件
├── install.ps1            # PowerShell安装脚本
├── quick_setup.bat        # 批处理安装脚本
└── README.md              # 项目说明
```

## 🛠️ 开发指南

### 环境要求
- Docker Desktop for Windows
- 4GB+ 内存
- 10GB+ 磁盘空间

### 常用命令
```bash
# 启动服务
docker-compose start

# 停止服务
docker-compose stop

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 进入容器
docker-compose exec web bash
```

### 开发流程
1. **环境搭建**: 运行安装脚本创建开发环境
2. **代码修改**: 修改相应的PHP、HTML、CSS、JS文件
3. **服务重启**: 重启Docker服务使修改生效
4. **功能测试**: 访问相应页面测试功能
5. **日志查看**: 查看日志文件排查问题

## 🧪 功能测试

### 基础测试
- [ ] 访问主页面 http://localhost
- [ ] 检查API接口 http://localhost/api/
- [ ] 登录phpMyAdmin http://localhost:8080
- [ ] 查看Docker容器状态

### 功能测试
- [ ] 仪表板数据显示
- [ ] 模板管理功能
- [ ] 部署管理界面
- [ ] 系统监控功能
- [ ] 日志查看功能

## 🚨 常见问题

### 端口冲突
如果80端口被占用，可以修改docker-compose.yml中的端口映射：
```yaml
ports:
  - "8080:80"  # 改为8080端口
```

### Docker服务异常
```bash
# 重启Docker Desktop
# 或者重新构建容器
docker-compose down
docker-compose up -d --build
```

### 权限问题
确保Docker Desktop有足够的权限访问项目目录。

## 📊 性能优化

### 开发环境优化
- 启用Docker的文件共享优化
- 增加Docker分配的内存和CPU
- 使用SSD存储提高I/O性能

### 数据库优化
- 定期清理日志表
- 优化数据库索引
- 配置合适的缓存策略

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

## 📞 获取帮助

### 问题排查
1. 检查Docker服务状态
2. 查看容器日志
3. 验证端口占用情况
4. 检查文件权限设置

### 日志位置
- **应用日志**: logs/目录
- **Docker日志**: `docker-compose logs`
- **数据库日志**: 容器内部日志

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

---

🎉 **感谢使用WordPress Deploy Manager！**

如果您觉得这个项目有用，请给我们一个⭐️星标支持！

## 🔗 相关链接

- [Docker官方文档](https://docs.docker.com/)
- [PHP官方文档](https://www.php.net/docs.php)
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [WordPress官方文档](https://wordpress.org/support/)

---

**项目状态**: ✅ 可用于本地开发和测试  
**最后更新**: 2024年7月26日  
**版本**: v1.0.0
