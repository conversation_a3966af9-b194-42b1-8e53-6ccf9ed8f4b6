<?php
/**
 * Theme Customizer Functions
 * 
 * This file contains functions for theme customization options
 * including layout options, and other theme settings.
 */

if (!defined('ABSPATH')) {
    exit; 
}


function flatsome_child_customize_register($wp_customize) {
    $wp_customize->add_section('flatsome_child_options', array(
        'title'    => __('Child Theme Options', 'flatsome-child'),
        'priority' => 30,
        'description' => __('Configure layout, styles, spacing, and border radius specific to the child theme. Colors are managed under Appearance > Customize > Style > Colors.', 'flatsome-child'),
    ));

    $preset_styles = function_exists('flatsome_child_get_preset_styles') ? flatsome_child_get_preset_styles() : array();
    $preset_choices = array('' => __('— Select a Preset —', 'flatsome-child'));
    foreach ($preset_styles as $key => $values) {
        $preset_choices[$key] = __(ucfirst($key), 'flatsome-child');
    }
    $wp_customize->add_setting('apply_preset_style_selector', array(
        'default'           => '',
        'transport'         => 'postMessage',
        'sanitize_callback' => 'sanitize_key',
    ));
    $wp_customize->add_control('apply_preset_style_selector', array(
        'label'    => __('Apply Preset Style', 'flatsome-child'),
        'section'  => 'flatsome_child_options',
        'type'     => 'select',
        'choices'  => $preset_choices,
        'priority' => 5,
    ));
    $wp_customize->add_setting('apply_preset_style_button_placeholder', array(
        'default' => '',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('apply_preset_style_button_placeholder', array(
        'section' => 'flatsome_child_options',
        'type' => 'hidden',
        'priority' => 6,
    ));

    $controls = array(
        'layout_style' => array(
            'default' => 'standard',
            'choices' => array(
            'standard' => __('Standard', 'flatsome-child'),
            'boxed'    => __('Boxed', 'flatsome-child'),
            'wide'     => __('Wide', 'flatsome-child'),
                'framed'   => __('Framed', 'flatsome-child'),
                'split'    => __('Split', 'flatsome-child'),
                'glass'    => __('Glass', 'flatsome-child'),
                'masonry'  => __('Masonry', 'flatsome-child'),
                'sidebar'  => __('Sidebar', 'flatsome-child'),
                'fullscreen' => __('FullScreen', 'flatsome-child'),
            ),
            'priority' => 10,
        ),
        'header_style' => array(
            'default' => 'default',
            'choices' => array(
            'default' => __('Default', 'flatsome-child'),
            'minimal' => __('Minimal', 'flatsome-child'),
            'modern'  => __('Modern', 'flatsome-child'),
                'center'  => __('Center', 'flatsome-child'),
                'stacked' => __('Stacked', 'flatsome-child'),
                'glass'   => __('Glass', 'flatsome-child'),
                'underline' => __('Underline', 'flatsome-child'),
                'shadow'  => __('Shadow', 'flatsome-child'),
                'biglogo' => __('Big Logo', 'flatsome-child'),
                'sticky'  => __('Sticky', 'flatsome-child'),
            ),
            'priority' => 20,
        ),
        'footer_style' => array(
            'default' => 'default',
            'choices' => array(
            'default' => __('Default', 'flatsome-child'),
            'minimal' => __('Minimal', 'flatsome-child'),
            'modern'  => __('Modern', 'flatsome-child'),
                'dark'    => __('Dark', 'flatsome-child'),
                'center'  => __('Center', 'flatsome-child'),
                'glass'   => __('Glass', 'flatsome-child'),
                'widgetized' => __('Widgetized', 'flatsome-child'),
                'minimaldark' => __('Minimal Dark', 'flatsome-child'),
                'bordered' => __('Bordered', 'flatsome-child'),
                'sticky'   => __('Sticky', 'flatsome-child'),
        ),
        'priority' => 30,
        ),
        'product_style' => array(
            'default' => 'default',
            'choices' => array(
                'default'   => __('Default', 'flatsome-child'),
                'minimal'   => __('Minimal', 'flatsome-child'),
                'modern'    => __('Modern', 'flatsome-child'),
                'creative'  => __('Creative', 'flatsome-child'),
                'list'      => __('List', 'flatsome-child'),
                'grid'      => __('Grid', 'flatsome-child'),
                'masonry'   => __('Masonry', 'flatsome-child'),
                'overlaytext' => __('Overlay Text', 'flatsome-child'),
                'shadowcard'  => __('Shadow Card', 'flatsome-child'),
                'circleimage' => __('Circle Image', 'flatsome-child'),
                'badgecorner' => __('Badge Corner', 'flatsome-child'),
        ),
        'priority' => 40,
        ),
        'spacing' => array(
            'default' => 20,
            'type' => 'number',
        'input_attrs' => array(
            'min'  => 10,
            'max'  => 50,
            'step' => 5,
        ),
        'priority' => 50,
        ),
        'border_radius' => array(
            'default' => 3,
            'type' => 'number',
        'input_attrs' => array(
            'min'  => 0,
            'max'  => 20,
            'step' => 1,
        ),
        'priority' => 60,
        ),
    );
    foreach ($controls as $id => $control) {
        $wp_customize->add_setting($id, array(
            'default'           => $control['default'],
            'sanitize_callback' => isset($control['type']) && $control['type'] === 'number' ? 'absint' : 'sanitize_text_field',
            'transport'         => isset($control['type']) && $control['type'] === 'number' ? 'postMessage' : 'refresh',
        ));
        $wp_customize->add_control($id, array(
            'label'    => __(ucfirst(str_replace('_', ' ', $id)), 'flatsome-child'),
            'section'  => 'flatsome_child_options',
            'type'     => isset($control['type']) ? $control['type'] : 'select',
            'choices'  => isset($control['choices']) ? $control['choices'] : null,
            'input_attrs' => isset($control['input_attrs']) ? $control['input_attrs'] : null,
            'priority' => $control['priority'],
        ));
    }

    $wp_customize->add_section('flatsome_child_banner_section', array(
        'title'    => __('Banner Style', 'flatsome-child'),
        'priority' => 40,
    ));
    $wp_customize->add_setting('flatsome_child_banner_bg_type', array(
        'default'   => 'solid',
        'transport' => 'refresh',
    ));
    $wp_customize->add_control('flatsome_child_banner_bg_type', array(
        'label'   => __('Banner Background Type', 'flatsome-child'),
        'section' => 'flatsome_child_banner_section',
        'type'    => 'select',
        'choices' => array(
            'solid'    => __('Solid Color', 'flatsome-child'),
            'gradient' => __('Gradient', 'flatsome-child'),
            'image'    => __('Image', 'flatsome-child'),
            'video'    => __('Video', 'flatsome-child'),
            'svgwave'  => __('SVG Wave', 'flatsome-child'),
            'pattern'  => __('Pattern', 'flatsome-child'),
        ),
    ));
    $wp_customize->add_setting('flatsome_child_banner_layout', array(
        'default'   => 'centered',
        'transport' => 'refresh',
    ));
    $wp_customize->add_control('flatsome_child_banner_layout', array(
        'label'   => __('Banner Layout', 'flatsome-child'),
        'section' => 'flatsome_child_banner_section',
        'type'    => 'select',
        'choices' => array(
            'centered'      => __('Centered Text', 'flatsome-child'),
            'left_image'    => __('Left Image, Right Text', 'flatsome-child'),
            'right_image'   => __('Right Image, Left Text', 'flatsome-child'),
            'split'         => __('Split Columns', 'flatsome-child'),
            'full_overlay'  => __('Full Overlay', 'flatsome-child'),
            'minimal'       => __('Minimal', 'flatsome-child'),
        ),
    ));
    $wp_customize->add_setting('flatsome_child_banner_shape', array(
        'default'   => 'rectangle',
        'transport' => 'refresh',
    ));
    $wp_customize->add_control('flatsome_child_banner_shape', array(
        'label'   => __('Banner Shape', 'flatsome-child'),
        'section' => 'flatsome_child_banner_section',
        'type'    => 'select',
        'choices' => array(
            'rectangle'   => __('Rectangle', 'flatsome-child'),
            'rounded'     => __('Rounded Corners', 'flatsome-child'),
            'wave'        => __('Wave Bottom', 'flatsome-child'),
            'diagonal'    => __('Diagonal Cut', 'flatsome-child'),
            'circle'      => __('Circle', 'flatsome-child'),
            'asymmetry'   => __('Asymmetrical', 'flatsome-child'),
        ),
    ));
    $wp_customize->add_setting('flatsome_child_banner_animation', array(
        'default'   => 'none',
        'transport' => 'refresh',
    ));
    $wp_customize->add_control('flatsome_child_banner_animation', array(
        'label'   => __('Banner Animation', 'flatsome-child'),
        'section' => 'flatsome_child_banner_section',
        'type'    => 'select',
        'choices' => array(
            'none'      => __('None', 'flatsome-child'),
            'fadein'    => __('Fade In', 'flatsome-child'),
            'slideup'   => __('Slide Up', 'flatsome-child'),
            'zoom'      => __('Zoom In', 'flatsome-child'),
            'parallax'  => __('Parallax', 'flatsome-child'),
            'kenburns'  => __('Ken Burns', 'flatsome-child'),
            'bounce'    => __('Bounce', 'flatsome-child'),
        ),
    ));
}
add_action('customize_register', 'flatsome_child_customize_register');


function flatsome_child_customizer_css() {
    $spacing       = get_theme_mod('spacing', 20);
    $border_radius = get_theme_mod('border_radius', 3);
    
    $css_variables = array();

    if (!empty($spacing)) {
        $css_variables[] = '--spacing: ' . absint($spacing) . 'px';
    }
    
    if (isset($border_radius) && $border_radius !== '') { // Check if set, allow 0
        $css_variables[] = '--border-radius: ' . absint($border_radius) . 'px';
    }
    
    if (!empty($css_variables)) {
        echo '<style type="text/css" id="flatsome-child-customizer-vars">:root {' . esc_html(implode(';', $css_variables)) . '}</style>';
    }
}
add_action('wp_head', 'flatsome_child_customizer_css', 999); 
add_action('wp_head', function() {
    $banner_bg_type   = get_theme_mod('flatsome_child_banner_bg_type', 'solid');
    $banner_layout    = get_theme_mod('flatsome_child_banner_layout', 'centered');
    $banner_shape     = get_theme_mod('flatsome_child_banner_shape', 'rectangle');
    $banner_anim      = get_theme_mod('flatsome_child_banner_animation', 'none');

    ?>
    <style>
    .auto-category-banner {
        <?php if($banner_bg_type === 'solid'): ?>
            background: #bbd2d9;
        <?php elseif($banner_bg_type === 'gradient'): ?>
            background: linear-gradient(135deg, #bbd2d9 0%, #cac6e6 100%);
        <?php elseif($banner_bg_type === 'image'): ?>
            background: url('/path/to/your/banner.jpg') center/cover no-repeat;
        <?php elseif($banner_bg_type === 'video'): ?>
            background: #000; /* fallback */
        <?php elseif($banner_bg_type === 'svgwave'): ?>
            background: linear-gradient(180deg, #bbd2d9 80%, #fff 100%);
            position: relative;
        <?php elseif($banner_bg_type === 'pattern'): ?>
            background: repeating-linear-gradient(45deg, #bbd2d9, #bbd2d9 10px, #cac6e6 10px, #cac6e6 20px);
        <?php endif; ?>

        <?php if($banner_shape === 'rounded'): ?>
            border-radius: 48px;
        <?php elseif($banner_shape === 'wave'): ?>
            border-radius: 0 0 80px 80px/0 0 40px 40px;
        <?php elseif($banner_shape === 'diagonal'): ?>
            clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
        <?php elseif($banner_shape === 'circle'): ?>
            border-radius: 50%;
            overflow: hidden;
        <?php elseif($banner_shape === 'asymmetry'): ?>
            border-radius: 80px 16px 48px 16px/40px 80px 16px 48px;
        <?php endif; ?>
        <?php if($banner_anim !== 'none'): ?>
            animation: banner-<?php echo esc_attr($banner_anim); ?> 1s;
        <?php endif; ?>
    }
    <?php if($banner_anim === 'fadein'): ?>
    @keyframes banner-fadein { from { opacity:0; } to { opacity:1; } }
    <?php elseif($banner_anim === 'slideup'): ?>
    @keyframes banner-slideup { from { transform:translateY(60px); opacity:0; } to { transform:none; opacity:1; } }
    <?php elseif($banner_anim === 'zoom'): ?>
    @keyframes banner-zoom { from { transform:scale(0.8); opacity:0; } to { transform:scale(1); opacity:1; } }
    <?php elseif($banner_anim === 'parallax'): ?>
    <?php elseif($banner_anim === 'kenburns'): ?>
    @keyframes banner-kenburns { from { transform: scale(1.1); } to { transform: scale(1); } }
    <?php elseif($banner_anim === 'bounce'): ?>
    @keyframes banner-bounce { 0%,100%{transform:translateY(0);} 50%{transform:translateY(-20px);} }
    <?php endif; ?>

    <?php if($banner_layout === 'centered'): ?>
    .auto-category-banner-content { text-align: center; justify-content: center; align-items: center; }
    <?php elseif($banner_layout === 'left_image'): ?>
    .auto-category-banner { flex-direction: row; }
    .auto-category-banner-content { text-align: right; margin-left: auto; }
    <?php elseif($banner_layout === 'right_image'): ?>
    .auto-category-banner { flex-direction: row-reverse; }
    .auto-category-banner-content { text-align: left; margin-right: auto; }
    <?php elseif($banner_layout === 'split'): ?>
    .auto-category-banner { display: flex; flex-direction: row; }
    .auto-category-banner-content { flex: 1; }
    <?php elseif($banner_layout === 'full_overlay'): ?>
    .auto-category-banner-content { position: absolute; top:0;left:0;right:0;bottom:0; display:flex; align-items:center; justify-content:center; }
    <?php elseif($banner_layout === 'minimal'): ?>
    .auto-category-banner-content { background: none; box-shadow: none; }
    <?php endif; ?>
    </style>
    <?php
});