import os
import shutil

# 指定原始文件夹路径
source_folder = '/www/imagerdown/imagewebp'

# 指定新的文件夹路径
target_folder = '/www/imagerdown/imagewebp_renamed'

# 如果目标文件夹不存在，则创建它
if not os.path.exists(target_folder):
    os.makedirs(target_folder)

# 遍历原始文件夹中的所有文件
for filename in os.listdir(source_folder):
    # 检查文件名是否以Castorama-开头
    if filename.startswith('Castorama-'):
        # 去掉开头的Castorama-
        new_filename = filename[len('Castorama-'):]
        
        # 去掉问号后面的内容，但保留.webp扩展名
        if '?' in new_filename:
            # 找到.webp的位置
            webp_index = new_filename.find('.webp')
            if webp_index != -1:
                # 保留.webp及之前的内容
                new_filename = new_filename[:webp_index + 5]  # +5 是为了包含.webp
            else:
                # 如果没有.webp，直接去掉问号后面的内容
                new_filename = new_filename.split('?')[0]
        else:
            # 如果没有问号，直接保留原文件名
            pass
        
        # 构建完整的原始文件路径和目标文件路径
        old_file = os.path.join(source_folder, filename)
        new_file = os.path.join(target_folder, new_filename)
        
        # 将文件复制到新文件夹并重命名
        shutil.copy2(old_file, new_file)
        print(f'Copied and renamed: {filename} -> {new_filename}')