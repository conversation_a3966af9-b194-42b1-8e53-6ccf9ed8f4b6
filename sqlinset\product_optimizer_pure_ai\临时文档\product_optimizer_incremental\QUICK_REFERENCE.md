# 产品优化器 - 快速参考

## 🚀 常用命令

### 启动程序
```bash
# 交互模式（推荐新手）
python3 product_optimizer_incremental.py

# 后台模式（自动平衡模式）
nohup python3 product_optimizer_incremental.py > output.log 2>&1 &

# Screen模式（推荐生产环境）
screen -S optimizer
python3 product_optimizer_incremental.py
# Ctrl+A+D 分离，screen -r optimizer 重连
```

### 监控程序
```bash
# 查看进程
ps aux | grep product_optimizer

# 查看日志
tail -f logs/product_optimizer_*.log

# 查看输出
ls -lt output/
```

### 终止程序
```bash
# 优雅终止（推荐）
kill -TERM <进程ID>

# 强制终止
kill -9 <进程ID>

# 交互模式
Ctrl+C
```

## ⚡ 性能模式

| 模式 | 线程 | 批次 | 速度 | 场景 |
|------|------|------|------|------|
| 保守 | 6 | 20 | 1.0x | 稳定优先 |
| **平衡** | **8** | **25** | **1.5x** | **推荐/Auto** |
| 激进 | 10 | 30 | 2.0x | 最大性能 |

## 📁 重要路径

```bash
# 输入目录
/www/imagerdown/sqlinset/product_optimizer_incremental/input/

# 输出目录
/www/imagerdown/sqlinset/product_optimizer_incremental/output/

# 日志目录
logs/
```

## 🔧 故障排除

### 常见错误
```bash
# API错误
grep "网络错误\|API" logs/*.log

# 权限错误
ls -la /www/imagerdown/sqlinset/product_optimizer_incremental/

# 磁盘空间
df -h
```

### 恢复处理
```bash
# 从部分结果恢复
cp output/*_partial.txt input/resume.txt

# 从备份恢复
cp output/*_backup_*.txt input/resume.txt
```

## 📊 监控指标

### 正常运行
- 处理速度: 30-150 产品/分钟
- 内存使用: < 2GB
- CPU使用: 50-80%

### 性能警告
- 速度 < 30 产品/分钟 → 可能API限制
- 速度 > 150 产品/分钟 → 性能优秀
- 内存 > 4GB → 考虑重启

## 🛠️ 维护命令

```bash
# 清理备份（7天前）
find output/ -name "*_backup_*" -mtime +7 -delete

# 清理日志（30天前）
find logs/ -name "*.log" -mtime +30 -delete

# 检查文件数量
ls output/ | wc -l
```

## 📞 紧急联系

遇到问题时，请收集：
1. 错误日志: `tail -50 logs/*.log`
2. 系统状态: `free -h && df -h`
3. 进程状态: `ps aux | grep python`

---
**快速参考** | **版本**: v2.0 | **更新**: 2025-07-02
