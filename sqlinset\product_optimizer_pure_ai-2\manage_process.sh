#!/bin/bash
# 进程管理脚本
# 使用方法: ./manage_process.sh [start|stop|status|log]

PID_FILE="output/process.pid"
LOG_FILE="output/processing.log"

case "$1" in
    start)
        echo "🚀 启动产品优化器..."
        if [ -f "$PID_FILE" ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null 2>&1; then
                echo "⚠️ 进程已在运行 (PID: $PID)"
                exit 1
            fi
        fi
        
        mkdir -p output
        nohup python3 product_optimizer_input.py auto > $LOG_FILE 2>&1 &
        PID=$!
        echo $PID > $PID_FILE
        echo "✅ 进程已启动 (PID: $PID)"
        ;;
        
    stop)
        echo "🛑 停止产品优化器..."
        if [ -f "$PID_FILE" ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null 2>&1; then
                kill -15 $PID
                echo "✅ 已发送停止信号 (PID: $PID)"
                echo "⏳ 等待进程优雅退出..."
                sleep 5
                if ps -p $PID > /dev/null 2>&1; then
                    kill -9 $PID
                    echo "🔨 强制终止进程"
                fi
                rm -f $PID_FILE
            else
                echo "⚠️ 进程未运行"
                rm -f $PID_FILE
            fi
        else
            echo "⚠️ 未找到进程ID文件"
        fi
        ;;
        
    status)
        echo "🔍 检查进程状态..."
        if [ -f "$PID_FILE" ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null 2>&1; then
                echo "✅ 进程正在运行 (PID: $PID)"
                echo "📊 进程信息:"
                ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
            else
                echo "❌ 进程未运行"
                rm -f $PID_FILE
            fi
        else
            echo "❌ 未找到进程ID文件"
        fi
        ;;
        
    log)
        echo "📝 查看实时日志 (Ctrl+C退出):"
        if [ -f "$LOG_FILE" ]; then
            tail -f $LOG_FILE
        else
            echo "❌ 未找到日志文件: $LOG_FILE"
        fi
        ;;
        
    *)
        echo "📋 进程管理脚本使用方法:"
        echo "  ./manage_process.sh start   - 启动后台进程"
        echo "  ./manage_process.sh stop    - 停止后台进程"
        echo "  ./manage_process.sh status  - 查看进程状态"
        echo "  ./manage_process.sh log     - 查看实时日志"
        echo ""
        echo "📁 相关文件:"
        echo "  进程ID: $PID_FILE"
        echo "  日志文件: $LOG_FILE"
        ;;
esac
