#!/bin/bash

# WordPress Deploy Manager 一键安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示欢迎信息
clear
echo -e "${PURPLE}"
echo "=================================================================="
echo "    🚀 WordPress Deploy Manager 本地环境一键安装"
echo "=================================================================="
echo -e "${NC}"
echo -e "${CYAN}这个脚本将为您创建完整的WordPress Deploy Manager本地开发环境${NC}"
echo ""
echo -e "${YELLOW}包含以下功能:${NC}"
echo "  ✅ Docker容器化环境"
echo "  ✅ MySQL数据库"
echo "  ✅ phpMyAdmin管理界面"
echo "  ✅ 完整的前端界面"
echo "  ✅ RESTful API接口"
echo "  ✅ 自动化测试脚本"
echo "  ✅ 管理和维护工具"
echo ""
echo -e "${YELLOW}系统要求:${NC}"
echo "  • Docker 20.10+"
echo "  • Docker Compose 1.29+"
echo "  • 4GB+ 内存"
echo "  • 10GB+ 磁盘空间"
echo ""

# 确认安装
read -p "是否继续安装？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "安装已取消"
    exit 1
fi

echo ""
echo -e "${BLUE}开始安装...${NC}"
echo ""

# 检查系统要求
echo -e "${BLUE}[1/3]${NC} 检查系统要求..."

# 检查Docker
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    echo -e "   ✅ Docker 已安装: $DOCKER_VERSION"
else
    echo -e "   ❌ Docker 未安装"
    echo -e "   请访问 https://docs.docker.com/get-docker/ 安装Docker"
    exit 1
fi

# 检查Docker Compose
if command -v docker-compose &> /dev/null; then
    COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
    echo -e "   ✅ Docker Compose 已安装: $COMPOSE_VERSION"
elif docker compose version &> /dev/null; then
    COMPOSE_VERSION=$(docker compose version --short)
    echo -e "   ✅ Docker Compose (Plugin) 已安装: $COMPOSE_VERSION"
else
    echo -e "   ❌ Docker Compose 未安装"
    echo -e "   请安装Docker Compose"
    exit 1
fi

# 检查Docker服务
if ! docker info >/dev/null 2>&1; then
    echo -e "   ❌ Docker服务未运行"
    echo -e "   请启动Docker服务"
    exit 1
else
    echo -e "   ✅ Docker服务正在运行"
fi

# 检查可用空间
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
if [ $AVAILABLE_SPACE -lt 10485760 ]; then # 10GB in KB
    echo -e "   ⚠️  磁盘空间不足，建议至少10GB可用空间"
    read -p "   是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo -e "   ✅ 磁盘空间充足"
fi

# 创建项目目录
echo ""
echo -e "${BLUE}[2/3]${NC} 创建项目环境..."

# 设置脚本权限并运行
chmod +x setup_complete_environment.sh
./setup_complete_environment.sh

# 验证安装
echo ""
echo -e "${BLUE}[3/3]${NC} 验证安装结果..."

# 等待服务完全启动
echo -e "   等待服务完全启动..."
sleep 10

# 运行测试
if [ -f "test_installation.sh" ]; then
    chmod +x test_installation.sh
    ./test_installation.sh
else
    echo -e "   ⚠️  测试脚本未找到，手动验证..."
    
    # 手动检查服务
    if curl -s http://localhost >/dev/null; then
        echo -e "   ✅ Web服务正常"
    else
        echo -e "   ❌ Web服务异常"
    fi
    
    if curl -s http://localhost/api/ >/dev/null; then
        echo -e "   ✅ API服务正常"
    else
        echo -e "   ❌ API服务异常"
    fi
fi

# 显示完成信息
echo ""
echo -e "${GREEN}=================================================================="
echo -e "  🎉 安装完成！WordPress Deploy Manager 已准备就绪"
echo -e "=================================================================="
echo -e "${NC}"

echo -e "${YELLOW}🌐 访问地址:${NC}"
echo -e "   主应用:      ${BLUE}http://localhost${NC}"
echo -e "   phpMyAdmin:  ${BLUE}http://localhost:8080${NC}"
echo -e "   API接口:     ${BLUE}http://localhost/api/${NC}"
echo ""

echo -e "${YELLOW}🔑 登录信息:${NC}"
echo -e "   数据库主机: localhost:3306"
echo -e "   数据库名称: wp_deploy_manager"
echo -e "   数据库用户: wp_deploy"
echo -e "   数据库密码: wp_deploy_pass_2024"
echo -e "   Root密码:   wp_deploy_2024"
echo ""

echo -e "${YELLOW}🛠️  常用命令:${NC}"
echo -e "   启动服务:   ${BLUE}./manage.sh start${NC}"
echo -e "   停止服务:   ${BLUE}./manage.sh stop${NC}"
echo -e "   查看状态:   ${BLUE}./manage.sh status${NC}"
echo -e "   查看日志:   ${BLUE}./manage.sh logs${NC}"
echo -e "   运行测试:   ${BLUE}./test_installation.sh${NC}"
echo -e "   备份数据:   ${BLUE}./manage.sh backup${NC}"
echo ""

echo -e "${YELLOW}📚 文档和帮助:${NC}"
echo -e "   查看README: ${BLUE}cat README.md${NC}"
echo -e "   项目结构:   ${BLUE}tree -L 2${NC}"
echo ""

echo -e "${GREEN}🚀 现在您可以开始使用WordPress Deploy Manager了！${NC}"
echo -e "${GREEN}   在浏览器中访问 http://localhost 开始体验${NC}"
echo ""

# 询问是否立即打开浏览器
if command -v xdg-open &> /dev/null; then
    read -p "是否立即在浏览器中打开应用？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open http://localhost
    fi
elif command -v open &> /dev/null; then
    read -p "是否立即在浏览器中打开应用？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open http://localhost
    fi
fi

echo -e "${CYAN}感谢使用WordPress Deploy Manager！${NC}"
echo -e "${CYAN}如有问题，请查看日志文件或运行测试脚本进行诊断。${NC}"
echo ""
