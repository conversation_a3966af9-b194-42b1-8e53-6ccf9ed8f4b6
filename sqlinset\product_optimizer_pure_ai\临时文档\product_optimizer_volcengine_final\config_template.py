#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件模板
复制此文件为 config.py 并修改相应配置
"""

# 火山引擎API配置
VOLC_API_KEY = "3a0ee26f-a392-40fc-9265-335b261942b0"  # 请替换为您的API密钥
VOLC_API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
VOLC_MODEL = "deepseek-r1-250528"

# 服务器路径配置 (请根据实际环境修改)
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/output"

# 性能配置
MAX_WORKERS = 8           # 并发线程数
BATCH_SIZE = 20           # 批次大小
REQUEST_DELAY = 0.2       # 请求间隔(秒)
TIMEOUT_SECONDS = 120     # 超时时间(秒)
MAX_RETRIES = 4           # 重试次数

# 高级配置
AUTO_DETECT_WORKERS = True    # 是否自动检测最优线程数
DEFAULT_OUTPUT_FORMAT = "csv" # 默认输出格式 (csv/txt)
DEFAULT_FAIL_MODE = "original" # 默认失败处理模式 (original/mark/basic)

# 日志配置
ENABLE_DETAILED_LOGS = True   # 是否启用详细日志
LOG_FILE_PATH = "/var/log/product_optimizer.log"  # 日志文件路径
