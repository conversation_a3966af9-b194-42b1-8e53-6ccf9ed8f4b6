<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress 部署管理系统 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status-ok {
            border-left-color: #28a745;
        }
        .status-error {
            border-left-color: #dc3545;
        }
        .status-warning {
            border-left-color: #ffc107;
        }
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-error {
            background: #f8d7da;
            color: #721c24;
        }
        .badge-loading {
            background: #fff3cd;
            color: #856404;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 0 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WordPress 部署管理系统</h1>
            <p>系统状态检测页面</p>
        </div>

        <div class="status-grid">
            <div class="status-card status-ok">
                <h3>✅ 系统状态</h3>
                <p>基础环境正常</p>
                <small>PHP 8.1 + MySQL 8.0</small>
            </div>
            
            <div class="status-card" id="file-status">
                <h3>📁 文件状态</h3>
                <p id="file-status-text">检测中...</p>
                <small>静态资源文件</small>
            </div>
            
            <div class="status-card" id="api-status">
                <h3>🔌 API状态</h3>
                <p id="api-status-text">检测中...</p>
                <small>后端接口服务</small>
            </div>
        </div>

        <div class="test-results">
            <h3>🔍 详细检测结果</h3>
            <div id="test-list">
                <div class="test-item">
                    <span>CSS文件加载</span>
                    <span class="status-badge badge-loading" id="css-status">检测中</span>
                </div>
                <div class="test-item">
                    <span>JavaScript文件加载</span>
                    <span class="status-badge badge-loading" id="js-status">检测中</span>
                </div>
                <div class="test-item">
                    <span>API接口连通性</span>
                    <span class="status-badge badge-loading" id="api-test-status">检测中</span>
                </div>
                <div class="test-item">
                    <span>数据库连接</span>
                    <span class="status-badge badge-loading" id="db-status">检测中</span>
                </div>
            </div>
        </div>

        <div class="actions">
            <a href="/" class="btn">进入系统</a>
            <a href="/api/" class="btn btn-secondary">API文档</a>
            <button onclick="runTests()" class="btn btn-secondary">重新检测</button>
        </div>
    </div>

    <script>
        // 测试函数
        async function testResource(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        async function testAPI() {
            try {
                const response = await fetch('/api/', { 
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        function updateStatus(elementId, success, successText = '正常', errorText = '异常') {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status-badge badge-success';
                element.textContent = successText;
            } else {
                element.className = 'status-badge badge-error';
                element.textContent = errorText;
            }
        }

        function updateCard(cardId, statusClass, text) {
            const card = document.getElementById(cardId);
            card.className = `status-card ${statusClass}`;
            document.getElementById(cardId + '-text').textContent = text;
        }

        async function runTests() {
            console.log('开始系统检测...');

            // 重置状态
            document.querySelectorAll('.status-badge').forEach(badge => {
                badge.className = 'status-badge badge-loading';
                badge.textContent = '检测中';
            });

            // 测试CSS文件
            const cssOk = await testResource('/assets/css/main.css');
            updateStatus('css-status', cssOk);

            // 测试JavaScript文件
            const jsOk = await testResource('/assets/js/main.js');
            updateStatus('js-status', jsOk);

            // 测试API
            const apiOk = await testAPI();
            updateStatus('api-test-status', apiOk);

            // 模拟数据库测试（实际应该通过API）
            setTimeout(() => {
                updateStatus('db-status', true, '已连接');
            }, 1000);

            // 更新状态卡片
            const fileOk = cssOk && jsOk;
            updateCard('file-status', fileOk ? 'status-ok' : 'status-error', 
                      fileOk ? '文件正常' : '文件缺失');

            updateCard('api-status', apiOk ? 'status-ok' : 'status-error',
                      apiOk ? 'API正常' : 'API异常');

            console.log('检测完成', { cssOk, jsOk, apiOk });
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
