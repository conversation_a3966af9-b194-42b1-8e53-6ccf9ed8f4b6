#!/bin/bash

# WordPress 部署管理系统 - 清理安装文件脚本
# 用于删除安装文件和脚本，提高系统安全性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=================================================="
echo "  WordPress 部署管理系统 - 清理安装文件"
echo "==================================================${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用root用户运行此脚本${NC}"
    exit 1
fi

# 获取当前目录
CURRENT_DIR="$(pwd)"
echo "当前目录: $CURRENT_DIR"

# 确认操作
echo -e "${YELLOW}此操作将删除以下文件和目录:${NC}"
echo "- install/ 目录及其所有内容"
echo "- install_fixed.sh 安装脚本"
echo "- quick_install.sh 安装脚本"
echo "- simple_install.sh 安装脚本"
echo "- cleanup_install.sh 本脚本"
echo ""

read -p "确定要继续吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 删除安装目录
if [ -d "install" ]; then
    echo -e "${BLUE}删除 install/ 目录...${NC}"
    rm -rf install/
    echo -e "${GREEN}✓ install/ 目录已删除${NC}"
else
    echo -e "${YELLOW}install/ 目录不存在${NC}"
fi

# 删除安装脚本
INSTALL_SCRIPTS=(
    "install_fixed.sh"
    "quick_install.sh" 
    "simple_install.sh"
)

for script in "${INSTALL_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo -e "${BLUE}删除 $script...${NC}"
        rm -f "$script"
        echo -e "${GREEN}✓ $script 已删除${NC}"
    else
        echo -e "${YELLOW}$script 不存在${NC}"
    fi
done

# 删除其他安装相关文件
OTHER_FILES=(
    "DEPLOYMENT_GUIDE.md"
    "README.md"
)

for file in "${OTHER_FILES[@]}"; do
    if [ -f "$file" ]; then
        read -p "是否删除 $file? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -f "$file"
            echo -e "${GREEN}✓ $file 已删除${NC}"
        else
            echo -e "${YELLOW}保留 $file${NC}"
        fi
    fi
done

# 创建安全提醒文件
cat > SECURITY_NOTICE.txt << 'EOF'
WordPress 部署管理系统 - 安全提醒

安装文件已清理完成！

为了系统安全，建议您：

1. 定期备份数据库和配置文件
   - 数据库: wp_deploy_manager
   - 配置目录: config/
   - 模板目录: templates/
   - 日志目录: logs/

2. 监控系统日志
   - 系统日志: logs/system_*.log
   - 部署日志: logs/job_*.log
   - Nginx日志: logs/nginx_*.log

3. 定期更新系统
   - 保持PHP和MySQL版本更新
   - 监控安全漏洞

4. 访问控制
   - 限制管理界面访问IP
   - 使用强密码
   - 定期更换数据库密码

5. 备份策略
   - 每日自动备份数据库
   - 每周备份整个系统
   - 测试备份恢复流程

重要文件位置：
- 项目目录: $(pwd)
- 配置文件: $(pwd)/config/
- 数据库配置: $(pwd)/config/database.php
- 应用配置: $(pwd)/config/app.json

如有问题，请查看系统日志或联系技术支持。

清理时间: $(date)
EOF

echo -e "${GREEN}✓ 已创建安全提醒文件: SECURITY_NOTICE.txt${NC}"

# 设置正确的文件权限
echo -e "${BLUE}设置文件权限...${NC}"
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod +x scripts/*.sh 2>/dev/null || true

echo -e "${GREEN}✓ 文件权限设置完成${NC}"

# 最后删除本脚本
echo -e "${BLUE}删除清理脚本...${NC}"
SCRIPT_NAME="$(basename "$0")"

echo -e "${GREEN}=================================================="
echo "  🎉 清理完成！"
echo "=================================================="
echo "已删除的文件:"
echo "- install/ 目录"
echo "- 所有安装脚本"
echo "- $SCRIPT_NAME (本脚本)"
echo ""
echo "系统现在更加安全了！"
echo "请查看 SECURITY_NOTICE.txt 了解安全建议。"
echo "==================================================${NC}"

# 删除本脚本（延迟执行）
(sleep 2; rm -f "$SCRIPT_NAME") &

exit 0
