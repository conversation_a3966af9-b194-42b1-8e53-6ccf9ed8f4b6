/*
Theme Name: Flatsome Child
Description: This is a child theme for Flatsome Theme
Author: UX Themes
Template: flatsome
Version: 3.0
*/

/* Root Variables */
:root {
    --primary-color: #446084;
    --secondary-color: #d26e4b;
    --alert-color: #b20000;
    --success-color: #7a9c59;
    --bg-color: #ffffff;
    --bg-color-alt: #f8f9fa;
    --border-radius: 3px;
    --box-shadow: 0 1px 3px -2px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --spacing: 20px;
    --transition: all 0.3s ease;
    --text-color: #222;
}

/* Base Styles */
body {
    color: var(--text-color);
}

body, html {
    overflow-x: hidden;
    background: #fff !important;
}

#wrapper {
    min-height: 100vh;
    background: #fff !important;
}

/* Header Styles */
.header-wrapper {
    z-index: 30;
}

.header-main {
    z-index: 10;
}

/* Product Styles */
.product-small .box {
    background-color: var(--bg-color);
    box-shadow: var(--box-shadow);
    transition: box-shadow .3s;
    border-radius: var(--border-radius);
}

.product-small .box:hover {
    box-shadow: 0 3px 6px -4px rgba(0,0,0,.16), 0 3px 6px rgba(0,0,0,.23);
}

.product-small .box-image {
    position: relative;
    height: auto;
    margin-bottom: .6em;
    max-width: 100%;
    width: 100%;
}

.product-small .box-text {
    padding: 1em;
    position: relative;
    width: 100%;
}

/* Button Styles */
body .button {
    text-transform: uppercase;
    border-radius: var(--border-radius) !important;
    font-weight: 600;
    letter-spacing: .03em;
    transition: var(--transition);
}

/* Navigation Styles */
.nav > li > a {
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: .02em;
}

/* Footer Styles */
.footer-wrapper {
    padding: 30px 0;
}

.absolute-footer {
    padding: 15px 0;
    font-size: .9em;
    border-top: 1px solid rgba(0,0,0,.05);
}

/* Form Styles */
.form-flat input:not([type=submit]),
.form-flat select,
.form-flat textarea {
    border-radius: var(--border-radius) !important;
    padding: .5em 1em;
    transition: var(--transition);
}

.form-flat input:not([type=submit]):focus,
.form-flat select:focus,
.form-flat textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color),.1);
}

/* Utility Classes */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }

/* Responsive Styles */
@media screen and (max-width: 849px) {
    .col {
        padding-bottom: 30px;
    }
    
    .product-small .box-text {
        padding: .7em;
    }
    
    .layout-creative,
    .layout-modern {
        padding: 0 15px;
    }
}

/* Layout Styles */
.layout-standard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing);
}

.layout-modern {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
}

.layout-minimal,
.layout-minimalist {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Theme Styles */
.theme-style-1 {
    --theme-spacing: 20px;
    --theme-radius: 5px;
}

.theme-style-2 {
    --theme-spacing: 30px;
    --theme-radius: 10px;
}

.theme-style-3 {
    --theme-spacing: 15px;
    --theme-radius: 3px;
}

.theme-style-4 {
    --theme-spacing: 25px;
    --theme-radius: 8px;
}

/* Image Styles */
.auto-category-banner img,
.responsive-image-container img {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
}

/* Breadcrumbs */
.breadcrumbs {
    font-weight: 400;
    letter-spacing: 0;
    padding: 0;
    font-size: .85em;
}

/* Product Labels */
.product-label {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: var(--border-radius) !important;
    font-size: 12px;
    font-weight: 600;
    z-index: 9;
}

.product-label.hot-sale {
    background-color: #ff4757;
    color: #fff;
}

.product-label.new-arrival {
    background-color: #2ed573;
    color: #fff;
}

.product-label.limited {
    background-color: #ffa502;
    color: #fff;
}

.product-label.special {
    background-color: #1e90ff;
    color: #fff;
}

/* Price Styles */
.price-label {
    display: inline-block;
    margin-right: 5px;
    color: #ff4757;
    font-weight: 600;
}

.special-price {
    display: inline-flex;
    align-items: center;
    background: #fff8e1;
    padding: 5px 10px;
    border-radius: 3px;
}

/* Product Features */
.custom-product-features {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.feature-item {
    display: inline-flex;
    align-items: center;
    background: #f8f9fa;
    font-size: 13px;
    color: #666;
    padding: 5px 10px;
    border-radius: 3px;
}

.feature-item:before {
    content: "✓";
    margin-right: 5px;
    color: var(--success-color);
}

/* Category Items */
.custom-category-item {
    position: relative;
    margin-bottom: 5px;
}

.custom-category-item a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 3px;
    transition: .3s;
}

.custom-category-item a:hover {
    background: var(--primary-color);
    color: #fff;
}

.custom-category-item .count {
    font-size: 12px;
    opacity: .8;
}

/* Breadcrumb */
.custom-breadcrumb {
    padding: 10px 0;
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
}

.custom-breadcrumb .divider {
    margin: 0 5px;
    color: #999;
}

/* Layout Styles */
.layout-creative {
    max-width: 100%;
    padding: 0 50px;
}

.layout-boxed {
    max-width: 1200px;
    margin: 2em auto;
    padding: 0 var(--spacing);
    background: var(--bg-color-alt);
    box-shadow: 0 0 10px rgba(0,0,0,.1);
}

.layout-wide {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Media Elements */
iframe, img, video {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.custom-animation {
    animation: .5s ease-out fadeInUp;
}

/* Responsive Image Container */
.responsive-image-container {
    position: relative;
    width: 100%;
    padding-top: 100%;
    overflow: hidden;
}

.responsive-image-container img {
    width: 100%;
    transition: transform .3s;
}

/* Hover Effects */
.auto-category-banner:hover img,
.product-style-modern .box:hover .box-image img,
.responsive-image-container:hover img {
    transform: scale(1.05);
}

/* Product Style Variations */
.product-style-1 .box-image {
    border-radius: var(--theme-radius) var(--theme-radius) 0 0;
}

.product-style-2 .box-image {
    border-radius: 50%;
    overflow: hidden;
    margin: var(--theme-spacing) auto;
}

.product-style-3 .box-image {
    clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

.product-style-4 .box-image {
    border-radius: var(--theme-radius);
    box-shadow: 0 5px 15px rgba(0,0,0,.1);
}

/* Product Summary */
.product-summary .quantity {
    margin-bottom: 1em;
    margin-top: 1em;
}

.product-summary .price {
    margin: 1em 0;
}

/* Product Style Default */
.product-style-default .box {
    background: var(--bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Product Style Minimal */
.product-style-minimal .box {
    background: 0 0;
    box-shadow: none;
    border: 1px solid rgba(0,0,0,.1);
}

.product-style-minimal .box:hover,
body.layout-minimal .product-small .box:hover,
body.layout-minimalist .product-small .box:hover {
    border-color: var(--primary-color);
}

.product-style-modern .box {
    background: var(--bg-color);
    border-radius: calc(var(--border-radius) * 2);
    box-shadow: 0 5px 15px rgba(0,0,0,.1);
    overflow: hidden;
}

.product-style-modern .box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,.15);
}

.product-style-modern .box-image {
    position: relative;
    overflow: hidden;
}

.product-style-modern .box-image img {
    transition: transform .3s;
}

.product-style-default .box-text {
    padding: 1em;
}

.product-style-minimal .box-text {
    padding: .8em 0;
}

.product-style-modern .box-text {
    padding: 1.2em;
}

.header-style-default {
    background: var(--bg-color);
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.header-style-minimal {
    background: 0 0;
    border-bottom: 1px solid rgba(0,0,0,.1);
}

.header-style-modern {
    background: var(--primary-color);
    color: var(--text-color);
}

.footer-style-default {
    background: var(--bg-color-alt);
    padding: 2em 0;
}

.footer-style-minimal {
    background: 0 0;
    border-top: 1px solid rgba(0,0,0,.1);
    padding: 1.5em 0;
}

.footer-style-modern {
    background: var(--primary-color);
    color: var(--text-color);
    padding: 3em 0;
}

body.layout-standard #content > .container,
body.layout-standard #content > .full-width > .container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--spacing);
    padding-right: var(--spacing);
}

body.layout-boxed #wrapper {
    max-width: 1250px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--spacing);
    padding-right: var(--spacing);
}

body.layout-boxed #content > .container,
body.layout-boxed #content > .full-width > .container {
    max-width: 1200px;
    padding-left: 0;
    padding-right: 0;
}

body.layout-wide #content > .container,
body.layout-wide #content > .full-width > .container,
body.layout-wide #wrapper {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}

body.footer-style-default #footer .footer-wrapper {
    padding: 2em 0;
}

body.footer-style-minimal #footer .footer-wrapper {
    padding: 1.5em 0;
}

body.footer-style-modern #footer .footer-wrapper {
    padding: 3em 0;
}

body.product-style-modern .products .product-small .box {
    border-radius: calc(var(--border-radius) * 2) !important;
}

@media screen and (max-width: 849px) {
    .col {
        padding-bottom: 30px;
    }
    
    .product-small .box-text {
        padding: .7em;
    }
    
    .layout-creative,
    .layout-modern {
        padding: 0 15px;
    }
    
    .custom-product-features {
        flex-wrap: wrap;
    }
    
    .feature-item {
        width: calc(50% - 5px);
    }
    
    body.layout-boxed #wrapper,
    body.layout-standard #content > .container,
    body.layout-standard #content > .full-width > .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

.layout-framed #wrapper {
    border: 2px solid #eee;
    box-shadow: 0 0 20px #ccc;
    background: #fafbfc;
}

.layout-split #content {
    display: flex;
    gap: 40px;
}

.header-style-modern #header,
.header-style-modern .header-wrapper {
    background: #222 !important;
    color: var(--text-color) !important;
}

body.layout-minimal #wrapper,
body.layout-minimalist #wrapper {
    max-width: 1000px !important;
    margin: 0 auto !important;
    background: #fff !important;
    box-shadow: 0 2px 16px rgba(0,0,0,.04) !important;
    min-height: 100vh !important;
    width: 100% !important;
}

body.layout-minimal,
body.layout-minimalist {
    background: #fafbfc !important;
}

body.layout-minimal .container,
body.layout-minimal .row,
body.layout-minimalist .container,
body.layout-minimalist .row {
    max-width: 100% !important;
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

body.layout-minimal .product-small .box,
body.layout-minimalist .product-small .box {
    background: #fff;
    border: 1px solid #eee;
    box-shadow: none;
    border-radius: 0;
    transition: border-color .2s;
}

body.layout-minimal .product-small .box-text,
body.layout-minimalist .product-small .box-text {
    padding: .8em 0;
    background: 0 0;
    font-size: 1em;
    color: #222;
}

body.layout-minimal .product-title,
body.layout-minimalist .product-title {
    font-weight: 500;
    font-size: 1.05em;
    color: #222;
}

body.layout-minimal .price,
body.layout-minimalist .price {
    color: #222;
    font-weight: 600;
}

body.layout-minimal .footer-wrapper,
body.layout-minimal .header-wrapper,
body.layout-minimalist .footer-wrapper,
body.layout-minimalist .header-wrapper {
    background: #fff !important;
    box-shadow: none !important;
    border: none !important;
    color: #222 !important;
}

body.layout-minimal .footer-wrapper,
body.layout-minimalist .footer-wrapper {
    border-top: 1px solid #eee !important;
}

body.layout-minimal .nav > li > a,
body.layout-minimalist .nav > li > a {
    font-weight: 400;
    letter-spacing: 0;
    text-transform: none;
}

body.layout-minimal .container,
body.layout-minimal .content-area,
body.layout-minimal .main,
body.layout-minimal .site-main,
body.layout-minimalist .container,
body.layout-minimalist .content-area,
body.layout-minimalist .main,
body.layout-minimalist .site-main {
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
    width: 100%;
    box-sizing: border-box;
}

.auto-category-grid .swiper-wrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: stretch;
    gap: 32px;
}

.auto-category-item.swiper-slide {
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
    margin: 0 !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: 260px;
    box-sizing: border-box;
}

.auto-category-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    width: 100%;
    text-decoration: none;
}

.auto-category-item img {
    width: 100%;
    height: 180px;
    object-fit: contain;
    border-radius: 12px;
    background: #f8f9fa;
    display: block;
}

.auto-category-item span {
    display: block;
    text-align: center;
    margin-top: 12px;
    font-weight: bold;
    font-size: 1.1rem;
    color: #222;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.auto-category-banner-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

@media (max-width: 900px) {
    .auto-category-banners-swiper {
        flex-direction: column;
        gap: 16px;
    }
    
    .auto-category-banner-slide {
        max-width: 100%;
    }
    
    .auto-category-banner {
        height: 220px;
    }
    
    .auto-category-banner-content {
        padding: 16px;
    }
    
    .auto-category-banner-title {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }
    
    .auto-category-banner-description {
        font-size: .9rem;
        margin-bottom: 8px;
    }
    
    .auto-category-banner-btn {
        padding: 8px 20px;
        font-size: .9rem;
    }
    
    .auto-category-grid .swiper-slide,
    .auto-category-item {
        max-width: 48vw;
        min-width: 40vw;
    }
    
    .auto-category-banner-content {
        padding-top: 8px;
        padding-bottom: 8px;
    }
}

@media (min-width: 900px) {
  .auto-category-banners-swiper {
    display: flex;
    gap: 32px;
    justify-content: center;
    align-items: stretch;
  }
  .auto-category-banner-slide {
    flex: 1 1 0;
    max-width: 48%;
    min-width: 350px;
    border-radius: 18px;
    overflow: hidden;
    margin: 0;
  }
}

@media (max-width: 899px) {
  .auto-category-banners-swiper {
    flex-direction: column;
    gap: 16px;
  }
  .auto-category-banner-slide {
    max-width: 100%;
    min-width: 0;
    border-radius: 18px;
    margin: 0 auto;
  }
}

.auto-category-banner {
  position: relative;
  height: 400px;
  border-radius: 18px;
  overflow: hidden;
  width: 100%;
  background: #eee;
}

.auto-category-banner img {
  position: absolute;
  left: 0; top: 0; width: 100%; height: 100%;
  object-fit: cover;
  border-radius: 18px;
  z-index: 1;
}

.auto-category-banners-swiper {
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

.auto-category-banner-title,
.auto-category-banner-content h2,
.auto-category-banner-content h3 {
  color: #fff !important;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}

.auto-category-banner-btn,
.auto-category-banner-content .button,
.auto-category-banner-content .btn {
  color: #fff !important;
  background: var(--primary-color, #222) !important;
  border: none;
  border-radius: 24px;
  padding: 10px 28px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  transition: background 0.2s;
}

.auto-category-banner-btn.secondary,
.auto-category-banner-content .button.secondary,
.auto-category-banner-content .btn.secondary {
  background: var(--secondary-color, #444) !important;
}

.auto-category-banner-btn:hover,
.auto-category-banner-content .button:hover,
.auto-category-banner-content .btn:hover {
  background: #000 !important;
  color: #fff !important;
}

.auto-category-banners-swiper,
.swiper-wrapper {
  width: 100% !important;
  margin: 0 auto;
  box-sizing: border-box;
}

@media (min-width: 900px) {
  .auto-category-banners-swiper {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 32px;
  }
  .swiper-wrapper {
    display: flex;
    width: 100% !important;
    gap: 32px;
  }
  .auto-category-banner-slide {
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
    margin: 0 !important;
    height: 400px;
    display: flex;
    align-items: stretch;
  }
  .auto-category-banner {
    width: 100%;
    height: 100%;
  }
}

.product-small,
.product-style-default .box,
.product-style-modern .box,
.product-style-minimal .box,
.product-style-creative .box,
.product-style-list .box,
.product-style-grid .box,
.product-style-masonry .box,
.product-style-overlay .box,
.product-style-shadow .box,
.product-style-circle .box,
.product-style-badge .box {
  height: 100%;
  min-height: 450px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.product-small .box-image,
.product-style-default .box-image,
.product-style-modern .box-image,
.product-style-minimal .box-image,
.product-style-creative .box-image,
.product-style-list .box-image,
.product-style-grid .box-image,
.product-style-masonry .box-image,
.product-style-overlay .box-image,
.product-style-shadow .box-image,
.product-style-circle .box-image,
.product-style-badge .box-image {
  width: 100%;
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-small .box-image img,
.product-style-default .box-image img,
.product-style-modern .box-image img,
.product-style-minimal .box-image img,
.product-style-creative .box-image img,
.product-style-list .box-image img,
.product-style-grid .box-image img,
.product-style-masonry .box-image img,
.product-style-overlay .box-image img,
.product-style-shadow .box-image img,
.product-style-circle .box-image img,
.product-style-badge .box-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

