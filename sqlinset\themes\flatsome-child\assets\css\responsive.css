@media screen and (min-width: 850px) {
    .nav-main {
        padding: 0 30px;
    }
    
    .nav-main .menu-item {
        margin: 0 15px;
    }
    
    .content-area {
        padding: 40px 0;
    }
    
    .sidebar {
        padding-left: 30px;
    }
    
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
    }
    
    .footer-widgets {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
    }
}

@media (prefers-reduced-motion: no-preference) {
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
}

@media print {
    .nav-main,
    .sidebar,
    .footer-widgets {
        display: none;
    }
    
    .content-area {
        width: 100%;
        padding: 0;
    }
} 