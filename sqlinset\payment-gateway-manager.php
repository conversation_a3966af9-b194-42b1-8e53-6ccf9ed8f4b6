<?php
/*
Plugin Name: Payment Gateway Manager
Description: Manages payment gateways, custom fields, dynamic discounts, and shipping options.
Version: 1.1
Author: hanp
*/

// ======================
// 支付网关管理功能
// ======================
add_filter('woocommerce_available_payment_gateways', 'remove_gateways_for_large_orders_and_sort', 10, 1);

function remove_gateways_for_large_orders_and_sort($available_gateways) {
    if (is_admin() || !is_checkout()) return $available_gateways;

    $order_total = WC()->cart->total;

    // 订单金额超过100时移除指定网关
    if ($order_total > 100) {
        unset($available_gateways['panbaopay']);
    }
    // 订单金额超过 10 时移除 richness_credit
    if ($order_total > 10) {
        unset($available_gateways['richness_credit']);
    }

    // 网关排序逻辑
    $order = [
        'panbaopay',
        'richness_credit',
        'richness_pay',
        'tpay_card_emb',// 其他支付网关...
    ];

    $available_gateways = array_filter($available_gateways, function($key) use ($order) {
        return in_array($key, $order);
    }, ARRAY_FILTER_USE_KEY);

    uksort($available_gateways, function($a, $b) use ($order) {
        return array_search($a, $order) - array_search($b, $order);
    });

    return $available_gateways;
}

// ======================
// 自定义字段管理
// ======================
add_action('admin_init', 'pgm_register_custom_fields');

function pgm_register_custom_fields() {
    // 注册设置字段
    $fields = [
        'bzg_description' => '地址',
        'bzg_code'        => '邮箱',
        'bzg_phone'       => '电话',
        'bzg_domain'      => '域名'
    ];

    foreach ($fields as $key => $label) {
        register_setting('general', $key);
        add_settings_field(
            $key,
            "<label for='$key'>$label</label>",
            function() use ($key, $label) {
                $value = get_option($key, '');
                if ($key === 'bzg_description' || $key === 'bzg_code') {
                    echo "<textarea name='$key' class='large-text code' rows='3'>$value</textarea>";
                } else {
                    echo "<input type='text' name='$key' value='" . esc_attr($value) . "' class='regular-text'>";
                }
                echo "<p class='description'>$label</p>";
            },
            'general'
        );
    }
}

// ======================
// 短码功能
// ======================
add_shortcode('woocommerce_admin_adresstr', function() {
    return get_option('bzg_description', '');
});

add_shortcode('woocommerce_admin_email', function() {
    return get_option('bzg_code', '');
});

add_shortcode('woocommerce_admin_phones', function() {
    return get_option('bzg_phone', '');
});

add_shortcode('woocommerce_admin_domains', function() {
    return get_option('bzg_domain', '');
});

// ======================
// 动态折扣系统
// ======================
add_action('woocommerce_cart_calculate_fees', 'pgm_apply_dynamic_discount');

function pgm_apply_dynamic_discount() {
    if (is_admin() && !defined('DOING_AJAX')) return;

    $cart = WC()->cart;
    $cart_total = $cart->get_cart_contents_total();
    $cart_hash = md5(json_encode($cart->get_cart()));

    // 获取或生成随机最大金额
    $session = WC()->session;
    if ($session->get('pgm_cart_hash') !== $cart_hash) {
        $session->set('pgm_max_total', mt_rand(18801, 18999) / 100);
        $session->set('pgm_cart_hash', $cart_hash);
    }
    
    $max_total = $session->get('pgm_max_total');
    $current_total = $cart_total + $cart->get_fee_total();

    // 应用动态折扣
    if ($current_total > $max_total) {
        $discount = $max_total - $current_total;
        $percentage = round(($discount / $current_total) * 100, 2);
        $cart->add_fee(
            sprintf(__('Zeitlich begrenztes Angebot! (%s%%)', 'pgm'), abs($percentage)),
            $discount
        );
    }
}

// ======================
// 配送方式控制
// ======================
add_filter('woocommerce_package_rates', 'pgm_custom_shipping', 100, 2);

function pgm_custom_shipping($rates, $package) {
    $threshold = 59;
    $cart_total = WC()->cart->get_displayed_subtotal();

    if ($cart_total >= $threshold) {
        foreach ($rates as $key => $rate) {
            if ('free_shipping' !== $rate->method_id) {
                unset($rates[$key]);
            }
        }
    }
    
    return $rates;
}