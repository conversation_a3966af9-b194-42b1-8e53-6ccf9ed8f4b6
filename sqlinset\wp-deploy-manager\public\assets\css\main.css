/* WordPress 部署管理系统 - 主样式文件 */

/* CSS变量定义 */
:root {
    /* 颜色系统 */
    --primary-color: #2271b1;
    --primary-dark: #135e96;
    --primary-light: #72aee6;
    
    --secondary-color: #6c757d;
    --secondary-dark: #5a6268;
    --secondary-light: #adb5bd;
    
    --success-color: #28a745;
    --success-dark: #1e7e34;
    --success-light: #d4edda;
    
    --warning-color: #ffc107;
    --warning-dark: #e0a800;
    --warning-light: #fff3cd;
    
    --danger-color: #dc3545;
    --danger-dark: #c82333;
    --danger-light: #f8d7da;
    
    --info-color: #17a2b8;
    --info-dark: #138496;
    --info-light: #d1ecf1;
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #e9ecef;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
    --gray-400: #adb5bd;
    --gray-500: #6c757d;
    --gray-600: #495057;
    --gray-700: #343a40;
    --gray-800: #212529;
    --gray-900: #000000;
    
    /* 字体 */
    --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    
    /* 字体大小 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    
    /* 圆角 */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡 */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* 布局 */
    --navbar-height: 60px;
    --sidebar-width: 250px;
    --container-max-width: 1200px;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-4);
    color: var(--gray-800);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落 */
p {
    margin-bottom: var(--spacing-4);
}

/* 列表 */
ul, ol {
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

li {
    margin-bottom: var(--spacing-1);
}

/* 图片 */
img {
    max-width: 100%;
    height: auto;
}

/* 表格 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-4);
}

th, td {
    padding: var(--spacing-3);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

th {
    font-weight: 600;
    color: var(--gray-800);
    background-color: var(--gray-50);
}

/* 表单元素 */
input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

input, textarea, select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--white);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(34, 113, 177, 0.1);
}

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 按钮变体 */
.btn-primary {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    color: var(--white);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-success {
    color: var(--white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: var(--success-dark);
    border-color: var(--success-dark);
}

.btn-warning {
    color: var(--gray-800);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: var(--warning-dark);
    border-color: var(--warning-dark);
}

.btn-danger {
    color: var(--white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: var(--danger-dark);
    border-color: var(--danger-dark);
}

.btn-outline {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline:hover {
    color: var(--white);
    background-color: var(--primary-color);
}

/* 按钮大小 */
.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-lg);
}

.btn-icon {
    padding: var(--spacing-3);
    width: auto;
    height: auto;
}

/* 工具类 */
.hidden { display: none !important; }
.visible { display: block !important; }
.invisible { visibility: hidden !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--gray-500) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }

.border { border: 1px solid var(--gray-200) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--gray-200) !important; }
.border-bottom { border-bottom: 1px solid var(--gray-200) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: 50% !important; }

.shadow { box-shadow: var(--shadow) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-none { box-shadow: none !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-1) !important; }
.ml-2 { margin-left: var(--spacing-2) !important; }
.ml-3 { margin-left: var(--spacing-3) !important; }
.ml-4 { margin-left: var(--spacing-4) !important; }
.ml-auto { margin-left: auto !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-1) !important; }
.mr-2 { margin-right: var(--spacing-2) !important; }
.mr-3 { margin-right: var(--spacing-3) !important; }
.mr-4 { margin-right: var(--spacing-4) !important; }
.mr-auto { margin-right: auto !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --font-size-base: 0.875rem;
        --spacing-4: 0.75rem;
        --spacing-6: 1rem;
    }
    
    .btn {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-xs);
    }
    
    .btn-lg {
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-base);
    }
}
