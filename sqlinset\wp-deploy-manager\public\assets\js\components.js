/**
 * 通用组件库
 */

// 模态框组件
class Modal {
    constructor(options = {}) {
        this.options = {
            title: '',
            content: '',
            size: 'medium', // small, medium, large
            closable: true,
            backdrop: true,
            ...options
        };
        this.element = null;
        this.isOpen = false;
    }

    create() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal modal-${this.options.size}">
                <div class="modal-header">
                    <h3 class="modal-title">${this.options.title}</h3>
                    ${this.options.closable ? '<button class="modal-close">&times;</button>' : ''}
                </div>
                <div class="modal-body">
                    ${this.options.content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" data-action="cancel">取消</button>
                    <button class="btn btn-primary" data-action="confirm">确定</button>
                </div>
            </div>
        `;

        this.element = modal;
        this.bindEvents();
        return modal;
    }

    bindEvents() {
        if (!this.element) return;

        // 关闭按钮
        const closeBtn = this.element.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // 背景点击关闭
        if (this.options.backdrop) {
            this.element.addEventListener('click', (e) => {
                if (e.target === this.element) {
                    this.close();
                }
            });
        }

        // 按钮事件
        const buttons = this.element.querySelectorAll('[data-action]');
        buttons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action === 'cancel') {
                    this.close();
                } else if (action === 'confirm') {
                    this.confirm();
                }
            });
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    open() {
        if (!this.element) {
            this.create();
        }

        document.body.appendChild(this.element);
        this.isOpen = true;
        
        // 添加动画
        setTimeout(() => {
            this.element.classList.add('show');
        }, 10);

        return this;
    }

    close() {
        if (!this.isOpen) return;

        this.element.classList.remove('show');
        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.isOpen = false;
        }, 300);

        return this;
    }

    confirm() {
        if (this.options.onConfirm) {
            this.options.onConfirm();
        }
        this.close();
    }
}

// 通知组件
class Notification {
    constructor(message, type = 'info', duration = 5000) {
        this.message = message;
        this.type = type;
        this.duration = duration;
        this.element = null;
    }

    create() {
        const notification = document.createElement('div');
        notification.className = `notification notification-${this.type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${this.message}</div>
                <div class="notification-time">${new Date().toLocaleTimeString()}</div>
            </div>
            <button class="notification-close">&times;</button>
        `;

        this.element = notification;
        this.bindEvents();
        return notification;
    }

    bindEvents() {
        const closeBtn = this.element.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => this.remove());
    }

    show() {
        if (!this.element) {
            this.create();
        }

        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        container.appendChild(this.element);
        
        // 添加动画
        setTimeout(() => {
            this.element.classList.add('show');
        }, 10);

        // 自动移除
        if (this.duration > 0) {
            setTimeout(() => {
                this.remove();
            }, this.duration);
        }

        return this;
    }

    remove() {
        if (!this.element) return;

        this.element.classList.add('hide');
        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
        }, 300);
    }
}

// 加载指示器
class LoadingIndicator {
    constructor(message = '加载中...') {
        this.message = message;
        this.element = null;
    }

    show() {
        if (this.element) return;

        this.element = document.createElement('div');
        this.element.className = 'loading-overlay';
        this.element.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>${this.message}</p>
            </div>
        `;

        document.body.appendChild(this.element);
        setTimeout(() => {
            this.element.classList.add('show');
        }, 10);
    }

    hide() {
        if (!this.element) return;

        this.element.classList.remove('show');
        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
                this.element = null;
            }
        }, 300);
    }

    updateMessage(message) {
        if (this.element) {
            const p = this.element.querySelector('p');
            if (p) {
                p.textContent = message;
            }
        }
    }
}

// 进度条组件
class ProgressBar {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            value: 0,
            max: 100,
            showText: true,
            animated: true,
            ...options
        };
        this.element = null;
        this.create();
    }

    create() {
        this.element = document.createElement('div');
        this.element.className = 'progress-container';
        this.element.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill ${this.options.animated ? 'animated' : ''}"></div>
            </div>
            ${this.options.showText ? '<span class="progress-text">0%</span>' : ''}
        `;

        if (this.container) {
            this.container.appendChild(this.element);
        }
    }

    setValue(value) {
        if (!this.element) return;

        const percentage = Math.min(100, Math.max(0, (value / this.options.max) * 100));
        const fill = this.element.querySelector('.progress-fill');
        const text = this.element.querySelector('.progress-text');

        if (fill) {
            fill.style.width = percentage + '%';
        }

        if (text) {
            text.textContent = Math.round(percentage) + '%';
        }
    }

    setColor(color) {
        const fill = this.element.querySelector('.progress-fill');
        if (fill) {
            fill.style.backgroundColor = color;
        }
    }
}

// 表格组件
class DataTable {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            columns: [],
            data: [],
            pagination: true,
            pageSize: 10,
            sortable: true,
            searchable: true,
            ...options
        };
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
        this.element = null;
        this.create();
    }

    create() {
        this.element = document.createElement('div');
        this.element.className = 'data-table-container';
        
        if (this.options.searchable) {
            this.createSearchBox();
        }
        
        this.createTable();
        
        if (this.options.pagination) {
            this.createPagination();
        }

        if (this.container) {
            this.container.appendChild(this.element);
        }
    }

    createSearchBox() {
        const searchBox = document.createElement('div');
        searchBox.className = 'table-search';
        searchBox.innerHTML = `
            <input type="text" placeholder="搜索..." class="search-input">
        `;

        const input = searchBox.querySelector('.search-input');
        input.addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.render();
        });

        this.element.appendChild(searchBox);
    }

    createTable() {
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        this.options.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title;
            
            if (this.options.sortable && column.sortable !== false) {
                th.style.cursor = 'pointer';
                th.addEventListener('click', () => {
                    this.sort(column.key);
                });
            }
            
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // 创建表体
        const tbody = document.createElement('tbody');
        table.appendChild(tbody);
        
        this.element.appendChild(table);
    }

    createPagination() {
        const pagination = document.createElement('div');
        pagination.className = 'table-pagination';
        this.element.appendChild(pagination);
    }

    setData(data) {
        this.options.data = data;
        this.render();
    }

    render() {
        const tbody = this.element.querySelector('tbody');
        const filteredData = this.getFilteredData();
        const paginatedData = this.getPaginatedData(filteredData);
        
        tbody.innerHTML = '';
        
        paginatedData.forEach(row => {
            const tr = document.createElement('tr');
            
            this.options.columns.forEach(column => {
                const td = document.createElement('td');
                
                if (column.render) {
                    td.innerHTML = column.render(row[column.key], row);
                } else {
                    td.textContent = row[column.key] || '';
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        if (this.options.pagination) {
            this.renderPagination(filteredData.length);
        }
    }

    getFilteredData() {
        let data = this.options.data;
        
        // 搜索过滤
        if (this.searchTerm) {
            data = data.filter(row => {
                return this.options.columns.some(column => {
                    const value = row[column.key];
                    return value && value.toString().toLowerCase().includes(this.searchTerm.toLowerCase());
                });
            });
        }
        
        // 排序
        if (this.sortColumn) {
            data = [...data].sort((a, b) => {
                const aVal = a[this.sortColumn];
                const bVal = b[this.sortColumn];
                
                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }
        
        return data;
    }

    getPaginatedData(data) {
        if (!this.options.pagination) return data;
        
        const start = (this.currentPage - 1) * this.options.pageSize;
        const end = start + this.options.pageSize;
        return data.slice(start, end);
    }

    sort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        this.render();
    }

    renderPagination(totalItems) {
        const pagination = this.element.querySelector('.table-pagination');
        const totalPages = Math.ceil(totalItems / this.options.pageSize);
        
        pagination.innerHTML = '';
        
        if (totalPages <= 1) return;
        
        // 上一页
        if (this.currentPage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.className = 'btn btn-sm';
            prevBtn.addEventListener('click', () => {
                this.currentPage--;
                this.render();
            });
            pagination.appendChild(prevBtn);
        }
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = `btn btn-sm ${i === this.currentPage ? 'btn-primary' : 'btn-outline'}`;
            pageBtn.addEventListener('click', () => {
                this.currentPage = i;
                this.render();
            });
            pagination.appendChild(pageBtn);
        }
        
        // 下一页
        if (this.currentPage < totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.className = 'btn btn-sm';
            nextBtn.addEventListener('click', () => {
                this.currentPage++;
                this.render();
            });
            pagination.appendChild(nextBtn);
        }
    }
}

// 导出组件
window.Modal = Modal;
window.Notification = Notification;
window.LoadingIndicator = LoadingIndicator;
window.ProgressBar = ProgressBar;
window.DataTable = DataTable;
