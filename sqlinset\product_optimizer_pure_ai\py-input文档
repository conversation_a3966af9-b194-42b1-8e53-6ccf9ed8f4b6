# 处理大文件
python3 product_optimizer_requests_server.py pronamet.txt txt 8

# 参数说明:
# tpa-distribution.txt: 输入文件
# txt: 输出格式
# 8: 使用8个线程


# 上传文件
scp product_optimizer_requests_server.py user@server:/home/<USER>/
scp tpa-distribution-1k.txt user@server:/home/<USER>/

# 登录服务器
ssh user@server
cd /home/<USER>/

# 添加执行权限
chmod +x product_optimizer_requests_server.py

# 运行测试
python3 product_optimizer_requests_server.py tpa-distribution-1k.txt txt

# 后台运行大文件
nohup python3 product_optimizer_requests_server.py tpa-distribution.txt txt > process.log 2>&1 &


# 安装requests库
pip install requests

# 或者使用pip3
pip3 install requests

# 检查安装
python3 -c "import requests; print(f'requests {requests.__version__} 安装成功')"



-----------------------------------------
python3 product_optimizer_input.py
python3 product_optimizer_input.py auto

# 方法1: 使用nohup (推荐)
nohup python3 product_optimizer_input.py auto > processing.log 2>&1 &

# 方法2: 使用screen
screen -S product_optimizer
python3 product_optimizer_input.py auto
# Ctrl+A, D 分离会话

# 方法3: 使用后台模式
python3 product_optimizer_input.py background &

# 查看后台进程
ps aux | grep product_optimizer

# 查看日志
tail -f processing.log
# 或
tail -f /www/imagerdown/sqlinset/product_optimizer_pure_ai/output/processing.log


# 实时查看处理日志
tail -f processing.log

# 或者查看最后50行并持续监控
tail -50f processing.log

# 退出监控: Ctrl+C

# 查找进程ID
ps aux | grep product_optimizer

# 停止进程 (替换PID为实际进程ID)
kill PID

# 强制停止 (如果普通kill无效)
kill -9 PID

# 交互模式 (推荐首次使用)
python3 product_optimizer_gemini.py

# 自动模式
python3 product_optimizer_gemini.py auto

# 后台运行
nohup python3 product_optimizer_gemini.py auto > gemini_processing.log 2>&1 &
nohup python3 product_optimizer_gemini.py auto > processing.log 2>&1 &

# 监控进度
tail -f processing.log