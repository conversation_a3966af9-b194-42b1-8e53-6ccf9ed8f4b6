<?php
/**
 * Flatsome Child Theme Functions
 *
 * Main functions file - Keep this file clean and simple.
 * Load modules from the /inc/ directory.
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; 
}

define( 'FLATSOME_CHILD_THEME_DIR', get_stylesheet_directory() );
define( 'FLATSOME_CHILD_THEME_URI', get_stylesheet_directory_uri() );

$theme_includes = array(
    '/inc/theme-setup.php',      
    '/inc/customizer.php',      
    '/inc/woocommerce.php',   
    '/inc/differentiation.php',  
    '/inc/performance.php', 
);

foreach ( $theme_includes as $file ) {
    $filepath = FLATSOME_CHILD_THEME_DIR . $file;
    if ( file_exists( $filepath ) ) {
        require_once $filepath;
    } else {
    }
}

if (file_exists(get_stylesheet_directory() . '/inc/customizer.php')) {
    require_once get_stylesheet_directory() . '/inc/customizer.php';
}


add_filter('body_class', function($classes) {
    $preset = get_theme_mod('apply_preset_style_selector', 'default');
    if ($preset && $preset !== 'default') {
        $classes[] = 'body-style-' . esc_attr($preset);
    }
    return $classes;
});

add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    if (is_product() && isset($attr['alt']) && empty($attr['alt'])) {
        global $post;
        if ($post && $post->post_type === 'product') {
            $attr['alt'] = get_the_title($post->ID);
        }
    }
    return $attr;
}, 10, 3);

add_action('wp_head', function() {
    if (is_singular()) {
        global $post;
        echo '<link rel="canonical" href="' . esc_url(get_permalink($post->ID)) . '" />' . PHP_EOL;
    } elseif (is_home() || is_front_page()) {
        echo '<link rel="canonical" href="' . esc_url(home_url('/')) . '" />' . PHP_EOL;
    } elseif (is_category() || is_tag() || is_tax()) {
        echo '<link rel="canonical" href="' . esc_url(get_term_link(get_queried_object())) . '" />' . PHP_EOL;
    } elseif (is_archive()) {
        echo '<link rel="canonical" href="' . esc_url(get_permalink()) . '" />' . PHP_EOL;
    }
}, 1);

add_action('wp_enqueue_scripts', function() {
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-block-style');
}, 100);

add_action('init', function() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}, 1);

add_filter('wp_default_scripts', function($scripts){
    if (!is_admin() && isset($scripts->registered['jquery'])) {
        $script = $scripts->registered['jquery'];
        if ($script->deps) {
            $script->deps = array_diff($script->deps, array('jquery-migrate'));
        }
    }
});

add_action('wp_footer', function() {
    if (is_product()) {
        global $product, $post;
        if ($product && $post) {
            ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "<?php echo esc_js(get_the_title($post->ID)); ?>",
    "image": ["<?php echo esc_url(get_the_post_thumbnail_url($post->ID, 'full')); ?>"],
    "description": "<?php echo esc_js(wp_strip_all_tags($post->post_excerpt ?: $post->post_content)); ?>",
    "sku": "<?php echo esc_js($product->get_sku()); ?>",
    "offers": {
        "@type": "Offer",
        "priceCurrency": "<?php echo esc_js(get_woocommerce_currency()); ?>",
        "price": "<?php echo esc_js($product->get_price()); ?>",
        "availability": "https://schema.org/<?php echo $product->is_in_stock() ? 'InStock' : 'OutOfStock'; ?>"
    }
}
</script>
            <?php
        }
    }
});

if (!function_exists('flatsome_child_picture_tag')) {
    function flatsome_child_picture_tag($img_url, $alt = '', $width = 0, $height = 0, $extra = '') {
        $webp_url = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $img_url);
        $webp_path = '';
        if (strpos($webp_url, get_stylesheet_directory_uri()) === 0) {
            $webp_path = str_replace(get_stylesheet_directory_uri(), get_stylesheet_directory(), $webp_url);
        } elseif (strpos($webp_url, get_template_directory_uri()) === 0) {
            $webp_path = str_replace(get_template_directory_uri(), get_template_directory(), $webp_url);
        } else {
            $site_url = site_url();
            if (strpos($webp_url, $site_url) === 0) {
                $webp_path = ABSPATH . ltrim(str_replace($site_url, '', $webp_url), '/');
            }
        }
        $size_attr = ($width && $height) ? " width=\"$width\" height=\"$height\"" : '';
        $img_tag = '<img src="' . esc_url($img_url) . '" alt="' . esc_attr($alt) . '"' . $size_attr . ' ' . $extra . ' loading="lazy">';
        if ($webp_path && file_exists($webp_path)) {
            return '<picture>'
                . '<source srcset="' . esc_url($webp_url) . '" type="image/webp">'
                . $img_tag
                . '</picture>';
        } else {
            return $img_tag;
        }
    }
}

add_shortcode('auto_top_category_grid', function($atts) {
    $atts = shortcode_atts([
        'columns' => 4,
        'number' => 4
    ], $atts);

    $terms = get_terms([
        'taxonomy' => 'product_cat',
        'orderby' => 'count',
        'order' => 'DESC',
        'hide_empty' => true,
        'number' => intval($atts['number'])
    ]);
    if (empty($terms) || is_wp_error($terms)) return '';

    $output = '<div class="auto-category-grid swiper"><div class="swiper-wrapper">';

    foreach ($terms as $term) {
        $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);
        $img_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';

        if (!$img_url) {
            $products = wc_get_products([
                'status' => 'publish',
                'limit' => 1,
                'category' => [$term->slug],
                'orderby' => 'date',
                'order' => 'DESC',
                'return' => 'ids'
            ]);
            if (!empty($products)) {
                $img_url = get_the_post_thumbnail_url($products[0], 'medium');
            }
        }

        if (!$img_url) {
            $img_url = wc_placeholder_img_src('medium');
        }

        $output .= '<div class="auto-category-item swiper-slide" style="flex:1 0 '.(100/intval($atts['columns'])).'%;max-width:'.(100/intval($atts['columns'])).'%">';
        $output .= '<a href="' . esc_url(get_term_link($term)) . '">';
        $output .= flatsome_child_picture_tag($img_url, $term->name, 400, 400, 'style="width:100%;border-radius:12px;"');
        $output .= '<span style="display:block;text-align:center;margin-top:8px;font-weight:bold;">' . esc_html($term->name) . '</span>';
        $output .= '</a></div>';
    }

    $output .= '</div><div class="swiper-pagination"></div></div>';
    return $output;
});

add_shortcode('auto_top_category_banners', function($atts) {
    $atts = shortcode_atts([
        'number' => 2,
        'height' => '400px'
    ], $atts);

    $terms = get_terms([
        'taxonomy' => 'product_cat',
        'orderby' => 'count',
        'order' => 'DESC',
        'hide_empty' => true,
        'number' => intval($atts['number'])
    ]);
    if (empty($terms) || is_wp_error($terms)) return '';

    $output = '<section class="auto-category-banners-swiper swiper" aria-label="Featured Categories">';
    $output .= '<div class="swiper-wrapper">';
    
    foreach ($terms as $term) {
        $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);
        $img_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';

        if (!$img_url) {
            $products = wc_get_products([
                'status' => 'publish',
                'limit' => 1,
                'category' => [$term->slug],
                'orderby' => 'date',
                'order' => 'DESC',
                'return' => 'ids'
            ]);
            if (!empty($products)) {
                $img_url = get_the_post_thumbnail_url($products[0], 'large');
            }
        }

        if (!$img_url) {
            $img_url = wc_placeholder_img_src('large');
        }

        $description = term_description($term->term_id, 'product_cat');
        $description = wp_strip_all_tags($description);
        $alt = esc_attr($term->name);
        $output .= '<article class="auto-category-banner-slide swiper-slide" itemscope itemtype="https://schema.org/Category">';
        $output .= '<div class="auto-category-banner" style="height:' . esc_attr($atts['height']) . '">';
        $output .= flatsome_child_picture_tag($img_url, $alt, 1200, intval(str_replace('px', '', $atts['height'])), 'itemprop="image" loading="lazy" class="category-banner-image"');
        $output .= '<div class="auto-category-banner-content" itemprop="description">';
        $output .= '<h2 class="auto-category-banner-title" itemprop="name">' . esc_html($term->name) . '</h2>';
        if ($description) {
            $output .= '<p class="auto-category-banner-description">' . esc_html($description) . '</p>';
        }
        $output .= '<a href="' . esc_url(get_term_link($term)) . '" class="auto-category-banner-btn" itemprop="url" aria-label="' . sprintf(esc_attr__('View all products in %s', 'flatsome-child'), esc_attr($term->name)) . '">' . esc_html__('Shop Now', 'flatsome-child') . '</a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</article>';
    }
    $output .= '</div>';
    $output .= '<div class="swiper-pagination" aria-label="' . esc_attr__('Category slides', 'flatsome-child') . '"></div>';
    $output .= '</section>';
    return $output;
});

add_action('wp_footer', function() {
    ?>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <style>
    .swiper-pagination-bullet {
      background: #bbb;
      opacity: 1;
      margin: 0 4px !important;
      width: 8px;
      height: 8px;
      transition: background 0.2s;
    }
    .swiper-pagination-bullet-active {
      background: #0073aa;
    }
    .auto-category-banners-swiper .swiper-slide,
    .auto-category-grid .swiper-slide {
      box-sizing: border-box;
    }
    @media (max-width: 900px) {
      .auto-category-banners-swiper .swiper-slide {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
      }
      .auto-category-grid .swiper-slide {
        width: 50% !important;
        max-width: 50% !important;
        flex: 0 0 50% !important;
      }
    }
    </style>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
      if (document.querySelector('.auto-category-banners-swiper.swiper')) {
        new Swiper('.auto-category-banners-swiper.swiper', {
          slidesPerView: 1,
          spaceBetween: 0,
          centeredSlides: false,
          loop: true,
          pagination: { el: '.auto-category-banners-swiper .swiper-pagination', clickable: true },
          breakpoints: {
            901: { 
              slidesPerView: 'auto',
              spaceBetween: 24,
              loop: false,
              allowTouchMove: false
            }
          }
        });
      }
      if (document.querySelector('.auto-category-grid.swiper')) {
        new Swiper('.auto-category-grid.swiper', {
          slidesPerView: 2,
          spaceBetween: 12,
          centeredSlides: false,
          loop: false,
          pagination: { el: '.auto-category-grid .swiper-pagination', clickable: true },
          breakpoints: {
            901: { 
              slidesPerView: 'auto',
              spaceBetween: 32,
              allowTouchMove: false
            }
          }
        });
      }
    });
    </script>
    <?php
});

add_action('wp_head', function() {
    if (is_front_page()) {
        $terms = get_terms([
            'taxonomy' => 'product_cat',
            'orderby' => 'count',
            'order' => 'DESC',
            'hide_empty' => true,
            'number' => 1
        ]);
        if (!empty($terms) && !is_wp_error($terms)) {
            $thumbnail_id = get_term_meta($terms[0]->term_id, 'thumbnail_id', true);
            $img_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';
            if ($img_url) {
                echo '<link rel="preload" as="image" href="' . esc_url($img_url) . '" imagesrcset="' . esc_url($img_url) . ' 1200w" imagesizes="100vw" />';
            }
        }
    }
}, 2);

add_action('wp_head', function() {
    if (is_front_page()) {
        echo '<style>.auto-category-banners-swiper{display:flex;gap:24px;justify-content:center;align-items:stretch;margin-bottom:40px;flex-wrap:wrap;position:relative;}.auto-category-banner{position:relative;width:100%;height:400px;overflow:hidden;border-radius:16px;background:#f5f5f5;display:flex;align-items:flex-end;justify-content:flex-start;}.auto-category-banner img{width:100%;height:100%;object-fit:cover;position:absolute;top:0;left:0;z-index:1;border-radius:16px;transition:transform .3s ease;}.auto-category-banner-content{position:relative;z-index:2;padding:32px;color:#fff;font-weight:700;text-shadow:0 2px 8px rgba(0,0,0,.25);background:linear-gradient(to top,rgba(0,0,0,.7),transparent);width:100%;}</style>';
    }
}, 3);

add_action('wp_enqueue_scripts', function() {
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-block-style');
}, 100);

add_filter('flatsome_viewport_meta', function($meta){
    return '<meta name="viewport" content="width=device-width, initial-scale=1">';
});