<?php
$requirements = getRequirementsStatus();
$allPassed = !in_array(false, array_column($requirements, 'status'), true);
?>

<div class="requirements-content">
    <h2>环境检查</h2>
    <p>正在检查您的服务器环境是否满足系统要求...</p>
    
    <div class="requirements-table-container">
        <table class="requirements-table">
            <thead>
                <tr>
                    <th>检查项目</th>
                    <th>要求</th>
                    <th>当前状态</th>
                    <th>结果</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($requirements as $name => $requirement): ?>
                <tr class="<?php echo $requirement['status'] ? 'requirement-pass' : 'requirement-fail'; ?>">
                    <td><?php echo htmlspecialchars($name); ?></td>
                    <td><?php echo htmlspecialchars($requirement['required']); ?></td>
                    <td><?php echo htmlspecialchars($requirement['current']); ?></td>
                    <td>
                        <?php if ($requirement['status']): ?>
                            <span class="status-ok">✓ 通过</span>
                        <?php else: ?>
                            <span class="status-error">✗ 失败</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <?php if ($allPassed): ?>
        <div class="alert alert-success">
            <strong>恭喜！</strong>您的服务器环境满足所有要求，可以继续安装。
        </div>
    <?php else: ?>
        <div class="alert alert-error">
            <strong>注意：</strong>您的服务器环境不满足部分要求，请先解决以下问题：
            <ul>
                <?php foreach ($requirements as $name => $requirement): ?>
                    <?php if (!$requirement['status']): ?>
                        <li><?php echo htmlspecialchars($name); ?>: <?php echo htmlspecialchars($requirement['current']); ?></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <div class="troubleshooting">
            <h4>解决方案</h4>
            <div class="solution-item">
                <strong>PHP版本过低：</strong>
                <p>请升级PHP到8.0或更高版本。在宝塔面板中：网站 → PHP版本 → 切换到PHP 8.0+</p>
            </div>
            
            <div class="solution-item">
                <strong>缺少PHP扩展：</strong>
                <p>在宝塔面板中：软件商店 → PHP → 设置 → 安装扩展</p>
            </div>
            
            <div class="solution-item">
                <strong>目录权限问题：</strong>
                <p>执行命令：<code>chown -R www:www /www/wwwroot/<?php echo basename(INSTALL_ROOT); ?></code></p>
            </div>
            
            <div class="solution-item">
                <strong>原始脚本问题：</strong>
                <p>确保原始部署脚本存在且可执行：<code>chmod +x /www/wwwroot/imagerdown/sqlinset/deployclone/auto_db_clone_config_replace.sh</code></p>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="system-details">
        <h4>系统详细信息</h4>
        <div class="details-grid">
            <div class="detail-item">
                <strong>PHP版本：</strong>
                <span><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="detail-item">
                <strong>操作系统：</strong>
                <span><?php echo php_uname('s') . ' ' . php_uname('r'); ?></span>
            </div>
            <div class="detail-item">
                <strong>Web服务器：</strong>
                <span><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
            </div>
            <div class="detail-item">
                <strong>内存限制：</strong>
                <span><?php echo ini_get('memory_limit'); ?></span>
            </div>
            <div class="detail-item">
                <strong>最大执行时间：</strong>
                <span><?php echo ini_get('max_execution_time'); ?>秒</span>
            </div>
            <div class="detail-item">
                <strong>上传限制：</strong>
                <span><?php echo ini_get('upload_max_filesize'); ?></span>
            </div>
        </div>
    </div>
</div>

<style>
.requirements-content {
    max-width: 800px;
    margin: 0 auto;
}

.requirements-table-container {
    margin: 20px 0;
    overflow-x: auto;
}

.requirement-pass {
    background-color: #f8fff8;
}

.requirement-fail {
    background-color: #fff8f8;
}

.troubleshooting {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.solution-item {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.solution-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.solution-item code {
    background: #f1f2f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 14px;
}

.system-details {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border-radius: 5px;
}

.detail-item strong {
    color: #2c3e50;
}

.detail-item span {
    color: #666;
    font-family: monospace;
}
</style>

<script>
// 自动刷新检查结果
setTimeout(function() {
    if (!<?php echo $allPassed ? 'true' : 'false'; ?>) {
        location.reload();
    }
}, 5000);
</script>
