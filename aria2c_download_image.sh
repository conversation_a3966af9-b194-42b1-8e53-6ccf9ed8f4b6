#!/bin/bash

# 设置图片链接文件和下载目标文件夹
IMAGE_LINKS_FILE="image_links.txt"
TARGET_FOLDER="./imagedownload"

# 设置代理 IP 列表，直接添加到脚本中
PROXY_LIST=(
    "socks5://juztdjks:58sdcy4mb85d@**************:6437"
    "socks5://juztdjks:58sdcy4mb85d@*************:6646"
    "socks5://juztdjks:58sdcy4mb85d@*************:5685"
    "socks5://juztdjks:58sdcy4mb85d@**************:6099"
    "socks5://juztdjks:58sdcy4mb85d@***************:6101"
    "socks5://juztdjks:58sdcy4mb85d@************:6715"
    "socks5://juztdjks:58sdcy4mb85d@***********:5709"
    "socks5://juztdjks:58sdcy4mb85d@*************:6132"
    # 可以继续添加更多代理 IP...
)

# 创建目标文件夹
mkdir -p "$TARGET_FOLDER"

# 随机选择代理 IP
get_random_proxy() {
    PROXY_COUNT=${#PROXY_LIST[@]}
    RANDOM_PROXY_INDEX=$((RANDOM % PROXY_COUNT))
    echo "${PROXY_LIST[$RANDOM_PROXY_INDEX]}"
}

# 下载图片的函数
download_image() {
    local URL="$1"
    local PROXY="$2"
    local FILENAME="$TARGET_FOLDER/$(basename "$URL")"

    # 调试输出，查看正在使用的代理和图片 URL
    echo "Using proxy: $PROXY"
    echo "Downloading: $URL"

    # 使用 aria2c 下载图片，确保不使用 --proxy-method 参数
    aria2c --proxy="$PROXY" --max-connection-per-server=4 --split=4 --continue=true --out="$FILENAME" "$URL"
}

# 从文件中读取图片链接并下载
while IFS= read -r URL; do
    if [[ -n "$URL" ]]; then
        PROXY=$(get_random_proxy)
        download_image "$URL" "$PROXY"
    fi
done < "$IMAGE_LINKS_FILE"
