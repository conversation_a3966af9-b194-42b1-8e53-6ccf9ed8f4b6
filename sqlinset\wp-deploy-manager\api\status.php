<?php
/**
 * 系统状态API接口
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 安全检查
checkApiAccess();

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'system':
            handleSystemStatus();
            break;
            
        case 'queue':
            handleQueueStatus();
            break;
            
        case 'toggle_queue':
            handleToggleQueue();
            break;
            
        case 'sites_health':
            handleSitesHealth();
            break;
            
        case 'run_health_check':
            handleRunHealthCheck();
            break;
            
        default:
            errorResponse('Invalid action', 400);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'Status API error: ' . $e->getMessage());
    errorResponse($e->getMessage(), 500);
}

/**
 * 获取系统状态
 */
function handleSystemStatus() {
    try {
        $systemInfo = getSystemInfo();
        
        // 获取负载平均值
        $loadAvg = sys_getloadavg();
        
        // 计算磁盘使用率
        $totalSpace = disk_total_space(ROOT_PATH);
        $freeSpace = disk_free_space(ROOT_PATH);
        $usedSpace = $totalSpace - $freeSpace;
        $diskUsage = ($usedSpace / $totalSpace) * 100;
        
        // 获取PHP进程数
        $phpProcesses = 0;
        if (function_exists('shell_exec')) {
            $phpProcesses = (int)shell_exec('ps aux | grep php-fpm | grep -v grep | wc -l');
        }
        
        $status = [
            'load_average' => $loadAvg,
            'disk_usage' => round($diskUsage, 2),
            'disk_total' => $totalSpace,
            'disk_free' => $freeSpace,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'php_processes' => $phpProcesses,
            'php_version' => PHP_VERSION,
            'uptime' => getSystemUptime(),
            'timestamp' => date(DATE_FORMAT)
        ];
        
        successResponse($status);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取队列状态
 */
function handleQueueStatus() {
    try {
        $queueManager = new QueueManager();
        $status = $queueManager->getQueueStatus();
        
        // 添加队列是否暂停的状态
        $status['is_paused'] = $queueManager->isQueuePaused();
        
        successResponse($status);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 暂停/恢复队列
 */
function handleToggleQueue() {
    $action = sanitizeInput($_POST['action'] ?? '');
    
    if (!in_array($action, ['pause', 'resume'])) {
        errorResponse('Invalid queue action');
    }
    
    try {
        $queueManager = new QueueManager();
        
        if ($action === 'pause') {
            $result = $queueManager->pauseQueue();
            $message = 'Queue paused successfully';
        } else {
            $result = $queueManager->resumeQueue();
            $message = 'Queue resumed successfully';
        }
        
        successResponse(['action' => $action], $message);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 获取站点健康状态
 */
function handleSitesHealth() {
    try {
        $db = getDatabase();
        
        // 获取健康概览
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_sites,
                SUM(CASE WHEN status = 'healthy' THEN 1 ELSE 0 END) as healthy_sites,
                SUM(CASE WHEN status = 'warning' THEN 1 ELSE 0 END) as warning_sites,
                SUM(CASE WHEN status = 'critical' THEN 1 ELSE 0 END) as critical_sites,
                SUM(CASE WHEN status = 'unknown' THEN 1 ELSE 0 END) as unknown_sites,
                ROUND(AVG(health_score), 2) as avg_health_score,
                ROUND(AVG(response_time), 3) as avg_response_time
            FROM site_health
            WHERE last_check_at > DATE_SUB(NOW(), INTERVAL 1 DAY)
        ");
        $overview = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 获取详细站点列表
        $stmt = $db->prepare("
            SELECT domain, status, health_score, response_time, last_check_at
            FROM site_health 
            ORDER BY last_check_at DESC 
            LIMIT 50
        ");
        $stmt->execute();
        $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        successResponse([
            'overview' => $overview,
            'sites' => $sites
        ]);
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 执行健康检查
 */
function handleRunHealthCheck() {
    $domain = sanitizeInput($_POST['domain'] ?? '');
    
    try {
        if (empty($domain)) {
            // 检查所有最近部署的站点
            $db = getDatabase();
            $stmt = $db->query("
                SELECT DISTINCT domain 
                FROM deploy_jobs 
                WHERE status = 'completed' 
                AND completed_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY completed_at DESC 
                LIMIT 20
            ");
            $domains = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } else {
            $domains = [$domain];
        }
        
        $results = [];
        foreach ($domains as $checkDomain) {
            $results[$checkDomain] = performSiteHealthCheck($checkDomain);
        }
        
        successResponse($results, 'Health check completed');
        
    } catch (Exception $e) {
        errorResponse($e->getMessage());
    }
}

/**
 * 执行单个站点健康检查
 */
function performSiteHealthCheck($domain) {
    $results = [
        'domain' => $domain,
        'timestamp' => date(DATE_FORMAT),
        'checks' => []
    ];
    
    $score = 0;
    $maxScore = 5;
    
    try {
        // 1. 检查目录是否存在
        $webroot = "/www/wwwroot/$domain";
        $results['checks']['directory_exists'] = is_dir($webroot);
        if ($results['checks']['directory_exists']) $score++;
        
        // 2. 检查WordPress配置
        $wpConfig = "$webroot/wp-config.php";
        $results['checks']['wp_config_exists'] = file_exists($wpConfig);
        if ($results['checks']['wp_config_exists']) $score++;
        
        // 3. HTTP响应检查
        $httpStatus = checkHttpResponse("http://$domain");
        $results['checks']['http_status'] = $httpStatus;
        $results['http_code'] = $httpStatus['code'];
        if ($httpStatus['success']) $score++;
        
        // 4. HTTPS响应检查
        $httpsStatus = checkHttpResponse("https://$domain");
        $results['checks']['https_status'] = $httpsStatus;
        $results['https_code'] = $httpsStatus['code'];
        if ($httpsStatus['success']) $score++;
        
        // 5. 响应时间检查
        $results['response_time'] = $httpStatus['response_time'];
        if ($httpStatus['response_time'] < 3.0) $score++;
        
        // 计算健康分数
        $results['health_score'] = round(($score / $maxScore) * 100);
        
        // 确定状态
        if ($results['health_score'] >= 80) {
            $results['status'] = 'healthy';
        } elseif ($results['health_score'] >= 60) {
            $results['status'] = 'warning';
        } else {
            $results['status'] = 'critical';
        }
        
        // 保存到数据库
        saveSiteHealthResult($results);
        
    } catch (Exception $e) {
        $results['error'] = $e->getMessage();
        $results['status'] = 'unknown';
        $results['health_score'] = 0;
    }
    
    return $results;
}

/**
 * 检查HTTP响应
 */
function checkHttpResponse($url) {
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $responseTime = microtime(true) - $startTime;
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 400,
        'code' => $httpCode,
        'response_time' => round($responseTime, 3),
        'error' => $error
    ];
}

/**
 * 保存站点健康检查结果
 */
function saveSiteHealthResult($results) {
    try {
        $db = getDatabase();
        
        $stmt = $db->prepare("
            INSERT INTO site_health 
            (domain, status, http_status, https_status, response_time, health_score, issues, last_check_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            status = VALUES(status),
            http_status = VALUES(http_status),
            https_status = VALUES(https_status),
            response_time = VALUES(response_time),
            health_score = VALUES(health_score),
            issues = VALUES(issues),
            last_check_at = VALUES(last_check_at)
        ");
        
        $issues = [];
        if (!$results['checks']['directory_exists']) $issues[] = 'Directory not found';
        if (!$results['checks']['wp_config_exists']) $issues[] = 'WordPress config missing';
        if (!$results['checks']['http_status']['success']) $issues[] = 'HTTP check failed';
        if (!$results['checks']['https_status']['success']) $issues[] = 'HTTPS check failed';
        if ($results['response_time'] > 3.0) $issues[] = 'Slow response time';
        
        $stmt->execute([
            $results['domain'],
            $results['status'],
            $results['http_code'],
            $results['https_code'],
            $results['response_time'],
            $results['health_score'],
            json_encode($issues)
        ]);
        
    } catch (Exception $e) {
        logMessage('ERROR', 'Failed to save health check result', [
            'domain' => $results['domain'],
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * 获取系统运行时间
 */
function getSystemUptime() {
    if (file_exists('/proc/uptime')) {
        $uptime = file_get_contents('/proc/uptime');
        $uptime = floatval(explode(' ', $uptime)[0]);
        return $uptime;
    }
    return null;
}
