#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
product_optimizer_gemini_background.py - 专用后台运行版本
完全移除交互式输入，专为nohup后台运行设计
"""

import requests
import json
import time
import os
import random
import sys
import threading
import platform
import glob
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Google Gemini API配置
GEMINI_API_KEY = "AIzaSyDJV45JlTkxweCaT4bSHT42szZ1q1384dg"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# 路径配置
INPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/input"
OUTPUT_DIR = "/www/imagerdown/sqlinset/product_optimizer_pure_ai/output"

# 后台运行优化配置
MAX_WORKERS = 1  # 单线程，确保稳定
BATCH_SIZE = 8   # 每批8个产品
REQUEST_DELAY = 4.5  # 4.5秒延迟，确保不超过15 RPM
TIMEOUT_SECONDS = 90
MAX_RETRIES = 4

# 速率限制保护
RATE_LIMIT_BUFFER = 0.5
MIN_REQUEST_INTERVAL = 4.0

# 统计锁和速率控制
stats_lock = threading.Lock()
rate_limit_lock = threading.Lock()
last_request_time = 0

processing_stats = {
    "completed_batches": 0,
    "total_batches": 0,
    "start_time": 0,
    "successful_products": 0,
    "failed_products": 0,
    "network_errors": 0,
    "timeout_errors": 0,
    "rate_limit_errors": 0,
    "total_requests": 0
}

def log_message(message):
    """统一日志输出"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def ai_optimize_products_gemini(batch_data, target_lang="auto", max_retries=MAX_RETRIES):
    """使用Gemini Pro的AI产品优化"""
    batch_num, products_batch = batch_data
    
    # 产品优化提示词
    prompt = f"""
You are a professional e-commerce product analysis expert. Please perform intelligent analysis and optimization for the following products:

Product List:
{chr(10).join([f"{i+1}. {product}" for i, product in enumerate(products_batch)])}

Please complete the following tasks for each product:

1. **Product Category Analysis**:
   - Intelligently generate 1-4 level category hierarchy based on product characteristics
   - Reference Amazon/eBay standard category systems
   - Categories should align with user shopping habits and search logic
   - Use fewer levels for simple products, more levels for complex products

2. **SEO Title Optimization**:
   - Generate search engine friendly product titles
   - Retain core product information and specifications
   - Optimize keyword layout to improve search rankings
   - Titles should be professional, concise, and attractive

3. **Keyword Generation**:
   - Generate 1-2 keywords highly relevant to the product core
   - Can be long-tail keywords or precise phrases
   - Keywords should be terms users would actually search for
   - Focus on highlighting core product features and uses

Requirements:
- Output language: 自动检测
- Based entirely on AI intelligent analysis, no preset rules
- Category levels determined naturally by product complexity
- SEO titles should not contain pipe symbols (|)
- Keywords should be concise and precise, total length within 30 characters

Output Format (Strict JSON):
{{
  "results": [
    {{
      "seo_name": "SEO optimized product title",
      "category": "Intelligently analyzed category path",
      "tags": "keyword1, keyword2"
    }}
  ]
}}

Please ensure the returned JSON array contains results for all {len(products_batch)} products.
"""
    
    for attempt in range(max_retries):
        try:
            # 严格的速率限制保护
            with rate_limit_lock:
                global last_request_time
                current_time = time.time()
                
                time_since_last = current_time - last_request_time
                min_interval = REQUEST_DELAY + RATE_LIMIT_BUFFER
                
                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    log_message(f"速率限制保护: 等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                
                extra_delay = random.uniform(0.2, 0.8)
                time.sleep(extra_delay)
                
                last_request_time = time.time()
                
                with stats_lock:
                    processing_stats["total_requests"] += 1
            
            # Gemini API请求
            data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 40,
                    "topP": 0.8,
                    "maxOutputTokens": 4000
                }
            }
            
            response = requests.post(
                f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=TIMEOUT_SECONDS
            )
            
            response.raise_for_status()
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                candidate = result['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content']:
                    content = candidate['content']['parts'][0]['text'].strip()
                    
                    try:
                        # 清理JSON内容
                        clean_content = content.strip()
                        
                        if "```json" in clean_content:
                            start_idx = clean_content.find("```json") + 7
                            end_idx = clean_content.find("```", start_idx)
                            if end_idx > start_idx:
                                clean_content = clean_content[start_idx:end_idx].strip()
                        
                        parsed_result = json.loads(clean_content)
                        
                        if isinstance(parsed_result, dict) and "results" in parsed_result:
                            results_array = parsed_result["results"]
                        elif isinstance(parsed_result, list):
                            results_array = parsed_result
                        else:
                            results_array = [parsed_result] if isinstance(parsed_result, dict) else []
                        
                        # 清理结果
                        cleaned_results = []
                        for result in results_array:
                            seo_name = result.get("seo_name", "").replace("|", "-").replace("｜", "-")
                            category = result.get("category", "")
                            tags = result.get("tags", "")
                            
                            # 优化关键词
                            if tags:
                                tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
                                optimized_tags = []
                                for tag in tag_list[:2]:
                                    if len(tag) <= 25:
                                        optimized_tags.append(tag)
                                tags = ", ".join(optimized_tags)
                                if len(tags) > 30:
                                    tags = optimized_tags[0] if optimized_tags else ""
                            
                            cleaned_result = {
                                "seo_name": seo_name,
                                "category": category,
                                "tags": tags
                            }
                            cleaned_results.append(cleaned_result)
                        
                        # 更新统计
                        with stats_lock:
                            processing_stats["completed_batches"] += 1
                            processing_stats["successful_products"] += len(cleaned_results)
                            
                            completed = processing_stats["completed_batches"]
                            total = processing_stats["total_batches"]
                            elapsed = time.time() - processing_stats["start_time"]
                            
                            if completed > 0:
                                rate = completed / elapsed
                                eta = (total - completed) / rate if rate > 0 else 0
                                progress = (completed / total) * 100
                                
                                log_message(f"批次 {batch_num} 完成 | 进度: {progress:.1f}% ({completed}/{total}) | ETA: {eta/60:.1f}分钟")
                        
                        return batch_num, cleaned_results
                        
                    except json.JSONDecodeError as e:
                        log_message(f"批次 {batch_num} JSON解析失败 (尝试 {attempt + 1}): {e}")
                        if attempt == max_retries - 1:
                            return batch_num, generate_basic_results(products_batch)
            
        except requests.exceptions.RequestException as e:
            with stats_lock:
                if "429" in str(e) or "rate" in str(e).lower() or "quota" in str(e).lower():
                    processing_stats["rate_limit_errors"] += 1
                elif "timeout" in str(e).lower():
                    processing_stats["timeout_errors"] += 1
                else:
                    processing_stats["network_errors"] += 1
            
            log_message(f"批次 {batch_num} 网络错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                if "429" in str(e) or "rate" in str(e).lower() or "quota" in str(e).lower():
                    delay = 10 * (attempt + 1)
                    log_message(f"速率限制触发，等待 {delay} 秒...")
                elif "timeout" in str(e).lower():
                    delay = 3 ** attempt
                else:
                    delay = 2 ** attempt
                time.sleep(delay)
        except Exception as e:
            log_message(f"批次 {batch_num} 未知错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    # 所有尝试失败
    with stats_lock:
        processing_stats["completed_batches"] += 1
        processing_stats["failed_products"] += len(products_batch)
    
    log_message(f"批次 {batch_num} 处理失败，使用备用方案")
    return batch_num, generate_basic_results(products_batch)

def generate_basic_results(products_batch):
    """生成基础结果（备用方案）"""
    results = []
    
    for product in products_batch:
        words = product.split()
        brand = words[0] if words else "Unknown"
        
        results.append({
            "seo_name": product.replace("|", "-"),
            "category": "General > Products",
            "tags": f"{brand} products"
        })
    
    return results

def process_file_background(input_path, output_path):
    """后台模式文件处理 - 无交互式输入"""
    log_message(f"开始处理文件: {os.path.basename(input_path)}")

    # 读取文件
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]
    except Exception as e:
        log_message(f"读取文件错误: {e}")
        return False

    log_message(f"产品数量: {len(products):,} 个")

    # 大文件自动分片
    if len(products) >= 10000:
        log_message(f"检测到大文件，自动启用分片处理")
        return process_file_in_chunks_background(output_path, products)

    # 准备批次数据
    batch_data = []
    for i in range(0, len(products), BATCH_SIZE):
        batch_num = (i // BATCH_SIZE) + 1
        batch = products[i:i+BATCH_SIZE]
        batch_data.append((batch_num, batch))

    # 初始化统计
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["start_time"] = time.time()
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0

    estimated_time = len(products) / 10
    log_message(f"预估处理时间: {estimated_time:.1f} 分钟 ({estimated_time/60:.1f} 小时)")
    log_message(f"开始并发处理 ({len(batch_data)} 个批次，{MAX_WORKERS} 个线程)")

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_gemini, batch, "auto"): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                log_message(f"批次 {batch_num} 执行异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    # 保存结果
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("SEO Name | Category | Tags\n")
            f.write("="*120 + "\n")

            for result in final_results:
                seo_name = result.get("seo_name", "").replace("|", "-")
                category = result.get("category", "")
                tags = result.get("tags", "")
                f.write(f"{seo_name} | {category} | {tags}\n")

        elapsed_time = time.time() - processing_stats["start_time"]
        successful = processing_stats["successful_products"]
        failed = processing_stats["failed_products"]
        success_rate = (successful / (successful + failed)) * 100 if (successful + failed) > 0 else 0

        log_message("处理完成!")
        log_message(f"成功: {successful:,} | 失败: {failed:,} | 成功率: {success_rate:.1f}%")
        log_message(f"总时间: {elapsed_time/60:.1f} 分钟")
        log_message(f"处理速度: {len(products)/(elapsed_time/60):.0f} 产品/分钟")
        log_message(f"结果保存到: {output_path}")

        return True

    except Exception as e:
        log_message(f"保存结果错误: {e}")
        return False

def process_file_in_chunks_background(output_path, products):
    """后台模式分片处理"""
    chunk_size = 3000
    total_products = len(products)
    total_chunks = (total_products + chunk_size - 1) // chunk_size

    log_message("启动自动分片处理:")
    log_message(f"  总产品数: {total_products:,}")
    log_message(f"  分片大小: {chunk_size:,} 产品/片")
    log_message(f"  分片数量: {total_chunks} 片")
    log_message(f"  预估总时间: {(total_products / 10) / 60:.1f} 小时")

    all_results = []
    successful_chunks = 0

    try:
        for chunk_idx in range(total_chunks):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, total_products)
            chunk_products = products[start_idx:end_idx]

            log_message(f"处理分片 {chunk_idx + 1}/{total_chunks} (产品 {start_idx + 1}-{end_idx})")

            # 处理当前分片
            chunk_results = process_chunk_background(chunk_products, chunk_idx + 1)

            if chunk_results:
                all_results.extend(chunk_results)
                successful_chunks += 1
                log_message(f"分片 {chunk_idx + 1} 处理完成")
            else:
                log_message(f"分片 {chunk_idx + 1} 处理失败")

            # 显示总体进度
            overall_progress = ((chunk_idx + 1) / total_chunks) * 100
            processed_products = len(all_results)
            log_message(f"总体进度: {overall_progress:.1f}% ({processed_products}/{total_products} 产品)")

        # 保存最终结果
        if all_results:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")

                for result in all_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")

            log_message("分片处理完成!")
            log_message(f"成功分片: {successful_chunks}/{total_chunks}")
            log_message(f"处理产品: {len(all_results)}/{total_products}")
            log_message(f"输出文件: {output_path}")

            return True
        else:
            log_message("所有分片处理失败")
            return False

    except KeyboardInterrupt:
        log_message("用户中断处理")
        if all_results:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("SEO Name | Category | Tags\n")
                f.write("="*120 + "\n")
                for result in all_results:
                    seo_name = result.get("seo_name", "").replace("|", "-")
                    category = result.get("category", "")
                    tags = result.get("tags", "")
                    f.write(f"{seo_name} | {category} | {tags}\n")
            log_message(f"部分结果已保存到: {output_path}")
        return False
    except Exception as e:
        log_message(f"分片处理异常: {e}")
        return False

def process_chunk_background(products_chunk, chunk_num):
    """处理单个分片 - 后台版本"""
    batch_data = []
    for i in range(0, len(products_chunk), BATCH_SIZE):
        batch_num = (i // BATCH_SIZE) + 1
        batch = products_chunk[i:i+BATCH_SIZE]
        batch_data.append((batch_num, batch))

    # 重置统计
    global processing_stats
    processing_stats["total_batches"] = len(batch_data)
    processing_stats["completed_batches"] = 0
    processing_stats["successful_products"] = 0
    processing_stats["failed_products"] = 0
    processing_stats["start_time"] = time.time()

    # 并发处理
    results = {}
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_batch = {
            executor.submit(ai_optimize_products_gemini, batch, "auto"): batch[0]
            for batch in batch_data
        }

        for future in as_completed(future_to_batch):
            try:
                batch_num, batch_results = future.result()
                results[batch_num] = batch_results
            except Exception as e:
                batch_num = future_to_batch[future]
                log_message(f"分片{chunk_num} 批次{batch_num} 异常: {e}")

    # 按顺序整理结果
    final_results = []
    for batch_num in sorted(results.keys()):
        batch_results = results[batch_num]
        for result in batch_results:
            final_results.append(result)

    return final_results

def main():
    """主函数 - 后台版本"""
    log_message("Gemini版产品优化器 - 后台专用版本")
    log_message(f"API: Google Gemini 1.5 Flash (15 RPM免费)")
    log_message(f"环境: {platform.system()} {platform.release()}")
    log_message(f"配置: {MAX_WORKERS}线程, {BATCH_SIZE}产品/批次, {REQUEST_DELAY}秒延迟")

    # 确保目录存在
    os.makedirs(INPUT_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 查找文件
    supported_extensions = ["*.txt", "*.csv", "*.tsv", "*.dat", "*.text"]
    all_files = []

    for pattern in supported_extensions:
        file_pattern = os.path.join(INPUT_DIR, pattern)
        found_files = glob.glob(file_pattern)
        all_files.extend(found_files)

    for item in os.listdir(INPUT_DIR):
        item_path = os.path.join(INPUT_DIR, item)
        if os.path.isfile(item_path) and not item.startswith('.') and '.' not in item:
            all_files.append(item_path)

    txt_files = sorted(list(set(all_files)))

    if not txt_files:
        log_message(f"在 {INPUT_DIR} 目录中未找到文件")
        return

    log_message(f"发现 {len(txt_files)} 个文件，开始自动处理...")

    success_count = 0
    total_start_time = time.time()

    for i, input_file in enumerate(txt_files, 1):
        log_message(f"处理文件 {i}/{len(txt_files)}: {os.path.basename(input_file)}")

        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(OUTPUT_DIR, f"{base_name}_gemini.txt")

        success = process_file_background(input_file, output_file)

        if success:
            success_count += 1
            log_message(f"文件 {i} 处理完成")
        else:
            log_message(f"文件 {i} 处理失败")

    # 总结
    total_elapsed = time.time() - total_start_time
    log_message("所有文件处理完成!")
    log_message(f"成功处理: {success_count}/{len(txt_files)} 个文件")
    log_message(f"总耗时: {total_elapsed/60:.1f} 分钟")
    log_message(f"输出目录: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
