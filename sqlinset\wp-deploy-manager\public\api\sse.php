<?php
/**
 * Server-Sent Events API
 * 提供实时数据流
 */

define('WP_DEPLOY_MANAGER', true);
require_once '../config/config.php';

// 设置SSE头
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 禁用输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'job_progress':
            streamJobProgress();
            break;
            
        case 'system_status':
            streamSystemStatus();
            break;
            
        case 'queue_status':
            streamQueueStatus();
            break;
            
        default:
            sendEvent('error', 'Invalid action');
            exit;
    }
    
} catch (Exception $e) {
    sendEvent('error', $e->getMessage());
    exit;
}

/**
 * 流式传输任务进度
 */
function streamJobProgress() {
    $jobId = intval($_GET['job_id'] ?? 0);
    
    if ($jobId <= 0) {
        sendEvent('error', 'Job ID is required');
        exit;
    }
    
    $db = getDatabase();
    $lastProgress = -1;
    $maxIterations = 600; // 10分钟
    $iteration = 0;
    
    while ($iteration < $maxIterations) {
        try {
            // 获取任务状态
            $stmt = $db->prepare("
                SELECT id, status, progress, current_step, error_message, 
                       started_at, completed_at, updated_at
                FROM deploy_jobs 
                WHERE id = ?
            ");
            $stmt->execute([$jobId]);
            $job = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$job) {
                sendEvent('error', 'Job not found');
                break;
            }
            
            // 检查进度是否有变化
            if ($job['progress'] != $lastProgress || $iteration % 10 == 0) {
                $data = [
                    'job_id' => $jobId,
                    'status' => $job['status'],
                    'progress' => intval($job['progress']),
                    'current_step' => $job['current_step'],
                    'error_message' => $job['error_message'],
                    'updated_at' => $job['updated_at']
                ];
                
                // 计算运行时间
                if ($job['started_at']) {
                    $startTime = new DateTime($job['started_at']);
                    $endTime = $job['completed_at'] ? new DateTime($job['completed_at']) : new DateTime();
                    $data['duration'] = $endTime->getTimestamp() - $startTime->getTimestamp();
                }
                
                sendEvent('progress', $data);
                $lastProgress = $job['progress'];
            }
            
            // 检查任务是否完成
            if (in_array($job['status'], ['completed', 'failed', 'cancelled'])) {
                sendEvent('complete', [
                    'job_id' => $jobId,
                    'status' => $job['status'],
                    'final_progress' => intval($job['progress'])
                ]);
                break;
            }
            
            // 发送心跳
            if ($iteration % 30 == 0) {
                sendEvent('heartbeat', ['timestamp' => time()]);
            }
            
        } catch (Exception $e) {
            sendEvent('error', $e->getMessage());
            break;
        }
        
        flush();
        sleep(1);
        $iteration++;
    }
    
    sendEvent('timeout', 'Stream timeout');
}

/**
 * 流式传输系统状态
 */
function streamSystemStatus() {
    $maxIterations = 1200; // 20分钟
    $iteration = 0;
    
    while ($iteration < $maxIterations) {
        try {
            // 获取系统信息
            $loadAvg = sys_getloadavg();
            $totalSpace = disk_total_space(ROOT_PATH);
            $freeSpace = disk_free_space(ROOT_PATH);
            $diskUsage = (($totalSpace - $freeSpace) / $totalSpace) * 100;
            
            $data = [
                'load_average' => $loadAvg,
                'disk_usage' => round($diskUsage, 2),
                'memory_usage' => memory_get_usage(true),
                'timestamp' => time()
            ];
            
            sendEvent('system_status', $data);
            
        } catch (Exception $e) {
            sendEvent('error', $e->getMessage());
            break;
        }
        
        flush();
        sleep(30); // 每30秒更新一次
        $iteration++;
    }
}

/**
 * 流式传输队列状态
 */
function streamQueueStatus() {
    $maxIterations = 1200; // 20分钟
    $iteration = 0;
    
    while ($iteration < $maxIterations) {
        try {
            $queueManager = new QueueManager();
            $status = $queueManager->getQueueStatus();
            $status['is_paused'] = $queueManager->isQueuePaused();
            $status['timestamp'] = time();
            
            sendEvent('queue_status', $status);
            
        } catch (Exception $e) {
            sendEvent('error', $e->getMessage());
            break;
        }
        
        flush();
        sleep(10); // 每10秒更新一次
        $iteration++;
    }
}

/**
 * 发送SSE事件
 */
function sendEvent($event, $data) {
    echo "event: $event\n";
    echo "data: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

/**
 * 检查客户端连接
 */
function checkConnection() {
    if (connection_aborted()) {
        exit;
    }
}

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    sendEvent('error', "PHP Error: $message in $file on line $line");
    exit;
});

// 设置异常处理
set_exception_handler(function($exception) {
    sendEvent('error', "Exception: " . $exception->getMessage());
    exit;
});

// 忽略用户中断
ignore_user_abort(false);

// 定期检查连接
register_tick_function('checkConnection');
declare(ticks=1);
