WordPress 部署管理系统 - 错误调试报告
生成时间: Sat Jul 26 06:47:14 AM CDT 2025
项目目录: /www/wwwroot/wpd.cloudcheckout.shop

================================================
1. 系统环境信息
================================================
操作系统: Linux amdtet 5.15.0-122-generic #132-Ubuntu SMP Thu Aug 29 13:45:52 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
PHP版本: PHP 8.1.27 (cli) (built: Mar  8 2024 06:23:21) (NTS)
MySQL版本: mysql  Ver 8.0.35 for Linux on x86_64 (Source distribution)
Nginx版本: nginx version: nginx/1.24.0
磁盘空间: /dev/nvme0n1p2  3.6T  1.3T  2.2T  37% /
内存使用: Mem:            93Gi        11Gi        21Gi       2.0Mi        59Gi        80Gi
负载平均:  06:47:14 up 201 days, 17:31,  1 user,  load average: 0.02, 0.06, 0.01

================================================
2. 文件权限检查
================================================
项目目录权限:
total 120
drwxr-xr-x  12 <USER>  <GROUP>   4096 Jul 26 06:47 .
drwxr-xr-x 144 <USER>  <GROUP>  20480 Jul 26 05:50 ..
drwxr-xr-x   2 <USER>  <GROUP>   4096 Jul 26 06:45 api
-rwxr-xr-x   1 <USER>  <GROUP>   4094 Jul 26 06:45 cleanup_install.sh
drwxr-xr-x   2 <USER>  <GROUP>   4096 Jul 26 06:45 config
drwxr-xr-x   2 <USER>  <GROUP>   4096 Jul 26 06:45 core
drwxr-xr-x   2 <USER>  <GROUP>   4096 Jul 26 06:45 database
-rwxr-xr-x   1 <USER>  <GROUP>   9524 Jul 26 06:45 debug_errors.sh
-rwxr-xr-x   1 <USER>  <GROUP>   8293 Jul 26 06:45 DEPLOYMENT_GUIDE.md
-rw-r--r--   1 <USER> <GROUP>   942 Jul 26 06:47 error_report_20250726_064714.txt
drwxr-xr-x   3 <USER>  <GROUP>   4096 Jul 26 06:45 install
-rwxr-xr-x   1 <USER>  <GROUP>   6590 Jul 26 06:45 install_fixed.sh
drwxr-xr-x   2 <USER> <GROUP>  4096 Jul 26 06:18 logs
drwxr-xr-x   5 <USER>  <GROUP>   4096 Jul 26 06:46 public
-rwxr-xr-x   1 <USER>  <GROUP>   6878 Jul 26 06:45 quick_install.sh
drwxr-xr-x   2 <USER>  <GROUP>   4096 Jul 26 06:45 scripts
-rwxr-xr-x   1 <USER>  <GROUP>   4900 Jul 26 06:45 simple_install.sh
drwxr-xr-x   4 <USER> <GROUP>  4096 Jul 26 06:18 templates
drwxr-xr-x   3 <USER> <GROUP>  4096 Jul 26 06:18 uploads

关键目录权限:
config/: drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 26 06:45 config
api/: drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 26 06:45 api
core/: drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 26 06:45 core
logs/: drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 26 06:18 logs
uploads/: drwxr-xr-x 3 <USER> <GROUP> 4096 Jul 26 06:18 uploads
templates/: drwxr-xr-x 4 <USER> <GROUP> 4096 Jul 26 06:18 templates

================================================
3. 配置文件检查
================================================
✓ config/config.php 存在
✓ config/database.php 存在
✓ config/app.json 存在
app.json 内容:
{
    "mysql_root_password": "2KHNS4XSsMBLZJ7B",
    "max_concurrent_jobs": 3,
    "default_timeout": 1800,
    "enable_notifications": false,
    "notification_email": "",
    "backup_before_deploy": true,
    "auto_ssl_setup": true,
    "domain": "wpd.cloudcheckout.shop"
}

================================================
4. 数据库连接检查
================================================
数据库名: wp_deploy_manager
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
数据库用户: wp_deploy
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
✗ 数据库连接失败

================================================
5. PHP错误日志
================================================
未找到PHP错误日志文件

PHP配置:
display_errors => Off => Off
error_log => no value => no value
error_reporting => 22527 => 22527
log_errors => On => On

================================================
6. Nginx错误日志
================================================
项目Nginx错误日志不存在

================================================
7. 系统日志
================================================
系统日志文件:
total 8
drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 26 06:18 .
drwxr-xr-x 12 <USER>  <GROUP>  4096 Jul 26 06:47 ..

无系统日志文件

================================================
8. API接口检查
================================================
API文件检查:
✓ api/deploy.php 存在
✓ api/templates.php 存在
✓ api/status.php 存在
✓ api/logs.php 存在
✓ api/settings.php 存在
✓ api/sse.php 存在

public/api/ 目录检查:
total 12
drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 26 06:46 .
drwxr-xr-x 5 <USER> <GROUP> 4096 Jul 26 06:46 ..
-rwxr-xr-x 1 <USER> <GROUP> 2939 Jul 26 06:46 index.php

API接口测试:
测试 http://wpd.cloudcheckout.shop/api/
HTTP状态码: 301
✗ API基础接口异常

================================================
9. 问题总结和建议
================================================
常见问题检查:

PHP扩展检查:
✓ pdo_mysql 扩展已安装
✓ json 扩展已安装
✓ curl 扩展已安装
✓ zip 扩展已安装

建议的解决步骤:
1. 检查文件权限: chown -R www:www /www/wwwroot/wpd.cloudcheckout.shop
2. 检查PHP扩展是否完整安装
3. 检查数据库连接配置
4. 查看具体错误日志定位问题
5. 确保Nginx配置正确

报告生成完成: /www/wwwroot/wpd.cloudcheckout.shop/error_report_20250726_064714.txt
