<?php
/**
 * API路由入口文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义常量
define('WP_DEPLOY_MANAGER', true);

// 包含配置文件
$configPath = dirname(__DIR__, 2) . '/config/config.php';
if (!file_exists($configPath)) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Configuration file not found'
    ]);
    exit;
}

require_once $configPath;

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];

// 移除脚本名称部分，获取路径信息
$pathInfo = str_replace(dirname($scriptName), '', $requestUri);
$pathInfo = trim($pathInfo, '/');

// 解析路径
$pathParts = explode('/', $pathInfo);
$endpoint = $pathParts[1] ?? '';

// 移除查询字符串
$endpoint = strtok($endpoint, '?');

// 路由映射
$routes = [
    'deploy.php' => '../../api/deploy.php',
    'templates.php' => '../../api/templates.php',
    'status.php' => '../../api/status.php',
    'logs.php' => '../../api/logs.php',
    'settings.php' => '../../api/settings.php',
    'sse.php' => '../../api/sse.php'
];

// 检查路由
if (isset($routes[$endpoint])) {
    $targetFile = __DIR__ . '/' . $routes[$endpoint];
    
    if (file_exists($targetFile)) {
        // 设置环境变量，让目标文件知道是通过路由访问的
        $_SERVER['ROUTED_REQUEST'] = true;
        
        // 包含目标文件
        require $targetFile;
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint file not found: ' . $endpoint
        ]);
    }
} else {
    // 默认API信息
    if (empty($endpoint) || $endpoint === 'index.php') {
        echo json_encode([
            'success' => true,
            'message' => 'WordPress Deploy Manager API',
            'version' => '1.0.0',
            'endpoints' => [
                'deploy' => '/api/deploy.php',
                'templates' => '/api/templates.php',
                'status' => '/api/status.php',
                'logs' => '/api/logs.php',
                'settings' => '/api/settings.php',
                'sse' => '/api/sse.php'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'API endpoint not found: ' . $endpoint,
            'available_endpoints' => array_keys($routes)
        ]);
    }
}
?>
