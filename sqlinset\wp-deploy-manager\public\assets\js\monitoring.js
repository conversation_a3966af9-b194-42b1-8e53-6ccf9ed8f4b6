/**
 * 监控中心模块
 */

class MonitoringManager {
    constructor() {
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.loadSystemResources();
        this.loadSitesHealth();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        const healthCheckBtn = document.querySelector('[onclick="runHealthCheck()"]');
        if (healthCheckBtn) {
            healthCheckBtn.onclick = () => this.runHealthCheck();
        }

        const exportBtn = document.querySelector('[onclick="exportMonitoringReport()"]');
        if (exportBtn) {
            exportBtn.onclick = () => this.exportReport();
        }
    }

    async loadSystemResources() {
        try {
            const response = await api.getSystemStatus();
            const data = response.data;
            
            this.renderSystemResources(data);
        } catch (error) {
            console.error('Failed to load system resources:', error);
            app.showNotification('加载系统资源失败', 'error');
        }
    }

    renderSystemResources(data) {
        const container = document.getElementById('system-resources');
        if (!container) return;

        const html = `
            <div class="resource-grid">
                <div class="resource-item">
                    <div class="resource-icon">💻</div>
                    <div class="resource-info">
                        <h4>CPU负载</h4>
                        <div class="resource-value">${data.load_average?.[0]?.toFixed(2) || '0.00'}</div>
                        <div class="resource-bar">
                            <div class="resource-fill" style="width: ${Math.min(100, (data.load_average?.[0] || 0) * 25)}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-item">
                    <div class="resource-icon">💾</div>
                    <div class="resource-info">
                        <h4>内存使用</h4>
                        <div class="resource-value">${app.formatFileSize(data.memory_usage || 0)}</div>
                        <div class="resource-bar">
                            <div class="resource-fill" style="width: ${data.memory_percentage || 0}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-item">
                    <div class="resource-icon">💿</div>
                    <div class="resource-info">
                        <h4>磁盘使用</h4>
                        <div class="resource-value">${data.disk_usage?.toFixed(1) || '0.0'}%</div>
                        <div class="resource-bar">
                            <div class="resource-fill" style="width: ${data.disk_usage || 0}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-item">
                    <div class="resource-icon">🌐</div>
                    <div class="resource-info">
                        <h4>网络连接</h4>
                        <div class="resource-value">${data.network_connections || 0}</div>
                        <div class="resource-status status-${data.network_status || 'unknown'}">
                            ${data.network_status || 'Unknown'}
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    async loadSitesHealth() {
        try {
            const response = await api.getSitesHealth();
            const data = response.data;
            
            this.renderSitesHealth(data);
        } catch (error) {
            console.error('Failed to load sites health:', error);
            app.showNotification('加载站点健康状态失败', 'error');
        }
    }

    renderSitesHealth(data) {
        const container = document.getElementById('sites-health');
        if (!container) return;

        const overview = data.overview || {};
        const sites = data.sites || [];

        const html = `
            <div class="health-overview">
                <div class="health-stats">
                    <div class="health-stat">
                        <div class="stat-number">${overview.total_sites || 0}</div>
                        <div class="stat-label">总站点</div>
                    </div>
                    <div class="health-stat healthy">
                        <div class="stat-number">${overview.healthy_sites || 0}</div>
                        <div class="stat-label">健康</div>
                    </div>
                    <div class="health-stat warning">
                        <div class="stat-number">${overview.warning_sites || 0}</div>
                        <div class="stat-label">警告</div>
                    </div>
                    <div class="health-stat critical">
                        <div class="stat-number">${overview.critical_sites || 0}</div>
                        <div class="stat-label">严重</div>
                    </div>
                </div>
                
                <div class="health-score">
                    <div class="score-circle">
                        <div class="score-value">${overview.avg_health_score || 0}</div>
                        <div class="score-label">平均健康分数</div>
                    </div>
                </div>
            </div>
            
            <div class="sites-list">
                <h4>站点详情</h4>
                ${sites.length > 0 ? this.renderSitesList(sites) : '<p class="no-sites">暂无站点数据</p>'}
            </div>
        `;

        container.innerHTML = html;
    }

    renderSitesList(sites) {
        return `
            <div class="sites-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>域名</th>
                            <th>状态</th>
                            <th>健康分数</th>
                            <th>响应时间</th>
                            <th>最后检查</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sites.map(site => `
                            <tr>
                                <td>
                                    <a href="http://${site.domain}" target="_blank">${site.domain}</a>
                                </td>
                                <td>
                                    <span class="status-badge status-${site.status}">
                                        ${this.getHealthStatusText(site.status)}
                                    </span>
                                </td>
                                <td>
                                    <div class="health-score-mini">
                                        <span class="score">${site.health_score || 0}</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: ${site.health_score || 0}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>${site.response_time ? site.response_time + 's' : '-'}</td>
                                <td>${app.formatRelativeTime(site.last_check_at)}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline" onclick="monitoringManager.checkSiteHealth('${site.domain}')">
                                        <i class="icon-refresh"></i> 检查
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    getHealthStatusText(status) {
        const texts = {
            'healthy': '健康',
            'warning': '警告',
            'critical': '严重',
            'unknown': '未知'
        };
        return texts[status] || status;
    }

    async runHealthCheck() {
        try {
            app.showNotification('开始健康检查...', 'info');
            
            const response = await api.runHealthCheck();
            
            app.showNotification('健康检查完成', 'success');
            this.loadSitesHealth();
            
        } catch (error) {
            console.error('Health check failed:', error);
            app.showNotification('健康检查失败', 'error');
        }
    }

    async checkSiteHealth(domain) {
        try {
            app.showNotification(`检查 ${domain} 健康状态...`, 'info');
            
            const response = await api.runHealthCheck(domain);
            
            app.showNotification(`${domain} 健康检查完成`, 'success');
            this.loadSitesHealth();
            
        } catch (error) {
            console.error('Site health check failed:', error);
            app.showNotification('站点健康检查失败', 'error');
        }
    }

    exportReport() {
        app.showNotification('导出监控报告功能开发中', 'info');
    }

    startAutoRefresh() {
        // 每30秒刷新一次系统资源
        this.refreshInterval = setInterval(() => {
            if (!document.hidden) {
                this.loadSystemResources();
            }
        }, 30000);

        // 每5分钟刷新一次站点健康状态
        setInterval(() => {
            if (!document.hidden) {
                this.loadSitesHealth();
            }
        }, 300000);
    }

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// 全局函数
function runHealthCheck() {
    if (window.monitoringManager) {
        window.monitoringManager.runHealthCheck();
    }
}

function exportMonitoringReport() {
    if (window.monitoringManager) {
        window.monitoringManager.exportReport();
    }
}

// 创建全局实例
window.monitoringManager = new MonitoringManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MonitoringManager;
}
