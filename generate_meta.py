import pandas as pd
import random
import re
import csv

# ===================== 配置区 =====================
INPUT_FILE = "productos.csv"          
OUTPUT_FILE = "productos_con_meta.csv"
MAX_DESC_LENGTH = 155  # 预留5字符给省略号
KEEP_ORIGINAL_TITLE = False

# ===================== 模板池 =====================
PROMO_PHRASES = [
    "¡Oferta limitada!", "¡Últimas unidades!", "¡Descuento EXCLUSIVO!",
    "Precio más bajo garantizado", "¡Envío GRATIS hoy!"
]

LOGISTICS_PHRASES = [
    "Envío en 24-48h", "Recogida en tienda", 
    "Entrega con seguimiento", "Embalaje reforzado"
]

TRUST_PHRASES = [
    "Garantía 3 años", "Certificado OEKO-TEX",
    "★★★★★ (4.9/5)", "Fabricación europea"
]

CTA_PHRASES = [
    "¡Compra segura!", "¡No dejes pasar esta oportunidad!",
    "¡Personaliza tu espacio ahora!", "¡Hazte con el tuyo!"
]

MATERIAL_FEATURES = [
    "Material premium", "Diseño ergonómico",
    "Resistente a UV/agua", "Estructura reforzada",
    "Mantenimiento fácil"
]

# ===================== 智能函数 =====================
def smart_truncate(text, max_len=MAX_DESC_LENGTH):
    """优先在标点或空格处截断的智能处理"""
    if len(text) <= max_len:
        return text
    
    # 第一优先级：标点截断 (., !, ;, ✔️)
    for punc in ['.', '!', ';', '✔️']:
        last_pos = text.rfind(punc, 0, max_len)
        if last_pos != -1:
            return text[:last_pos+1].strip() + ('...' if last_pos+3 < len(text) else '')
    
    # 第二优先级：空格截断
    last_space = text.rfind(' ', 0, max_len)
    if last_space > int(max_len*0.8):  # 确保不丢失太多信息
        return text[:last_space].strip() + '...'
    
    # 第三优先级：保留完整单词
    truncated = text[:max_len]
    if truncated[-1].isalnum():
        return truncated.rsplit(' ', 1)[0] + '...'  # 舍弃不完整单词
    
    return truncated + '...'

def clean_title(raw_title):
    """清洗特殊符号"""
    cleaned = re.sub(
        r'[^\wáéíóúñÁÉÍÓÚÑ°%|()+\-/\s]', 
        '', 
        str(raw_title)
    )
    cleaned = cleaned.replace('/', ' ').replace(',', ' ')
    return re.sub(r'\s+', ' ', cleaned).strip()

def generate_meta_desc(clean_title):
    """生成描述并智能截断"""
    template = random.choice([
        f"{clean_title}. ✔️ {random.choice(MATERIAL_FEATURES)} + {random.choice(LOGISTICS_PHRASES)}. {random.choice(PROMO_PHRASES)} {random.choice(CTA_PHRASES)}",
        f"¿Buscas {clean_title.split('|')[0].lower()}? ✔️ {random.choice(TRUST_PHRASES)}. {random.choice(PROMO_PHRASES)} {random.choice(CTA_PHRASES)}",
        f"✨ Ideal para {random.choice(['salones', 'dormitorios', 'oficinas'])}: {clean_title}. ✔️ {random.choice(MATERIAL_FEATURES)} + {random.choice(LOGISTICS_PHRASES)}. {random.choice(CTA_PHRASES)}",
        f"{clean_title}. Especificaciones: {random.choice(['Altura ajustable', 'Consumo A++'])}. {random.choice(PROMO_PHRASES)} {random.choice(CTA_PHRASES)}"
    ])
    return smart_truncate(re.sub(r'\s+', ' ', template))

# ===================== 主程序 =====================
if __name__ == "__main__":
    print("▶▶ 开始处理...")
    try:
        df = pd.read_csv(INPUT_FILE, encoding='utf-8')
        if 'title' not in df.columns:
            raise ValueError("❌ 输入文件必须包含'title'列")

        print("⏳ 清洗标题...")
        df['cleaned_title'] = df['title'].apply(clean_title)
        
        print("⏳ 生成元描述...")
        df['meta_description'] = df['cleaned_title'].apply(generate_meta_desc)
        
        if not KEEP_ORIGINAL_TITLE:
            df = df.rename(columns={'cleaned_title': 'title'}).drop(columns=['title'], errors='ignore')
        
        df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig', quoting=csv.QUOTE_MINIMAL)
        print(f"✅ 成功处理 {len(df)} 条数据\n💾 输出文件: {OUTPUT_FILE}")

    except Exception as e:
        print(f"❌ 错误: {str(e)}")
    print("▶▶ 处理完成")